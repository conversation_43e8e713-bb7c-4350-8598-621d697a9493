package com.sirch.mileagetracker.presentation.theme;

import com.sirch.mileagetracker.data.repository.AdsRepository;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ThemeManager_Factory implements Factory<ThemeManager> {
  private final Provider<SettingsRepository> settingsRepositoryProvider;

  private final Provider<AdsRepository> adsRepositoryProvider;

  public ThemeManager_Factory(Provider<SettingsRepository> settingsRepositoryProvider,
      Provider<AdsRepository> adsRepositoryProvider) {
    this.settingsRepositoryProvider = settingsRepositoryProvider;
    this.adsRepositoryProvider = adsRepositoryProvider;
  }

  @Override
  public ThemeManager get() {
    return newInstance(settingsRepositoryProvider.get(), adsRepositoryProvider.get());
  }

  public static ThemeManager_Factory create(Provider<SettingsRepository> settingsRepositoryProvider,
      Provider<AdsRepository> adsRepositoryProvider) {
    return new ThemeManager_Factory(settingsRepositoryProvider, adsRepositoryProvider);
  }

  public static ThemeManager newInstance(SettingsRepository settingsRepository,
      AdsRepository adsRepository) {
    return new ThemeManager(settingsRepository, adsRepository);
  }
}
