package com.sirch.mileagetracker.di

import android.content.Context
import androidx.room.Room
import com.sirch.mileagetracker.data.local.MileageTrackerDatabase
import com.sirch.mileagetracker.data.local.dao.ProgramDao
import com.sirch.mileagetracker.data.local.dao.PurchaseDao
import com.sirch.mileagetracker.data.local.dao.SettingsDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): MileageTrackerDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            MileageTrackerDatabase::class.java,
            MileageTrackerDatabase.DATABASE_NAME
        ).build()
    }
    
    @Provides
    fun provideProgramDao(database: MileageTrackerDatabase): ProgramDao {
        return database.programDao()
    }
    
    @Provides
    fun providePurchaseDao(database: MileageTrackerDatabase): PurchaseDao {
        return database.purchaseDao()
    }
    
    @Provides
    fun provideSettingsDao(database: MileageTrackerDatabase): SettingsDao {
        return database.settingsDao()
    }
}
