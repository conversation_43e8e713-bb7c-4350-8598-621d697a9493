package com.sirch.mileagetracker.di

import android.content.Context
import androidx.room.Room
import com.sirch.mileagetracker.data.local.MileageTrackerDatabase
import com.sirch.mileagetracker.data.local.dao.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): MileageTrackerDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            MileageTrackerDatabase::class.java,
            MileageTrackerDatabase.DATABASE_NAME
        ).build()
    }

    @Provides
    fun provideProgramDao(database: MileageTrackerDatabase): ProgramDao {
        return database.programDao()
    }

    @Provides
    fun providePurchaseDao(database: MileageTrackerDatabase): PurchaseDao {
        return database.purchaseDao()
    }

    @Provides
    fun provideSettingsDao(database: MileageTrackerDatabase): SettingsDao {
        return database.settingsDao()
    }

    @Provides
    fun provideEfficiencyScoreDao(database: MileageTrackerDatabase): EfficiencyScoreDao {
        return database.efficiencyScoreDao()
    }

    @Provides
    fun provideAggregatedDataDao(database: MileageTrackerDatabase): AggregatedDataDao {
        return database.aggregatedDataDao()
    }

    @Provides
    fun provideGoalDao(database: MileageTrackerDatabase): GoalDao {
        return database.goalDao()
    }

    @Provides
    fun provideNotificationConfigDao(database: MileageTrackerDatabase): NotificationConfigDao {
        return database.notificationConfigDao()
    }

    @Provides
    fun provideBackupHistoryDao(database: MileageTrackerDatabase): BackupHistoryDao {
        return database.backupHistoryDao()
    }

    @Provides
    fun provideReportTemplateDao(database: MileageTrackerDatabase): ReportTemplateDao {
        return database.reportTemplateDao()
    }
}
