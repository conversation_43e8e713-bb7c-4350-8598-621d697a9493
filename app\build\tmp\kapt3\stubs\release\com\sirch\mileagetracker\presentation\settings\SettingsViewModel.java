package com.sirch.mileagetracker.presentation.settings;

import android.app.Activity;
import androidx.lifecycle.ViewModel;
import com.android.billingclient.api.BillingClient;
import com.sirch.mileagetracker.data.auth.AuthRepository;
import com.sirch.mileagetracker.data.billing.BillingManager;
import com.sirch.mileagetracker.data.cloud.CloudBackupService;
import com.sirch.mileagetracker.data.repository.AdsRepository;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import dagger.hilt.android.lifecycle.HiltViewModel;
import kotlinx.coroutines.flow.StateFlow;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B/\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\u0006\u0010\u0014\u001a\u00020\u0015J\u0006\u0010\u0016\u001a\u00020\u0015J\b\u0010\u0017\u001a\u00020\u0015H\u0002J\b\u0010\u0018\u001a\u00020\u0015H\u0002J\b\u0010\u0019\u001a\u00020\u0015H\u0002J\b\u0010\u001a\u001a\u00020\u0015H\u0002J\b\u0010\u001b\u001a\u00020\u0015H\u0002J\b\u0010\u001c\u001a\u00020\u0015H\u0002J\u000e\u0010\u001d\u001a\u00020\u00152\u0006\u0010\u001e\u001a\u00020\u001fJ\u0006\u0010 \u001a\u00020\u0015J\u0006\u0010!\u001a\u00020\u0015J\u0006\u0010\"\u001a\u00020\u0015R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013\u00a8\u0006#"}, d2 = {"Lcom/sirch/mileagetracker/presentation/settings/SettingsViewModel;", "Landroidx/lifecycle/ViewModel;", "authRepository", "Lcom/sirch/mileagetracker/data/auth/AuthRepository;", "adsRepository", "Lcom/sirch/mileagetracker/data/repository/AdsRepository;", "billingManager", "Lcom/sirch/mileagetracker/data/billing/BillingManager;", "settingsRepository", "Lcom/sirch/mileagetracker/data/repository/SettingsRepository;", "cloudBackupService", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService;", "(Lcom/sirch/mileagetracker/data/auth/AuthRepository;Lcom/sirch/mileagetracker/data/repository/AdsRepository;Lcom/sirch/mileagetracker/data/billing/BillingManager;Lcom/sirch/mileagetracker/data/repository/SettingsRepository;Lcom/sirch/mileagetracker/data/cloud/CloudBackupService;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/sirch/mileagetracker/presentation/settings/SettingsUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "backupToCloud", "", "clearError", "clearSuccessMessage", "loadPremiumStatus", "loadRemoveAdsPrice", "loadSettings", "loadUserInfo", "observeBillingEvents", "purchaseRemoveAds", "activity", "Landroid/app/Activity;", "restoreFromCloud", "signOut", "toggleDarkMode", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class SettingsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.auth.AuthRepository authRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.AdsRepository adsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.billing.BillingManager billingManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.cloud.CloudBackupService cloudBackupService = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.sirch.mileagetracker.presentation.settings.SettingsUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.sirch.mileagetracker.presentation.settings.SettingsUiState> uiState = null;
    
    @javax.inject.Inject()
    public SettingsViewModel(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.auth.AuthRepository authRepository, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.AdsRepository adsRepository, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.billing.BillingManager billingManager, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.cloud.CloudBackupService cloudBackupService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.sirch.mileagetracker.presentation.settings.SettingsUiState> getUiState() {
        return null;
    }
    
    private final void loadUserInfo() {
    }
    
    private final void loadPremiumStatus() {
    }
    
    private final void loadRemoveAdsPrice() {
    }
    
    private final void observeBillingEvents() {
    }
    
    public final void purchaseRemoveAds(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    public final void signOut() {
    }
    
    public final void clearError() {
    }
    
    private final void clearSuccessMessage() {
    }
    
    private final void loadSettings() {
    }
    
    public final void toggleDarkMode() {
    }
    
    public final void backupToCloud() {
    }
    
    public final void restoreFromCloud() {
    }
}