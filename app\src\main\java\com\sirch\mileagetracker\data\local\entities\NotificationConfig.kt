package com.sirch.mileagetracker.data.local.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.Index
import kotlinx.datetime.LocalDateTime

/**
 * Entity for managing user notification preferences and configurations.
 * Supports various notification types with customizable frequency and timing.
 *
 * @property id Unique identifier for the notification configuration
 * @property userId Firebase user ID
 * @property type Type of notification (REMINDER, GOAL_UPDATE, EFFICIENCY_ALERT, etc.)
 * @property enabled Whether this notification type is enabled
 * @property frequency How often to send notifications (DAILY, WEEKLY, MONTHLY)
 * @property lastSent Timestamp of last notification sent
 * @property nextScheduled Timestamp of next scheduled notification
 * @property timeOfDay Preferred time of day for notifications (24-hour format)
 * @property daysOfWeek Days of week for weekly notifications (bitmask)
 * @property isPremium Whether this is a premium notification feature
 * @property customMessage Custom message template for notifications
 * @property isQuietHoursEnabled Whether to respect quiet hours
 * @property quietHoursStart Start of quiet hours (24-hour format)
 * @property quietHoursEnd End of quiet hours (24-hour format)
 */
@Entity(
    tableName = "notification_configs",
    indices = [
        Index(value = ["userId"]),
        Index(value = ["type"]),
        Index(value = ["userId", "type"], unique = true),
        Index(value = ["enabled"]),
        Index(value = ["nextScheduled"]),
        Index(value = ["lastSent"])
    ]
)
data class NotificationConfig(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,

    val userId: String,

    val type: NotificationType,

    val enabled: Boolean = true,

    val frequency: NotificationFrequency = NotificationFrequency.WEEKLY,

    val lastSent: LocalDateTime? = null,

    val nextScheduled: LocalDateTime? = null,

    val timeOfDay: Int = 18, // 6 PM default

    val daysOfWeek: Int = 0b0100000, // Sunday default (bit 0 = Sunday)

    val isPremium: Boolean = false,

    val customMessage: String? = null,

    val isQuietHoursEnabled: Boolean = true,

    val quietHoursStart: Int = 22, // 10 PM

    val quietHoursEnd: Int = 8 // 8 AM
) {
    /**
     * Check if notifications should be sent at the current time
     */
    fun shouldSendNow(): Boolean {
        // Simplified implementation - will be enhanced later
        return enabled
    }

    /**
     * Check if current hour is within quiet hours
     */
    private fun isInQuietHours(currentHour: Int): Boolean {
        return if (quietHoursStart <= quietHoursEnd) {
            currentHour >= quietHoursStart && currentHour < quietHoursEnd
        } else {
            // Quiet hours span midnight
            currentHour >= quietHoursStart || currentHour < quietHoursEnd
        }
    }

    /**
     * Check if today is a valid day for weekly notifications
     */
    fun isValidDayOfWeek(): Boolean {
        // Simplified implementation - will be enhanced later
        return true
    }

    /**
     * Calculate next scheduled notification time
     */
    fun calculateNextScheduled(): LocalDateTime {
        // Simplified implementation - will be enhanced later
        return kotlinx.datetime.LocalDateTime(2024, 12, 19, timeOfDay, 0)
    }

    /**
     * Get user-friendly description of notification schedule
     */
    fun getScheduleDescription(): String {
        if (!enabled) return "Disabled"

        return when (frequency) {
            NotificationFrequency.DAILY -> "Daily at ${timeOfDay}:00"
            NotificationFrequency.WEEKLY -> {
                val days = getDaysOfWeekNames()
                "Weekly on ${days.joinToString(", ")} at ${timeOfDay}:00"
            }
            NotificationFrequency.MONTHLY -> "Monthly at ${timeOfDay}:00"
            NotificationFrequency.NEVER -> "Never"
        }
    }

    /**
     * Get list of day names for weekly notifications
     */
    private fun getDaysOfWeekNames(): List<String> {
        val dayNames = listOf("Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat")
        return dayNames.filterIndexed { index, _ ->
            (daysOfWeek and (1 shl index)) != 0
        }
    }
}

/**
 * Types of notifications supported by the application
 */
enum class NotificationType {
    PURCHASE_REMINDER,      // Remind to log purchases
    GOAL_UPDATE,           // Goal progress updates
    EFFICIENCY_ALERT,      // Efficiency score changes
    BACKUP_REMINDER,       // Backup reminders
    PROMOTION_ALERT,       // Program promotions (premium)
    MILESTONE_CELEBRATION, // Achievement celebrations
    WEEKLY_SUMMARY,        // Weekly activity summary
    MONTHLY_REPORT         // Monthly report (premium)
}

/**
 * Frequency options for notifications
 */
enum class NotificationFrequency {
    DAILY,
    WEEKLY,
    MONTHLY,
    NEVER
}
