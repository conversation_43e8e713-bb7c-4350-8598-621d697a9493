package com.sirch.mileagetracker.data.local.converters

import androidx.room.TypeConverter
import com.sirch.mileagetracker.data.local.entities.*

/**
 * Room TypeConverters for enum types used in v0.2.0 entities.
 * Provides conversion between enum values and their string representations for database storage.
 */
class EnumConverters {
    
    // GoalType converters
    @TypeConverter
    fun fromGoalType(goalType: GoalType): String {
        return goalType.name
    }
    
    @TypeConverter
    fun toGoalType(goalType: String): GoalType {
        return try {
            GoalType.valueOf(goalType)
        } catch (e: IllegalArgumentException) {
            GoalType.MILES // Default fallback
        }
    }
    
    // NotificationType converters
    @TypeConverter
    fun fromNotificationType(notificationType: NotificationType): String {
        return notificationType.name
    }
    
    @TypeConverter
    fun toNotificationType(notificationType: String): NotificationType {
        return try {
            NotificationType.valueOf(notificationType)
        } catch (e: IllegalArgumentException) {
            NotificationType.PURCHASE_REMINDER // Default fallback
        }
    }
    
    // NotificationFrequency converters
    @TypeConverter
    fun fromNotificationFrequency(frequency: NotificationFrequency): String {
        return frequency.name
    }
    
    @TypeConverter
    fun toNotificationFrequency(frequency: String): NotificationFrequency {
        return try {
            NotificationFrequency.valueOf(frequency)
        } catch (e: IllegalArgumentException) {
            NotificationFrequency.WEEKLY // Default fallback
        }
    }
    
    // BackupStatus converters
    @TypeConverter
    fun fromBackupStatus(status: BackupStatus): String {
        return status.name
    }
    
    @TypeConverter
    fun toBackupStatus(status: String): BackupStatus {
        return try {
            BackupStatus.valueOf(status)
        } catch (e: IllegalArgumentException) {
            BackupStatus.PENDING // Default fallback
        }
    }
    
    // BackupType converters
    @TypeConverter
    fun fromBackupType(backupType: BackupType): String {
        return backupType.name
    }
    
    @TypeConverter
    fun toBackupType(backupType: String): BackupType {
        return try {
            BackupType.valueOf(backupType)
        } catch (e: IllegalArgumentException) {
            BackupType.AUTOMATIC // Default fallback
        }
    }
    
    // ReportType converters
    @TypeConverter
    fun fromReportType(reportType: ReportType): String {
        return reportType.name
    }
    
    @TypeConverter
    fun toReportType(reportType: String): ReportType {
        return try {
            ReportType.valueOf(reportType)
        } catch (e: IllegalArgumentException) {
            ReportType.SUMMARY // Default fallback
        }
    }
    
    // OutputFormat converters
    @TypeConverter
    fun fromOutputFormat(outputFormat: OutputFormat): String {
        return outputFormat.name
    }
    
    @TypeConverter
    fun toOutputFormat(outputFormat: String): OutputFormat {
        return try {
            OutputFormat.valueOf(outputFormat)
        } catch (e: IllegalArgumentException) {
            OutputFormat.PDF // Default fallback
        }
    }
    
    // DateRangeType converters
    @TypeConverter
    fun fromDateRangeType(dateRangeType: DateRangeType): String {
        return dateRangeType.name
    }
    
    @TypeConverter
    fun toDateRangeType(dateRangeType: String): DateRangeType {
        return try {
            DateRangeType.valueOf(dateRangeType)
        } catch (e: IllegalArgumentException) {
            DateRangeType.LAST_MONTH // Default fallback
        }
    }
}
