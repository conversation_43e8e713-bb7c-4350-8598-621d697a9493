#!/bin/bash

echo "========================================"
echo "   Mileage Tracker - Build Release"
echo "========================================"
echo

echo "[1/5] Limpando projeto..."
./gradlew clean
if [ $? -ne 0 ]; then
    echo "ERRO: Falha na limpeza do projeto"
    exit 1
fi

echo
echo "[2/5] Verificando configurações..."
if [ ! -f "keystore.properties" ]; then
    echo "AVISO: keystore.properties não encontrado"
    echo "Copie keystore.properties.example e configure suas credenciais"
    read -p "Pressione Enter para continuar..."
fi

echo
echo "[3/5] Compilando versão de release..."
./gradlew assembleRelease
if [ $? -ne 0 ]; then
    echo "ERRO: Falha na compilação"
    exit 1
fi

echo
echo "[4/5] Gerando Android App Bundle (AAB)..."
./gradlew bundleRelease
if [ $? -ne 0 ]; then
    echo "ERRO: Falha na geração do AAB"
    exit 1
fi

echo
echo "[5/5] Build concluído com sucesso!"
echo
echo "Arquivos gerados:"
echo "- APK: app/build/outputs/apk/release/app-release.apk"
echo "- AAB: app/build/outputs/bundle/release/app-release.aab"
echo
echo "Recomendação: Use o arquivo AAB para upload na Play Store"
echo
