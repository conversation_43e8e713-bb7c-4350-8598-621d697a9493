androidx.navigation.ActionOnlyNavDirections
androidx.navigation.AnimBuilder
androidx.navigation.FloatingWindow
androidx.navigation.NamedNavArgument
androidx.navigation.NamedNavArgumentKt
androidx.navigation.NavAction
androidx.navigation.NavActionBuilder
androidx.navigation.NavArgs
androidx.navigation.NavArgsLazy
androidx.navigation.NavArgsLazyKt
androidx.navigation.NavArgument
androidx.navigation.NavArgument$Builder
androidx.navigation.NavArgumentBuilder
androidx.navigation.NavArgumentKt
androidx.navigation.NavBackStackEntry
androidx.navigation.NavBackStackEntry$Companion
androidx.navigation.NavBackStackEntry$NavResultSavedStateFactory
androidx.navigation.NavBackStackEntry$SavedStateViewModel
androidx.navigation.NavBackStackEntry$defaultFactory$2
androidx.navigation.NavBackStackEntry$savedStateHandle$2
androidx.navigation.NavDeepLink
androidx.navigation.NavDeepLink$Builder
androidx.navigation.NavDeepLink$Builder$Companion
androidx.navigation.NavDeepLink$Companion
androidx.navigation.NavDeepLink$MimeType
androidx.navigation.NavDeepLink$ParamQuery
androidx.navigation.NavDeepLink$fragArgs$2
androidx.navigation.NavDeepLink$fragArgsAndRegex$2
androidx.navigation.NavDeepLink$fragPattern$2
androidx.navigation.NavDeepLink$fragRegex$2
androidx.navigation.NavDeepLink$getMatchingArguments$missingRequiredArguments$1
androidx.navigation.NavDeepLink$isParameterizedQuery$2
androidx.navigation.NavDeepLink$mimeTypePattern$2
androidx.navigation.NavDeepLink$pathPattern$2
androidx.navigation.NavDeepLink$queryArgsMap$2
androidx.navigation.NavDeepLinkDsl
androidx.navigation.NavDeepLinkDslBuilder
androidx.navigation.NavDeepLinkDslBuilderKt
androidx.navigation.NavDeepLinkRequest
androidx.navigation.NavDeepLinkRequest$Builder
androidx.navigation.NavDeepLinkRequest$Builder$Companion
androidx.navigation.NavDestination
androidx.navigation.NavDestination$ClassType
androidx.navigation.NavDestination$Companion
androidx.navigation.NavDestination$Companion$hierarchy$1
androidx.navigation.NavDestination$DeepLinkMatch
androidx.navigation.NavDestination$addDeepLink$missingRequiredArguments$1
androidx.navigation.NavDestination$hasRequiredArguments$missingRequiredArguments$1
androidx.navigation.NavDestinationBuilder
androidx.navigation.NavDestinationDsl
androidx.navigation.NavDirections
androidx.navigation.NavGraph
androidx.navigation.NavGraph$Companion
androidx.navigation.NavGraph$Companion$findStartDestination$1
androidx.navigation.NavGraph$iterator$1
androidx.navigation.NavGraphBuilder
androidx.navigation.NavGraphBuilderKt
androidx.navigation.NavGraphKt
androidx.navigation.NavGraphNavigator
androidx.navigation.NavOptions
androidx.navigation.NavOptions$Builder
androidx.navigation.NavOptionsBuilder
androidx.navigation.NavOptionsBuilder$popUpTo$1
androidx.navigation.NavOptionsBuilder$popUpTo$2
androidx.navigation.NavOptionsBuilderKt
androidx.navigation.NavOptionsDsl
androidx.navigation.NavType
androidx.navigation.NavType$Companion
androidx.navigation.NavType$Companion$BoolArrayType$1
androidx.navigation.NavType$Companion$BoolType$1
androidx.navigation.NavType$Companion$FloatArrayType$1
androidx.navigation.NavType$Companion$FloatType$1
androidx.navigation.NavType$Companion$IntArrayType$1
androidx.navigation.NavType$Companion$IntType$1
androidx.navigation.NavType$Companion$LongArrayType$1
androidx.navigation.NavType$Companion$LongType$1
androidx.navigation.NavType$Companion$ReferenceType$1
androidx.navigation.NavType$Companion$StringArrayType$1
androidx.navigation.NavType$Companion$StringType$1
androidx.navigation.NavType$EnumType
androidx.navigation.NavType$ParcelableArrayType
androidx.navigation.NavType$ParcelableType
androidx.navigation.NavType$SerializableArrayType
androidx.navigation.NavType$SerializableType
androidx.navigation.NavViewModelStoreProvider
androidx.navigation.Navigator
androidx.navigation.Navigator$Extras
androidx.navigation.Navigator$Name
androidx.navigation.Navigator$navigate$1
androidx.navigation.Navigator$onLaunchSingleTop$1
androidx.navigation.NavigatorProvider
androidx.navigation.NavigatorProvider$Companion
androidx.navigation.NavigatorProviderKt
androidx.navigation.NavigatorState
androidx.navigation.NoOpNavigator
androidx.navigation.PopUpToBuilder