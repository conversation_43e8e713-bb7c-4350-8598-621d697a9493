package com.sirch.mileagetracker.di;

import com.sirch.mileagetracker.data.local.dao.SettingsDao;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideSettingsRepositoryFactory implements Factory<SettingsRepository> {
  private final Provider<SettingsDao> settingsDaoProvider;

  public RepositoryModule_ProvideSettingsRepositoryFactory(
      Provider<SettingsDao> settingsDaoProvider) {
    this.settingsDaoProvider = settingsDaoProvider;
  }

  @Override
  public SettingsRepository get() {
    return provideSettingsRepository(settingsDaoProvider.get());
  }

  public static RepositoryModule_ProvideSettingsRepositoryFactory create(
      Provider<SettingsDao> settingsDaoProvider) {
    return new RepositoryModule_ProvideSettingsRepositoryFactory(settingsDaoProvider);
  }

  public static SettingsRepository provideSettingsRepository(SettingsDao settingsDao) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideSettingsRepository(settingsDao));
  }
}
