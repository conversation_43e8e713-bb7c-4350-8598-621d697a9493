{"logs": [{"outputFile": "com.sirch.mileagetracker.app-mergeReleaseResources-3:/values-ca/values-ca.xml", "map": [{"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\3793d766ef17a5c374cbb3773985b3be\\transformed\\play-services-base-18.5.0\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1252,1357,1509,1636,1745,1895,2022,2145,2388,2559,2668,2827,2958,3122,3280,3345,3413", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "1352,1504,1631,1740,1890,2017,2140,2248,2554,2663,2822,2953,3117,3275,3340,3408,3495"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\4f87b4fe7e5f13faf091c8d26d70e189\\transformed\\material3-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,414,534,634,732,847,989,1104,1263,1347,1445,1543,1644,1761,1890,1993,2134,2274,2415,2581,2714,2831,2952,3081,3180,3277,3398,3543,3649,3762,3876,4015,4160,4269,4376,4462,4563,4664,4750,4836,4947,5027,5111,5212,5320,5419,5523,5610,5723,5823,5930,6049,6129,6246", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "171,290,409,529,629,727,842,984,1099,1258,1342,1440,1538,1639,1756,1885,1988,2129,2269,2410,2576,2709,2826,2947,3076,3175,3272,3393,3538,3644,3757,3871,4010,4155,4264,4371,4457,4558,4659,4745,4831,4942,5022,5106,5207,5315,5414,5518,5605,5718,5818,5925,6044,6124,6241,6348"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4401,4522,4641,4760,4880,4980,5078,5193,5335,5450,5609,5693,5791,5889,5990,6107,6236,6339,6480,6620,6761,6927,7060,7177,7298,7427,7526,7623,7744,7889,7995,8108,8222,8361,8506,8615,8722,8808,8909,9010,9096,9182,9293,9373,9457,9558,9666,9765,9869,9956,10069,10169,10276,10395,10475,10592", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "4517,4636,4755,4875,4975,5073,5188,5330,5445,5604,5688,5786,5884,5985,6102,6231,6334,6475,6615,6756,6922,7055,7172,7293,7422,7521,7618,7739,7884,7990,8103,8217,8356,8501,8610,8717,8803,8904,9005,9091,9177,9288,9368,9452,9553,9661,9760,9864,9951,10064,10164,10271,10390,10470,10587,10694"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\fcd13fdc941282eaa2071fb31e6df1d3\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,390,493,582,660,751,842,928,1000,1069,1155,1246,1322,1404,1475", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "197,281,385,488,577,655,746,837,923,995,1064,1150,1241,1317,1399,1470,1590"}, "to": {"startLines": "11,12,32,33,34,38,39,99,100,104,113,114,115,123,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1071,1168,3613,3717,3820,4232,4310,10878,10969,11310,12171,12240,12326,12844,13021,13103,13174", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "1163,1247,3712,3815,3904,4305,4396,10964,11050,11377,12235,12321,12412,12915,13098,13169,13289"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\17af91994e13f583c0b6091f8c6fb16f\\transformed\\core-1.15.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "4,5,6,7,8,9,10,124", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "340,436,538,637,734,840,945,12920", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "431,533,632,729,835,940,1066,13016"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\e995885063661f84453093b90e093640\\transformed\\browser-1.8.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,108", "endOffsets": "163,266,377,486"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3500,3909,4012,4123", "endColumns": "112,102,110,108", "endOffsets": "3608,4007,4118,4227"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\10e14192bcf7bea6e0ef145c1ad40304\\transformed\\play-services-ads-23.5.0\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,239,285,366,430,499,609,672,805,926,1043,1099,1158,1276,1366,1408,1501,1536,1571,1632,1722,1765", "endColumns": "39,45,80,63,68,109,62,132,120,116,55,58,117,89,41,92,34,34,60,89,42,55", "endOffsets": "238,284,365,429,498,608,671,804,925,1042,1098,1157,1275,1365,1407,1500,1535,1570,1631,1721,1764,1820"}, "to": {"startLines": "96,97,98,101,102,103,105,106,107,108,109,110,111,112,116,117,118,119,120,121,122,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10699,10743,10793,11055,11123,11196,11382,11449,11586,11711,11832,11892,11955,12077,12417,12463,12560,12599,12638,12703,12797,13494", "endColumns": "43,49,84,67,72,113,66,136,124,120,59,62,121,93,45,96,38,38,64,93,46,59", "endOffsets": "10738,10788,10873,11118,11191,11305,11444,11581,11706,11827,11887,11950,12072,12166,12458,12555,12594,12633,12698,12792,12839,13549"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\ce7063c7507d7ba5446267220fbed412\\transformed\\credentials-1.2.0-rc01\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,119", "endOffsets": "165,285"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,220", "endColumns": "114,119", "endOffsets": "215,335"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\51f776a662999a2c4066cf59dc47f5c4\\transformed\\play-services-basement-18.4.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2253", "endColumns": "134", "endOffsets": "2383"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\831f7f0f93fd70afcef46ca0120dc927\\transformed\\foundation-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "128,129", "startColumns": "4,4", "startOffsets": "13294,13392", "endColumns": "97,101", "endOffsets": "13387,13489"}}]}]}