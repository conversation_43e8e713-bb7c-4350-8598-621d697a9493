package com.sirch.mileagetracker.data.repository

import com.sirch.mileagetracker.data.repository.SettingsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AdsRepository @Inject constructor(
    private val settingsRepository: SettingsRepository
) {

    /**
     * Returns true if ads should be shown (user hasn't purchased ad removal)
     */
    fun shouldShowAds(): Flow<Boolean> {
        return settingsRepository.getSettings().map { settings ->
            !(settings?.adsRemoved ?: false)
        }
    }

    /**
     * Returns true if ads are removed (user purchased ad removal)
     */
    fun areAdsRemoved(): Flow<Boolean> {
        return settingsRepository.getSettings().map { settings ->
            settings?.adsRemoved ?: false
        }
    }

    /**
     * Remove ads (called after successful purchase)
     */
    suspend fun removeAds() {
        settingsRepository.updateAdsRemoved(true)
    }

    /**
     * Restore ads (for testing or refund scenarios)
     */
    suspend fun restoreAds() {
        settingsRepository.updateAdsRemoved(false)
    }

    /**
     * Check current ads status synchronously
     */
    suspend fun getCurrentAdsStatus(): Boolean {
        return try {
            val settings = settingsRepository.getCurrentSettings()
            !(settings.adsRemoved)
        } catch (e: Exception) {
            // Default to showing ads if we can't determine status
            true
        }
    }
}
