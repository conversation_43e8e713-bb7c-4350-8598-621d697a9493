package com.sirch.mileagetracker.di;

import android.content.Context;
import androidx.room.Room;
import com.sirch.mileagetracker.data.local.MileageTrackerDatabase;
import com.sirch.mileagetracker.data.local.dao.ProgramDao;
import com.sirch.mileagetracker.data.local.dao.PurchaseDao;
import com.sirch.mileagetracker.data.local.dao.SettingsDao;
import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.android.qualifiers.ApplicationContext;
import dagger.hilt.components.SingletonComponent;
import javax.inject.Singleton;

@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0004H\u0007J\u0010\u0010\n\u001a\u00020\u000b2\u0006\u0010\t\u001a\u00020\u0004H\u0007J\u0010\u0010\f\u001a\u00020\r2\u0006\u0010\t\u001a\u00020\u0004H\u0007\u00a8\u0006\u000e"}, d2 = {"Lcom/sirch/mileagetracker/di/DatabaseModule;", "", "()V", "provideDatabase", "Lcom/sirch/mileagetracker/data/local/MileageTrackerDatabase;", "context", "Landroid/content/Context;", "provideProgramDao", "Lcom/sirch/mileagetracker/data/local/dao/ProgramDao;", "database", "providePurchaseDao", "Lcom/sirch/mileagetracker/data/local/dao/PurchaseDao;", "provideSettingsDao", "Lcom/sirch/mileagetracker/data/local/dao/SettingsDao;", "app_release"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class DatabaseModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.sirch.mileagetracker.di.DatabaseModule INSTANCE = null;
    
    private DatabaseModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.data.local.MileageTrackerDatabase provideDatabase(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.data.local.dao.ProgramDao provideProgramDao(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.MileageTrackerDatabase database) {
        return null;
    }
    
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.data.local.dao.PurchaseDao providePurchaseDao(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.MileageTrackerDatabase database) {
        return null;
    }
    
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.data.local.dao.SettingsDao provideSettingsDao(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.MileageTrackerDatabase database) {
        return null;
    }
}