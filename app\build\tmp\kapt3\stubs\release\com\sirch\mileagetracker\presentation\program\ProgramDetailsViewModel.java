package com.sirch.mileagetracker.presentation.program;

import androidx.lifecycle.ViewModel;
import androidx.paging.PagingData;
import com.sirch.mileagetracker.data.local.dao.ProgramStatistics;
import com.sirch.mileagetracker.data.local.entities.Program;
import com.sirch.mileagetracker.data.local.entities.Purchase;
import com.sirch.mileagetracker.data.repository.ProgramRepository;
import com.sirch.mileagetracker.data.repository.PurchaseRepository;
import dagger.hilt.android.lifecycle.HiltViewModel;
import kotlinx.coroutines.flow.StateFlow;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011J\u000e\u0010\u0012\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0013"}, d2 = {"Lcom/sirch/mileagetracker/presentation/program/ProgramDetailsViewModel;", "Landroidx/lifecycle/ViewModel;", "programRepository", "Lcom/sirch/mileagetracker/data/repository/ProgramRepository;", "purchaseRepository", "Lcom/sirch/mileagetracker/data/repository/PurchaseRepository;", "(Lcom/sirch/mileagetracker/data/repository/ProgramRepository;Lcom/sirch/mileagetracker/data/repository/PurchaseRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/sirch/mileagetracker/presentation/program/ProgramDetailsUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "loadProgramDetails", "", "programId", "", "refresh", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ProgramDetailsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.ProgramRepository programRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.PurchaseRepository purchaseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.sirch.mileagetracker.presentation.program.ProgramDetailsUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.sirch.mileagetracker.presentation.program.ProgramDetailsUiState> uiState = null;
    
    @javax.inject.Inject()
    public ProgramDetailsViewModel(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.ProgramRepository programRepository, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.PurchaseRepository purchaseRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.sirch.mileagetracker.presentation.program.ProgramDetailsUiState> getUiState() {
        return null;
    }
    
    public final void loadProgramDetails(long programId) {
    }
    
    public final void refresh(long programId) {
    }
}