package com.sirch.mileagetracker.data.billing

import android.app.Activity
import android.content.Context
import com.android.billingclient.api.*
import com.sirch.mileagetracker.data.repository.AdsRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

@Singleton
class BillingManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val adsRepository: AdsRepository
) : PurchasesUpdatedListener, BillingClientStateListener {

    companion object {
        const val REMOVE_ADS_PRODUCT_ID = "remove_ads"
        private const val INAPP = BillingClient.ProductType.INAPP
    }

    private val billingClient = BillingClient.newBuilder(context)
        .setListener(this)
        .enablePendingPurchases(
            PendingPurchasesParams.newBuilder()
                .enableOneTimeProducts()
                .build()
        )
        .build()

    private val _connectionState = MutableStateFlow<BillingConnectionState>(BillingConnectionState.DISCONNECTED)
    val connectionState: StateFlow<BillingConnectionState> = _connectionState.asStateFlow()

    private val _purchaseEvents = Channel<PurchaseEvent>(Channel.UNLIMITED)
    val purchaseEvents: Flow<PurchaseEvent> = _purchaseEvents.receiveAsFlow()

    private val _productDetails = MutableStateFlow<List<ProductDetails>>(emptyList())
    val productDetails: StateFlow<List<ProductDetails>> = _productDetails.asStateFlow()

    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    sealed class BillingConnectionState {
        object CONNECTED : BillingConnectionState()
        object CONNECTING : BillingConnectionState()
        object DISCONNECTED : BillingConnectionState()
        data class ERROR(val message: String) : BillingConnectionState()
    }

    sealed class PurchaseEvent {
        data class PurchaseCompleted(val purchase: Purchase) : PurchaseEvent()
        data class PurchaseFailed(val error: String) : PurchaseEvent()
        data class PurchaseCancelled(val message: String) : PurchaseEvent()
    }

    init {
        startConnection()
    }

    private fun startConnection() {
        _connectionState.value = BillingConnectionState.CONNECTING
        billingClient.startConnection(this)
    }

    override fun onBillingSetupFinished(billingResult: BillingResult) {
        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
            _connectionState.value = BillingConnectionState.CONNECTED
            queryProductDetails()
            queryPurchases()
        } else {
            _connectionState.value = BillingConnectionState.ERROR(
                "Billing setup failed: ${billingResult.debugMessage}"
            )
        }
    }

    override fun onBillingServiceDisconnected() {
        _connectionState.value = BillingConnectionState.DISCONNECTED
    }

    private fun queryProductDetails() {
        val productList = listOf(
            QueryProductDetailsParams.Product.newBuilder()
                .setProductId(REMOVE_ADS_PRODUCT_ID)
                .setProductType(INAPP)
                .build()
        )

        val params = QueryProductDetailsParams.newBuilder()
            .setProductList(productList)
            .build()

        billingClient.queryProductDetailsAsync(params) { billingResult, productDetailsList ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                _productDetails.value = productDetailsList ?: emptyList()
            }
        }
    }

    private fun queryPurchases() {
        val params = QueryPurchasesParams.newBuilder()
            .setProductType(INAPP)
            .build()

        billingClient.queryPurchasesAsync(params) { billingResult, purchases ->
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                coroutineScope.launch {
                    handlePurchases(purchases)
                }
            }
        }
    }

    override fun onPurchasesUpdated(billingResult: BillingResult, purchases: MutableList<Purchase>?) {
        when (billingResult.responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                purchases?.let {
                    coroutineScope.launch {
                        handlePurchases(it)
                    }
                }
            }
            BillingClient.BillingResponseCode.USER_CANCELED -> {
                _purchaseEvents.trySend(PurchaseEvent.PurchaseCancelled("Purchase cancelled by user"))
            }
            else -> {
                _purchaseEvents.trySend(
                    PurchaseEvent.PurchaseFailed(
                        billingResult.debugMessage ?: "Unknown error"
                    )
                )
            }
        }
    }

    private suspend fun handlePurchases(purchases: List<Purchase>) {
        for (purchase in purchases) {
            if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
                if (purchase.products.contains(REMOVE_ADS_PRODUCT_ID)) {
                    // Remove ads
                    adsRepository.removeAds()
                    _purchaseEvents.trySend(PurchaseEvent.PurchaseCompleted(purchase))
                }

                // Acknowledge the purchase if not already acknowledged
                if (!purchase.isAcknowledged) {
                    acknowledgePurchase(purchase)
                }
            }
        }
    }

    private suspend fun acknowledgePurchase(purchase: Purchase) {
        val params = AcknowledgePurchaseParams.newBuilder()
            .setPurchaseToken(purchase.purchaseToken)
            .build()

        suspendCancellableCoroutine<BillingResult> { continuation ->
            billingClient.acknowledgePurchase(params) { billingResult ->
                continuation.resume(billingResult)
            }
        }
    }

    suspend fun launchBillingFlow(activity: Activity, productId: String): BillingResult {
        val productDetails = _productDetails.value.find { it.productId == productId }
            ?: return BillingResult.newBuilder()
                .setResponseCode(BillingClient.BillingResponseCode.ITEM_UNAVAILABLE)
                .build()

        val productDetailsParamsList = listOf(
            BillingFlowParams.ProductDetailsParams.newBuilder()
                .setProductDetails(productDetails)
                .build()
        )

        val params = BillingFlowParams.newBuilder()
            .setProductDetailsParamsList(productDetailsParamsList)
            .build()

        return billingClient.launchBillingFlow(activity, params)
    }

    fun getRemoveAdsProductDetails(): ProductDetails? {
        return _productDetails.value.find { it.productId == REMOVE_ADS_PRODUCT_ID }
    }

    suspend fun isRemoveAdsPurchased(): Boolean {
        return try {
            !adsRepository.getCurrentAdsStatus()
        } catch (e: Exception) {
            false
        }
    }

    fun endConnection() {
        billingClient.endConnection()
    }
}
