package com.sirch.mileagetracker.data.local.dao;

import androidx.room.*;
import com.sirch.mileagetracker.data.local.entities.Settings;
import kotlinx.coroutines.flow.Flow;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u0003H\'J\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0004H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0004H\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\b2\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u0010\u000f\u001a\u00020\b2\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012J\u0016\u0010\u0013\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0004H\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0018\u0010\u0014\u001a\u00020\b2\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0017\u00a8\u0006\u0018"}, d2 = {"Lcom/sirch/mileagetracker/data/local/dao/SettingsDao;", "", "getSettings", "Lkotlinx/coroutines/flow/Flow;", "Lcom/sirch/mileagetracker/data/local/entities/Settings;", "getSettingsOnce", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertSettings", "", "settings", "(Lcom/sirch/mileagetracker/data/local/entities/Settings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAdsRemoved", "adsRemoved", "", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLastSyncTime", "timestamp", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSettings", "updateUserId", "userId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
@androidx.room.Dao()
public abstract interface SettingsDao {
    
    @androidx.room.Query(value = "SELECT * FROM settings WHERE id = 1")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.sirch.mileagetracker.data.local.entities.Settings> getSettings();
    
    @androidx.room.Query(value = "SELECT * FROM settings WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSettingsOnce(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.local.entities.Settings> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertSettings(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Settings settings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateSettings(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Settings settings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE settings SET adsRemoved = :adsRemoved WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAdsRemoved(boolean adsRemoved, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE settings SET userId = :userId WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateUserId(@org.jetbrains.annotations.Nullable()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE settings SET lastSyncTime = :timestamp WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLastSyncTime(long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}