/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity android.app.Applicationp 6com.android.billingclient.api.PurchasesUpdatedListener8com.android.billingclient.api.BillingClientStateListenerL Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionStateL Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionStateL Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionStateL Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionStateC Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEventC Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEventC Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEventD Ccom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResultD Ccom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResultD Ccom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResultE Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResultE Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResultE Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResultE Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult androidx.room.RoomDatabaseH Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEventH Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEventH Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEventH Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEventH Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel