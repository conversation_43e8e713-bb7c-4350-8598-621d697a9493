package com.sirch.mileagetracker.utils;

import android.content.Context;
import java.io.File;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/sirch/mileagetracker/utils/DatabaseUtils;", "", "()V", "clearDatabase", "", "context", "Landroid/content/Context;", "app_release"})
public final class DatabaseUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.sirch.mileagetracker.utils.DatabaseUtils INSTANCE = null;
    
    private DatabaseUtils() {
        super();
    }
    
    /**
     * Limpa completamente o banco de dados e força uma recriação
     */
    public final void clearDatabase(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
}