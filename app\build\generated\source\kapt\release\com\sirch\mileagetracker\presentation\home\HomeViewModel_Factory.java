package com.sirch.mileagetracker.presentation.home;

import com.sirch.mileagetracker.data.repository.AdsRepository;
import com.sirch.mileagetracker.data.repository.PurchaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HomeViewModel_Factory implements Factory<HomeViewModel> {
  private final Provider<PurchaseRepository> purchaseRepositoryProvider;

  private final Provider<AdsRepository> adsRepositoryProvider;

  public HomeViewModel_Factory(Provider<PurchaseRepository> purchaseRepositoryProvider,
      Provider<AdsRepository> adsRepositoryProvider) {
    this.purchaseRepositoryProvider = purchaseRepositoryProvider;
    this.adsRepositoryProvider = adsRepositoryProvider;
  }

  @Override
  public HomeViewModel get() {
    return newInstance(purchaseRepositoryProvider.get(), adsRepositoryProvider.get());
  }

  public static HomeViewModel_Factory create(
      Provider<PurchaseRepository> purchaseRepositoryProvider,
      Provider<AdsRepository> adsRepositoryProvider) {
    return new HomeViewModel_Factory(purchaseRepositoryProvider, adsRepositoryProvider);
  }

  public static HomeViewModel newInstance(PurchaseRepository purchaseRepository,
      AdsRepository adsRepository) {
    return new HomeViewModel(purchaseRepository, adsRepository);
  }
}
