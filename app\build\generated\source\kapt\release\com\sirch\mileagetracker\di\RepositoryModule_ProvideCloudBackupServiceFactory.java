package com.sirch.mileagetracker.di;

import com.google.firebase.auth.FirebaseAuth;
import com.sirch.mileagetracker.data.cloud.CloudBackupService;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideCloudBackupServiceFactory implements Factory<CloudBackupService> {
  private final Provider<FirebaseAuth> firebaseAuthProvider;

  private final Provider<SettingsRepository> settingsRepositoryProvider;

  public RepositoryModule_ProvideCloudBackupServiceFactory(
      Provider<FirebaseAuth> firebaseAuthProvider,
      Provider<SettingsRepository> settingsRepositoryProvider) {
    this.firebaseAuthProvider = firebaseAuthProvider;
    this.settingsRepositoryProvider = settingsRepositoryProvider;
  }

  @Override
  public CloudBackupService get() {
    return provideCloudBackupService(firebaseAuthProvider.get(), settingsRepositoryProvider.get());
  }

  public static RepositoryModule_ProvideCloudBackupServiceFactory create(
      Provider<FirebaseAuth> firebaseAuthProvider,
      Provider<SettingsRepository> settingsRepositoryProvider) {
    return new RepositoryModule_ProvideCloudBackupServiceFactory(firebaseAuthProvider, settingsRepositoryProvider);
  }

  public static CloudBackupService provideCloudBackupService(FirebaseAuth firebaseAuth,
      SettingsRepository settingsRepository) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideCloudBackupService(firebaseAuth, settingsRepository));
  }
}
