package com.sirch.mileagetracker.data.local.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.Index
import kotlinx.datetime.LocalDateTime

/**
 * Entity for tracking backup history and status.
 * Supports both basic and premium backup features with detailed metadata.
 *
 * @property id Unique identifier for the backup record
 * @property userId Firebase user ID
 * @property backupTimestamp When the backup was created
 * @property backupSize Size of backup in bytes
 * @property isPremium Whether this was a premium backup
 * @property status Current status of the backup
 * @property backupType Type of backup (FULL, INCREMENTAL, MANUAL)
 * @property cloudPath Path to backup file in cloud storage
 * @property checksum MD5 checksum for integrity verification
 * @property deviceInfo Information about the device that created the backup
 * @property appVersion App version when backup was created
 * @property dataVersion Version of data schema in backup
 * @property compressionRatio Compression ratio achieved (0.0-1.0)
 * @property uploadDuration Time taken to upload backup (milliseconds)
 * @property errorMessage Error message if backup failed
 * @property isEncrypted Whether the backup is encrypted
 * @property retentionDate When this backup should be deleted
 */
@Entity(
    tableName = "backup_history",
    indices = [
        Index(value = ["userId"]),
        Index(value = ["backupTimestamp"]),
        Index(value = ["status"]),
        Index(value = ["isPremium"]),
        Index(value = ["backupType"]),
        Index(value = ["retentionDate"]),
        Index(value = ["userId", "backupTimestamp"])
    ]
)
data class BackupHistory(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,

    val userId: String,

    val backupTimestamp: LocalDateTime,

    val backupSize: Long, // Size in bytes

    val isPremium: Boolean,

    val status: BackupStatus,

    val backupType: BackupType = BackupType.AUTOMATIC,

    val cloudPath: String? = null,

    val checksum: String? = null,

    val deviceInfo: String? = null,

    val appVersion: String? = null,

    val dataVersion: Int = 1,

    val compressionRatio: Double = 0.0, // 0.0 = no compression, 0.8 = 80% compression

    val uploadDuration: Long? = null, // Milliseconds

    val errorMessage: String? = null,

    val isEncrypted: Boolean = true,

    val retentionDate: LocalDateTime? = null
) {
    companion object {
        const val MAX_FREE_BACKUPS = 5 // Maximum backups for free users
        const val MAX_PREMIUM_BACKUPS = 30 // Maximum backups for premium users
        const val BACKUP_RETENTION_DAYS_FREE = 30 // Free backup retention
        const val BACKUP_RETENTION_DAYS_PREMIUM = 365 // Premium backup retention
    }

    /**
     * Check if backup is successful
     */
    fun isSuccessful(): Boolean {
        return status == BackupStatus.COMPLETED
    }

    /**
     * Check if backup is still in progress
     */
    fun isInProgress(): Boolean {
        return status == BackupStatus.IN_PROGRESS || status == BackupStatus.UPLOADING
    }

    /**
     * Check if backup failed
     */
    fun isFailed(): Boolean {
        return status == BackupStatus.FAILED || status == BackupStatus.CORRUPTED
    }

    /**
     * Get human-readable backup size
     */
    fun getFormattedSize(): String {
        return when {
            backupSize < 1024 -> "${backupSize} B"
            backupSize < 1024 * 1024 -> "%.1f KB".format(backupSize / 1024.0)
            backupSize < 1024 * 1024 * 1024 -> "%.1f MB".format(backupSize / (1024.0 * 1024.0))
            else -> "%.1f GB".format(backupSize / (1024.0 * 1024.0 * 1024.0))
        }
    }

    /**
     * Get backup age in days
     */
    fun getAgeInDays(): Int {
        // Simplified implementation - will be enhanced later
        return 0
    }

    /**
     * Check if backup is expired based on retention policy
     */
    fun isExpired(): Boolean {
        val retentionDays = if (isPremium) BACKUP_RETENTION_DAYS_PREMIUM else BACKUP_RETENTION_DAYS_FREE
        return getAgeInDays() > retentionDays
    }

    /**
     * Get upload speed in KB/s if duration is available
     */
    fun getUploadSpeed(): Double? {
        return uploadDuration?.let { duration ->
            if (duration > 0) {
                (backupSize / 1024.0) / (duration / 1000.0) // KB/s
            } else null
        }
    }

    /**
     * Get compression percentage
     */
    fun getCompressionPercentage(): Int {
        return (compressionRatio * 100).toInt()
    }

    /**
     * Get status description for UI
     */
    fun getStatusDescription(): String {
        return when (status) {
            BackupStatus.PENDING -> "Pending"
            BackupStatus.IN_PROGRESS -> "In Progress"
            BackupStatus.UPLOADING -> "Uploading"
            BackupStatus.COMPLETED -> "Completed"
            BackupStatus.FAILED -> "Failed"
            BackupStatus.CORRUPTED -> "Corrupted"
            BackupStatus.EXPIRED -> "Expired"
        }
    }

    /**
     * Get backup type description
     */
    fun getTypeDescription(): String {
        return when (backupType) {
            BackupType.AUTOMATIC -> "Automatic"
            BackupType.MANUAL -> "Manual"
            BackupType.INCREMENTAL -> "Incremental"
            BackupType.FULL -> "Full"
            BackupType.SCHEDULED -> "Scheduled"
        }
    }

    /**
     * Check if this backup can be restored
     */
    fun canRestore(): Boolean {
        return isSuccessful() && !isExpired() && cloudPath != null
    }

    /**
     * Calculate retention date based on backup type and premium status
     */
    fun calculateRetentionDate(): LocalDateTime {
        // Simplified implementation - will be enhanced later
        return backupTimestamp
    }
}

/**
 * Status of backup operations
 */
enum class BackupStatus {
    PENDING,        // Backup is queued
    IN_PROGRESS,    // Backup is being created
    UPLOADING,      // Backup is being uploaded
    COMPLETED,      // Backup completed successfully
    FAILED,         // Backup failed
    CORRUPTED,      // Backup file is corrupted
    EXPIRED         // Backup has expired
}

/**
 * Types of backup operations
 */
enum class BackupType {
    AUTOMATIC,      // Automatic scheduled backup
    MANUAL,         // User-initiated backup
    INCREMENTAL,    // Incremental backup (changes only)
    FULL,           // Full backup (all data)
    SCHEDULED       // Scheduled backup (premium)
}
