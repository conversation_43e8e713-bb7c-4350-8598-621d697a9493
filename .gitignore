# Android Studio / IntelliJ IDEA
*.iml
.gradle
/local.properties
/.idea/caches
/.idea/libraries
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
.DS_Store
/build
/captures
.externalNativeBuild
.cxx
local.properties

# Keystore files (SECURITY)
*.keystore
*.jks
keystore.properties

# Firebase config (if contains sensitive data)
# google-services.json (uncomment if needed)

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
.cursor/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# Task Master files
tasks.json
tasks/
.taskmasterconfig

# Android specific
/app/release/
/app/debug/
/app/build/
*.apk
*.aab
proguard/

# Gradle build artifacts and caches
caches/
daemon/
native/
kotlin-profile/
wrapper/
.kotlin/

# IDE and editor configuration files
.cursorrules
.roomodes
.windsurfrules
.roo/
.tmp/