package com.sirch.mileagetracker.presentation.navigation

import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.sirch.mileagetracker.presentation.ads.InterstitialAdEffect
import com.sirch.mileagetracker.presentation.ads.rememberInterstitialAdManager
import com.sirch.mileagetracker.presentation.auth.AuthViewModel
import com.sirch.mileagetracker.presentation.auth.LoginScreen
import com.sirch.mileagetracker.presentation.home.HomeScreen
import com.sirch.mileagetracker.presentation.program.ProgramDetailsScreen
import com.sirch.mileagetracker.presentation.purchase.PurchaseFormScreen
import com.sirch.mileagetracker.presentation.settings.SettingsScreen
import com.sirch.mileagetracker.presentation.splash.SplashScreen

@Composable
fun MileageTrackerApp() {
    val navController = rememberNavController()
    val authViewModel: AuthViewModel = hiltViewModel()
    val authState by authViewModel.uiState.collectAsState()
    val context = LocalContext.current

    // Interstitial Ad Manager
    val interstitialAdManager = rememberInterstitialAdManager()

    // Handle interstitial ad events
    InterstitialAdEffect(
        adManager = interstitialAdManager,
        onAdDismissed = {
            // Ad was dismissed, continue with navigation if needed
        }
    )

    // Helper function to show interstitial ad before navigation
    fun navigateWithAd(destination: String) {
        val activity = context as? androidx.activity.ComponentActivity
        if (activity != null) {
            val adShown = interstitialAdManager.showAd(activity)
            if (!adShown) {
                // No ad available, navigate immediately
                navController.navigate(destination)
            } else {
                // Ad shown, navigation will happen after ad is dismissed
                // For now, navigate immediately after ad
                navController.navigate(destination)
            }
        } else {
            navController.navigate(destination)
        }
    }

    NavHost(
        navController = navController,
        startDestination = "splash"
    ) {
        composable("splash") {
            SplashScreen(
                onNavigateToLogin = {
                    navController.navigate("login") {
                        popUpTo("splash") { inclusive = true }
                    }
                },
                onNavigateToHome = {
                    navController.navigate("home") {
                        popUpTo("splash") { inclusive = true }
                    }
                }
            )
        }

        composable("login") {
            LoginScreen(
                onSignInSuccess = {
                    navController.navigate("home") {
                        popUpTo("login") { inclusive = true }
                    }
                }
            )
        }

        composable("home") {
            HomeScreen(
                onNavigateToProgram = { programId ->
                    navController.navigate("program_details/$programId")
                },
                onNavigateToAddPurchase = {
                    navController.navigate("add_purchase")
                },
                onNavigateToSettings = {
                    navController.navigate("settings")
                }
            )
        }

        composable("program_details/{programId}") { backStackEntry ->
            val programId = backStackEntry.arguments?.getString("programId")?.toLongOrNull() ?: 0L
            ProgramDetailsScreen(
                programId = programId,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToAddPurchase = { programId ->
                    navController.navigate("add_purchase/$programId")
                },
                onNavigateToEditPurchase = { purchaseId ->
                    navController.navigate("edit_purchase/$purchaseId")
                }
            )
        }

        composable("add_purchase") {
            PurchaseFormScreen(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        composable("add_purchase/{programId}") { backStackEntry ->
            val programId = backStackEntry.arguments?.getString("programId")?.toLongOrNull() ?: 0L
            PurchaseFormScreen(
                programId = programId,
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        composable("edit_purchase/{purchaseId}") { backStackEntry ->
            val purchaseId = backStackEntry.arguments?.getString("purchaseId")?.toLongOrNull() ?: 0L
            PurchaseFormScreen(
                purchaseId = purchaseId,
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        composable("settings") {
            SettingsScreen(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }
    }
}
