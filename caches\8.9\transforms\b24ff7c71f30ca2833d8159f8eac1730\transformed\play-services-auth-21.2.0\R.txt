int anim fragment_fast_out_extra_slow_in 0x00000000
int animator fragment_close_enter 0x00000000
int animator fragment_close_exit 0x00000000
int animator fragment_fade_enter 0x00000000
int animator fragment_fade_exit 0x00000000
int animator fragment_open_enter 0x00000000
int animator fragment_open_exit 0x00000000
int attr alpha 0x00000000
int attr buttonSize 0x00000000
int attr circleCrop 0x00000000
int attr colorScheme 0x00000000
int attr coordinatorLayoutStyle 0x00000000
int attr drawerLayoutStyle 0x00000000
int attr elevation 0x00000000
int attr font 0x00000000
int attr fontProviderAuthority 0x00000000
int attr fontProviderCerts 0x00000000
int attr fontProviderFetchStrategy 0x00000000
int attr fontProviderFetchTimeout 0x00000000
int attr fontProviderPackage 0x00000000
int attr fontProviderQuery 0x00000000
int attr fontProviderSystemFontFamily 0x00000000
int attr fontStyle 0x00000000
int attr fontVariationSettings 0x00000000
int attr fontWeight 0x00000000
int attr imageAspectRatio 0x00000000
int attr imageAspectRatioAdjust 0x00000000
int attr keylines 0x00000000
int attr lStar 0x00000000
int attr layout_anchor 0x00000000
int attr layout_anchorGravity 0x00000000
int attr layout_behavior 0x00000000
int attr layout_dodgeInsetEdges 0x00000000
int attr layout_insetEdge 0x00000000
int attr layout_keyline 0x00000000
int attr nestedScrollViewStyle 0x00000000
int attr queryPatterns 0x00000000
int attr scopeUris 0x00000000
int attr shortcutMatchRequired 0x00000000
int attr statusBarBackground 0x00000000
int attr swipeRefreshLayoutProgressSpinnerBackgroundColor 0x00000000
int attr ttcIndex 0x00000000
int color androidx_core_ripple_material_light 0x00000000
int color androidx_core_secondary_text_default_material_light 0x00000000
int color call_notification_answer_color 0x00000000
int color call_notification_decline_color 0x00000000
int color common_google_signin_btn_text_dark 0x00000000
int color common_google_signin_btn_text_dark_default 0x00000000
int color common_google_signin_btn_text_dark_disabled 0x00000000
int color common_google_signin_btn_text_dark_focused 0x00000000
int color common_google_signin_btn_text_dark_pressed 0x00000000
int color common_google_signin_btn_text_light 0x00000000
int color common_google_signin_btn_text_light_default 0x00000000
int color common_google_signin_btn_text_light_disabled 0x00000000
int color common_google_signin_btn_text_light_focused 0x00000000
int color common_google_signin_btn_text_light_pressed 0x00000000
int color common_google_signin_btn_tint 0x00000000
int color notification_action_color_filter 0x00000000
int color notification_icon_bg_color 0x00000000
int color notification_material_background_media_default_color 0x00000000
int color primary_text_default_material_dark 0x00000000
int color secondary_text_default_material_dark 0x00000000
int dimen compat_button_inset_horizontal_material 0x00000000
int dimen compat_button_inset_vertical_material 0x00000000
int dimen compat_button_padding_horizontal_material 0x00000000
int dimen compat_button_padding_vertical_material 0x00000000
int dimen compat_control_corner_material 0x00000000
int dimen compat_notification_large_icon_max_height 0x00000000
int dimen compat_notification_large_icon_max_width 0x00000000
int dimen def_drawer_elevation 0x00000000
int dimen notification_action_icon_size 0x00000000
int dimen notification_action_text_size 0x00000000
int dimen notification_big_circle_margin 0x00000000
int dimen notification_content_margin_start 0x00000000
int dimen notification_large_icon_height 0x00000000
int dimen notification_large_icon_width 0x00000000
int dimen notification_main_column_padding_top 0x00000000
int dimen notification_media_narrow_margin 0x00000000
int dimen notification_right_icon_size 0x00000000
int dimen notification_right_side_padding_top 0x00000000
int dimen notification_small_icon_background_padding 0x00000000
int dimen notification_small_icon_size_as_large 0x00000000
int dimen notification_subtext_size 0x00000000
int dimen notification_top_pad 0x00000000
int dimen notification_top_pad_large_text 0x00000000
int drawable common_full_open_on_phone 0x00000000
int drawable common_google_signin_btn_icon_dark 0x00000000
int drawable common_google_signin_btn_icon_dark_focused 0x00000000
int drawable common_google_signin_btn_icon_dark_normal 0x00000000
int drawable common_google_signin_btn_icon_dark_normal_background 0x00000000
int drawable common_google_signin_btn_icon_disabled 0x00000000
int drawable common_google_signin_btn_icon_light 0x00000000
int drawable common_google_signin_btn_icon_light_focused 0x00000000
int drawable common_google_signin_btn_icon_light_normal 0x00000000
int drawable common_google_signin_btn_icon_light_normal_background 0x00000000
int drawable common_google_signin_btn_text_dark 0x00000000
int drawable common_google_signin_btn_text_dark_focused 0x00000000
int drawable common_google_signin_btn_text_dark_normal 0x00000000
int drawable common_google_signin_btn_text_dark_normal_background 0x00000000
int drawable common_google_signin_btn_text_disabled 0x00000000
int drawable common_google_signin_btn_text_light 0x00000000
int drawable common_google_signin_btn_text_light_focused 0x00000000
int drawable common_google_signin_btn_text_light_normal 0x00000000
int drawable common_google_signin_btn_text_light_normal_background 0x00000000
int drawable googleg_disabled_color_18 0x00000000
int drawable googleg_standard_color_18 0x00000000
int drawable ic_call_answer 0x00000000
int drawable ic_call_answer_low 0x00000000
int drawable ic_call_answer_video 0x00000000
int drawable ic_call_answer_video_low 0x00000000
int drawable ic_call_decline 0x00000000
int drawable ic_call_decline_low 0x00000000
int drawable notification_action_background 0x00000000
int drawable notification_bg 0x00000000
int drawable notification_bg_low 0x00000000
int drawable notification_bg_low_normal 0x00000000
int drawable notification_bg_low_pressed 0x00000000
int drawable notification_bg_normal 0x00000000
int drawable notification_bg_normal_pressed 0x00000000
int drawable notification_icon_background 0x00000000
int drawable notification_oversize_large_icon_bg 0x00000000
int drawable notification_template_icon_bg 0x00000000
int drawable notification_template_icon_low_bg 0x00000000
int drawable notification_tile_bg 0x00000000
int drawable notify_panel_notification_icon_bg 0x00000000
int id accessibility_action_clickable_span 0x00000000
int id accessibility_custom_action_0 0x00000000
int id accessibility_custom_action_1 0x00000000
int id accessibility_custom_action_10 0x00000000
int id accessibility_custom_action_11 0x00000000
int id accessibility_custom_action_12 0x00000000
int id accessibility_custom_action_13 0x00000000
int id accessibility_custom_action_14 0x00000000
int id accessibility_custom_action_15 0x00000000
int id accessibility_custom_action_16 0x00000000
int id accessibility_custom_action_17 0x00000000
int id accessibility_custom_action_18 0x00000000
int id accessibility_custom_action_19 0x00000000
int id accessibility_custom_action_2 0x00000000
int id accessibility_custom_action_20 0x00000000
int id accessibility_custom_action_21 0x00000000
int id accessibility_custom_action_22 0x00000000
int id accessibility_custom_action_23 0x00000000
int id accessibility_custom_action_24 0x00000000
int id accessibility_custom_action_25 0x00000000
int id accessibility_custom_action_26 0x00000000
int id accessibility_custom_action_27 0x00000000
int id accessibility_custom_action_28 0x00000000
int id accessibility_custom_action_29 0x00000000
int id accessibility_custom_action_3 0x00000000
int id accessibility_custom_action_30 0x00000000
int id accessibility_custom_action_31 0x00000000
int id accessibility_custom_action_4 0x00000000
int id accessibility_custom_action_5 0x00000000
int id accessibility_custom_action_6 0x00000000
int id accessibility_custom_action_7 0x00000000
int id accessibility_custom_action_8 0x00000000
int id accessibility_custom_action_9 0x00000000
int id action0 0x00000000
int id action_container 0x00000000
int id action_divider 0x00000000
int id action_image 0x00000000
int id action_text 0x00000000
int id actions 0x00000000
int id adjust_height 0x00000000
int id adjust_width 0x00000000
int id all 0x00000000
int id async 0x00000000
int id auto 0x00000000
int id blocking 0x00000000
int id bottom 0x00000000
int id cancel_action 0x00000000
int id center 0x00000000
int id center_horizontal 0x00000000
int id center_vertical 0x00000000
int id chronometer 0x00000000
int id clip_horizontal 0x00000000
int id clip_vertical 0x00000000
int id dark 0x00000000
int id dialog_button 0x00000000
int id edit_text_id 0x00000000
int id end 0x00000000
int id end_padder 0x00000000
int id fill 0x00000000
int id fill_horizontal 0x00000000
int id fill_vertical 0x00000000
int id forever 0x00000000
int id fragment_container_view_tag 0x00000000
int id hide_ime_id 0x00000000
int id icon 0x00000000
int id icon_group 0x00000000
int id icon_only 0x00000000
int id info 0x00000000
int id italic 0x00000000
int id left 0x00000000
int id light 0x00000000
int id line1 0x00000000
int id line3 0x00000000
int id media_actions 0x00000000
int id media_controller_compat_view_tag 0x00000000
int id none 0x00000000
int id normal 0x00000000
int id notification_background 0x00000000
int id notification_main_column 0x00000000
int id notification_main_column_container 0x00000000
int id report_drawn 0x00000000
int id right 0x00000000
int id right_icon 0x00000000
int id right_side 0x00000000
int id special_effects_controller_view_tag 0x00000000
int id standard 0x00000000
int id start 0x00000000
int id status_bar_latest_event_content 0x00000000
int id tag_accessibility_actions 0x00000000
int id tag_accessibility_clickable_spans 0x00000000
int id tag_accessibility_heading 0x00000000
int id tag_accessibility_pane_title 0x00000000
int id tag_on_apply_window_listener 0x00000000
int id tag_on_receive_content_listener 0x00000000
int id tag_on_receive_content_mime_types 0x00000000
int id tag_screen_reader_focusable 0x00000000
int id tag_state_description 0x00000000
int id tag_transition_group 0x00000000
int id tag_unhandled_key_event_manager 0x00000000
int id tag_unhandled_key_listeners 0x00000000
int id tag_window_insets_animation_callback 0x00000000
int id text 0x00000000
int id text2 0x00000000
int id time 0x00000000
int id title 0x00000000
int id top 0x00000000
int id view_tree_lifecycle_owner 0x00000000
int id view_tree_on_back_pressed_dispatcher_owner 0x00000000
int id view_tree_saved_state_registry_owner 0x00000000
int id view_tree_view_model_store_owner 0x00000000
int id visible_removing_fragment_view_tag 0x00000000
int id wide 0x00000000
int integer cancel_button_image_alpha 0x00000000
int integer google_play_services_version 0x00000000
int integer status_bar_notification_info_maxnum 0x00000000
int layout custom_dialog 0x00000000
int layout ime_base_split_test_activity 0x00000000
int layout ime_secondary_split_test_activity 0x00000000
int layout notification_action 0x00000000
int layout notification_action_tombstone 0x00000000
int layout notification_media_action 0x00000000
int layout notification_media_cancel_action 0x00000000
int layout notification_template_big_media 0x00000000
int layout notification_template_big_media_custom 0x00000000
int layout notification_template_big_media_narrow 0x00000000
int layout notification_template_big_media_narrow_custom 0x00000000
int layout notification_template_custom_big 0x00000000
int layout notification_template_icon_group 0x00000000
int layout notification_template_lines_media 0x00000000
int layout notification_template_media 0x00000000
int layout notification_template_media_custom 0x00000000
int layout notification_template_part_chronometer 0x00000000
int layout notification_template_part_time 0x00000000
int string call_notification_answer_action 0x00000000
int string call_notification_answer_video_action 0x00000000
int string call_notification_decline_action 0x00000000
int string call_notification_hang_up_action 0x00000000
int string call_notification_incoming_text 0x00000000
int string call_notification_ongoing_text 0x00000000
int string call_notification_screening_text 0x00000000
int string common_google_play_services_enable_button 0x00000000
int string common_google_play_services_enable_text 0x00000000
int string common_google_play_services_enable_title 0x00000000
int string common_google_play_services_install_button 0x00000000
int string common_google_play_services_install_text 0x00000000
int string common_google_play_services_install_title 0x00000000
int string common_google_play_services_notification_channel_name 0x00000000
int string common_google_play_services_notification_ticker 0x00000000
int string common_google_play_services_unknown_issue 0x00000000
int string common_google_play_services_unsupported_text 0x00000000
int string common_google_play_services_update_button 0x00000000
int string common_google_play_services_update_text 0x00000000
int string common_google_play_services_update_title 0x00000000
int string common_google_play_services_updating_text 0x00000000
int string common_google_play_services_wear_update_text 0x00000000
int string common_open_on_phone 0x00000000
int string common_signin_button_text 0x00000000
int string common_signin_button_text_long 0x00000000
int string status_bar_notification_info_overflow 0x00000000
int style TextAppearance_Compat_Notification 0x00000000
int style TextAppearance_Compat_Notification_Info 0x00000000
int style TextAppearance_Compat_Notification_Info_Media 0x00000000
int style TextAppearance_Compat_Notification_Line2 0x00000000
int style TextAppearance_Compat_Notification_Line2_Media 0x00000000
int style TextAppearance_Compat_Notification_Media 0x00000000
int style TextAppearance_Compat_Notification_Time 0x00000000
int style TextAppearance_Compat_Notification_Time_Media 0x00000000
int style TextAppearance_Compat_Notification_Title 0x00000000
int style TextAppearance_Compat_Notification_Title_Media 0x00000000
int style Widget_Compat_NotificationActionContainer 0x00000000
int style Widget_Compat_NotificationActionText 0x00000000
int style Widget_Support_CoordinatorLayout 0x00000000
int[] styleable Capability { 0x00000000, 0x00000000 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x00000000, 0x00000000 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CoordinatorLayout { 0x00000000, 0x00000000 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable DrawerLayout { 0x00000000 }
int styleable DrawerLayout_elevation 0
int[] styleable FontFamily { 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LoadingImageView { 0x00000000, 0x00000000, 0x00000000 }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable SignInButton { 0x00000000, 0x00000000, 0x00000000 }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable SwipeRefreshLayout { 0x00000000 }
int styleable SwipeRefreshLayout_swipeRefreshLayoutProgressSpinnerBackgroundColor 0
