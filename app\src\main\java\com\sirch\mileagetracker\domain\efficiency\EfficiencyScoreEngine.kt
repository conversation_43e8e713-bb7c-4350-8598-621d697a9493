package com.sirch.mileagetracker.domain.efficiency

import com.sirch.mileagetracker.data.auth.AuthRepository
import com.sirch.mileagetracker.data.local.dao.EfficiencyScoreDao
import com.sirch.mileagetracker.data.local.dao.PurchaseDao
import com.sirch.mileagetracker.data.local.entities.EfficiencyScore
import com.sirch.mileagetracker.domain.aggregation.DataAggregationService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.max
import kotlin.math.min

/**
 * Core engine for calculating efficiency scores based on user's cost per mile
 * compared to anonymized aggregate data from all users.
 * 
 * Algorithm:
 * - Personal Score = (Aggregate avg cost per mile / User's avg cost per mile) * 100
 * - Percentile = Position in sorted list of all users (0-100)
 * - Rating = 1-5 stars based on percentile (80%+ = 5 stars)
 * 
 * Privacy: All data is anonymized before aggregation
 * Performance: Calculations complete in <500ms
 */
@Singleton
class EfficiencyScoreEngine @Inject constructor(
    private val efficiencyScoreDao: EfficiencyScoreDao,
    private val purchaseDao: PurchaseDao,
    private val authRepository: AuthRepository,
    private val dataAggregationService: DataAggregationService
) {

    companion object {
        private const val MIN_PURCHASES_FOR_SCORE = 3
        private const val MIN_SAMPLE_SIZE_FOR_RELIABLE_AGGREGATE = 10
        private const val MAX_SCORE = 100.0
        private const val MIN_SCORE = 0.0
        
        // Percentile thresholds for star ratings
        private const val FIVE_STAR_THRESHOLD = 80.0
        private const val FOUR_STAR_THRESHOLD = 60.0
        private const val THREE_STAR_THRESHOLD = 40.0
        private const val TWO_STAR_THRESHOLD = 20.0
    }

    /**
     * Calculate efficiency score for a specific user and program
     */
    suspend fun calculateEfficiencyScore(
        userId: String,
        programId: Long,
        forceRecalculation: Boolean = false
    ): EfficiencyScoreResult {
        return try {
            val startTime = System.currentTimeMillis()
            
            // Check if we have recent score and don't need to recalculate
            if (!forceRecalculation) {
                val existingScore = efficiencyScoreDao.getEfficiencyScore(userId, programId)
                if (existingScore?.isDataRecent() == true) {
                    return EfficiencyScoreResult.Success(existingScore)
                }
            }
            
            // Get user's personal statistics
            val personalStats = purchaseDao.getProgramStatistics(programId)
                ?: return EfficiencyScoreResult.InsufficientData("No purchases found for this program")
            
            // Check if user has enough purchases for reliable score
            val purchaseCount = purchaseDao.getTotalPurchaseCount()
            if (purchaseCount < MIN_PURCHASES_FOR_SCORE) {
                return EfficiencyScoreResult.InsufficientData(
                    "Need at least $MIN_PURCHASES_FOR_SCORE purchases for score calculation"
                )
            }
            
            // Get aggregate data for comparison
            val aggregateData = dataAggregationService.getAggregateData(programId)
                ?: return EfficiencyScoreResult.NoAggregateData("No aggregate data available for comparison")
            
            // Check if aggregate has sufficient sample size
            if (aggregateData.sampleSize < MIN_SAMPLE_SIZE_FOR_RELIABLE_AGGREGATE) {
                return EfficiencyScoreResult.InsufficientData(
                    "Aggregate data has insufficient sample size (${aggregateData.sampleSize} < $MIN_SAMPLE_SIZE_FOR_RELIABLE_AGGREGATE)"
                )
            }
            
            // Calculate efficiency score
            val personalAvgCostPerMile = personalStats.averageCostPerMile
            val aggregateAvgCostPerMile = aggregateData.anonymizedAvgCostPerMile
            
            val score = calculateScore(personalAvgCostPerMile, aggregateAvgCostPerMile)
            val percentile = calculatePercentile(personalAvgCostPerMile, programId)
            
            // Create or update efficiency score record
            val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
            val efficiencyScore = EfficiencyScore(
                userId = userId,
                programId = programId,
                personalAvgCostPerMile = personalAvgCostPerMile,
                aggregateAvgCostPerMile = aggregateAvgCostPerMile,
                score = score,
                percentile = percentile,
                lastUpdated = now,
                sampleSize = aggregateData.sampleSize,
                isDataSharingEnabled = true // TODO: Get from user preferences
            )
            
            // Save to database
            efficiencyScoreDao.insertEfficiencyScore(efficiencyScore)
            
            val endTime = System.currentTimeMillis()
            val calculationTime = endTime - startTime
            
            // Ensure calculation completed within performance requirement
            if (calculationTime > 500) {
                // Log performance warning but don't fail
                println("Warning: Efficiency score calculation took ${calculationTime}ms (>500ms)")
            }
            
            EfficiencyScoreResult.Success(efficiencyScore)
            
        } catch (e: Exception) {
            EfficiencyScoreResult.Error("Failed to calculate efficiency score: ${e.message}")
        }
    }

    /**
     * Get efficiency score for current user and program
     */
    suspend fun getEfficiencyScore(programId: Long): EfficiencyScoreResult {
        val userId = authRepository.getUserId()
            ?: return EfficiencyScoreResult.Error("User not authenticated")
        
        return calculateEfficiencyScore(userId, programId)
    }

    /**
     * Get efficiency scores for all programs for current user
     */
    fun getAllEfficiencyScores(): Flow<List<EfficiencyScore>> = flow {
        val userId = authRepository.getUserId()
        if (userId != null) {
            efficiencyScoreDao.getEfficiencyScoresByUser(userId).collect { scores ->
                emit(scores)
            }
        } else {
            emit(emptyList())
        }
    }

    /**
     * Recalculate efficiency scores for all programs when new purchase is added
     */
    suspend fun recalculateScoresAfterPurchase(programId: Long) {
        val userId = authRepository.getUserId() ?: return
        
        // Recalculate score for the specific program
        calculateEfficiencyScore(userId, programId, forceRecalculation = true)
        
        // Update aggregate data in background
        dataAggregationService.updateAggregateDataForProgram(programId)
    }

    /**
     * Calculate efficiency score based on personal vs aggregate cost per mile
     * Higher score = more efficient (lower cost per mile compared to average)
     */
    private fun calculateScore(personalAvgCostPerMile: Double, aggregateAvgCostPerMile: Double): Double {
        if (personalAvgCostPerMile <= 0 || aggregateAvgCostPerMile <= 0) {
            return MIN_SCORE
        }
        
        // Score = (Aggregate avg / Personal avg) * 100
        // If personal cost is lower than average, score > 100 (capped at MAX_SCORE)
        // If personal cost is higher than average, score < 100
        val rawScore = (aggregateAvgCostPerMile / personalAvgCostPerMile) * 100.0
        
        return max(MIN_SCORE, min(MAX_SCORE, rawScore))
    }

    /**
     * Calculate percentile ranking compared to all users in the program
     */
    private suspend fun calculatePercentile(personalAvgCostPerMile: Double, programId: Long): Double {
        return try {
            // Get all efficiency scores for this program to calculate percentile
            val allScores = efficiencyScoreDao.getAllScoresForProgram(programId)
            
            if (allScores.isEmpty()) {
                return 50.0 // Default to median if no other data
            }
            
            // Count how many users have higher cost per mile (worse efficiency)
            val worseCount = allScores.count { it.personalAvgCostPerMile > personalAvgCostPerMile }
            
            // Percentile = (number of worse scores / total scores) * 100
            (worseCount.toDouble() / allScores.size.toDouble()) * 100.0
            
        } catch (e: Exception) {
            50.0 // Default to median on error
        }
    }

    /**
     * Convert percentile to star rating (1-5 stars)
     */
    fun getStarRating(percentile: Double): Int {
        return when {
            percentile >= FIVE_STAR_THRESHOLD -> 5
            percentile >= FOUR_STAR_THRESHOLD -> 4
            percentile >= THREE_STAR_THRESHOLD -> 3
            percentile >= TWO_STAR_THRESHOLD -> 2
            else -> 1
        }
    }

    /**
     * Get efficiency rating description
     */
    fun getEfficiencyRating(score: Double): EfficiencyRating {
        return when {
            score >= 120 -> EfficiencyRating.EXCELLENT
            score >= 100 -> EfficiencyRating.VERY_GOOD
            score >= 80 -> EfficiencyRating.GOOD
            score >= 60 -> EfficiencyRating.AVERAGE
            score >= 40 -> EfficiencyRating.BELOW_AVERAGE
            else -> EfficiencyRating.POOR
        }
    }
}

/**
 * Result of efficiency score calculation
 */
sealed class EfficiencyScoreResult {
    data class Success(val score: EfficiencyScore) : EfficiencyScoreResult()
    data class InsufficientData(val message: String) : EfficiencyScoreResult()
    data class NoAggregateData(val message: String) : EfficiencyScoreResult()
    data class Error(val message: String) : EfficiencyScoreResult()
}

/**
 * Efficiency rating categories
 */
enum class EfficiencyRating(val displayName: String, val description: String) {
    EXCELLENT("Excelente", "Muito mais eficiente que a média"),
    VERY_GOOD("Muito Bom", "Mais eficiente que a média"),
    GOOD("Bom", "Ligeiramente mais eficiente que a média"),
    AVERAGE("Médio", "Próximo à média"),
    BELOW_AVERAGE("Abaixo da Média", "Menos eficiente que a média"),
    POOR("Ruim", "Muito menos eficiente que a média")
}
