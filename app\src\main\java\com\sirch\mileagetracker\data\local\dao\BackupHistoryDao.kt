package com.sirch.mileagetracker.data.local.dao

import androidx.room.*
import com.sirch.mileagetracker.data.local.entities.BackupHistory
import com.sirch.mileagetracker.data.local.entities.BackupStatus
import com.sirch.mileagetracker.data.local.entities.BackupType
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

/**
 * Data Access Object for BackupHistory entity.
 */
@Dao
interface BackupHistoryDao {
    
    @Query("SELECT * FROM backup_history WHERE userId = :userId ORDER BY backupTimestamp DESC")
    fun getBackupHistoryByUser(userId: String): Flow<List<BackupHistory>>
    
    @Query("SELECT * FROM backup_history WHERE userId = :userId AND status = :status ORDER BY backupTimestamp DESC")
    fun getBackupHistoryByStatus(userId: String, status: BackupStatus): Flow<List<BackupHistory>>
    
    @Query("SELECT * FROM backup_history WHERE userId = :userId AND isPremium = 1 ORDER BY backupTimestamp DESC")
    fun getPremiumBackupHistory(userId: String): Flow<List<BackupHistory>>
    
    @Query("SELECT * FROM backup_history WHERE userId = :userId AND status = 'COMPLETED' ORDER BY backupTimestamp DESC LIMIT 1")
    suspend fun getLatestSuccessfulBackup(userId: String): BackupHistory?
    
    @Query("SELECT * FROM backup_history WHERE retentionDate < :currentTime")
    suspend fun getExpiredBackups(currentTime: LocalDateTime): List<BackupHistory>
    
    @Query("SELECT COUNT(*) FROM backup_history WHERE userId = :userId AND isPremium = 0")
    suspend fun getFreeBackupCount(userId: String): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBackupHistory(backup: BackupHistory): Long
    
    @Update
    suspend fun updateBackupHistory(backup: BackupHistory)
    
    @Query("UPDATE backup_history SET status = :status, errorMessage = :errorMessage WHERE id = :backupId")
    suspend fun updateBackupStatus(backupId: Long, status: BackupStatus, errorMessage: String? = null)
    
    @Delete
    suspend fun deleteBackupHistory(backup: BackupHistory)
    
    @Query("DELETE FROM backup_history WHERE userId = :userId")
    suspend fun deleteBackupHistoryByUser(userId: String)
    
    @Query("DELETE FROM backup_history WHERE retentionDate < :currentTime")
    suspend fun deleteExpiredBackups(currentTime: LocalDateTime): Int
}
