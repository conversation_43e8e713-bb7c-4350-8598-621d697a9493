  Activity android.app  Application android.app  MileageTrackerApp android.app.Activity  MileageTrackerTheme android.app.Activity  	RESULT_OK android.app.Activity  enableEdgeToEdge android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  onCreate android.app.Application  Context android.content  Intent android.content  MODE_PRIVATE android.content.Context  MileageTrackerApp android.content.Context  MileageTrackerTheme android.content.Context  applicationContext android.content.Context  enableEdgeToEdge android.content.Context  getDatabasePath android.content.Context  getSharedPreferences android.content.Context  	getString android.content.Context  
setContent android.content.Context  MileageTrackerApp android.content.ContextWrapper  MileageTrackerTheme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  
setContent android.content.ContextWrapper  edit !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  clear (android.content.SharedPreferences.Editor  Uri android.net  toString android.net.Uri  Build 
android.os  Bundle 
android.os  	getString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  	getString android.os.Bundle  MileageTrackerApp  android.view.ContextThemeWrapper  MileageTrackerTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  MileageTrackerApp #androidx.activity.ComponentActivity  MileageTrackerTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  MileageTrackerApp -androidx.activity.ComponentActivity.Companion  MileageTrackerTheme -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResult androidx.activity.result  data 'androidx.activity.result.ActivityResult  
resultCode 'androidx.activity.result.ActivityResult  ActivityResultContracts !androidx.activity.result.contract  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  Activity androidx.compose.animation  ActivityResultContracts androidx.compose.animation  Add androidx.compose.animation  	Alignment androidx.compose.animation  AnimatedContentScope androidx.compose.animation  AnimatedVisibility androidx.compose.animation  AnimatedVisibilityScope androidx.compose.animation  ApiException androidx.compose.animation  Arrangement androidx.compose.animation  
AuthViewModel androidx.compose.animation  BenefitItem androidx.compose.animation  BenefitsSection androidx.compose.animation  Box androidx.compose.animation  Brush androidx.compose.animation  Button androidx.compose.animation  ButtonDefaults androidx.compose.animation  Card androidx.compose.animation  CardDefaults androidx.compose.animation  Check androidx.compose.animation  CircularProgressIndicator androidx.compose.animation  Column androidx.compose.animation  
Composable androidx.compose.animation  EnterTransition androidx.compose.animation  	ErrorCard androidx.compose.animation  ExitTransition androidx.compose.animation  ExperimentalMaterial3Api androidx.compose.animation  
FontWeight androidx.compose.animation  GoogleSignIn androidx.compose.animation  GoogleSignInButton androidx.compose.animation  GoogleSignInOptions androidx.compose.animation  Home androidx.compose.animation  Icon androidx.compose.animation  Icons androidx.compose.animation  ImageVector androidx.compose.animation  LaunchedEffect androidx.compose.animation  
MaterialTheme androidx.compose.animation  Modifier androidx.compose.animation  OptIn androidx.compose.animation  R androidx.compose.animation  RoundedCornerShape androidx.compose.animation  Row androidx.compose.animation  Settings androidx.compose.animation  Spacer androidx.compose.animation  Star androidx.compose.animation  String androidx.compose.animation  Suppress androidx.compose.animation  Text androidx.compose.animation  	TextAlign androidx.compose.animation  Unit androidx.compose.animation  
background androidx.compose.animation  buttonColors androidx.compose.animation  
cardColors androidx.compose.animation  collectAsState androidx.compose.animation  fadeIn androidx.compose.animation  fadeOut androidx.compose.animation  fillMaxSize androidx.compose.animation  fillMaxWidth androidx.compose.animation  getValue androidx.compose.animation  height androidx.compose.animation  java androidx.compose.animation  let androidx.compose.animation  listOf androidx.compose.animation  padding androidx.compose.animation  provideDelegate androidx.compose.animation  remember androidx.compose.animation  rememberScrollState androidx.compose.animation  size androidx.compose.animation  slideInVertically androidx.compose.animation  slideOutVertically androidx.compose.animation  stringResource androidx.compose.animation  verticalGradient androidx.compose.animation  verticalScroll androidx.compose.animation  width androidx.compose.animation  
HomeScreen /androidx.compose.animation.AnimatedContentScope  LoginScreen /androidx.compose.animation.AnimatedContentScope  ProgramDetailsScreen /androidx.compose.animation.AnimatedContentScope  PurchaseFormScreen /androidx.compose.animation.AnimatedContentScope  SettingsScreen /androidx.compose.animation.AnimatedContentScope  SplashScreen /androidx.compose.animation.AnimatedContentScope  toLongOrNull /androidx.compose.animation.AnimatedContentScope  	ErrorCard 2androidx.compose.animation.AnimatedVisibilityScope  Modifier 2androidx.compose.animation.AnimatedVisibilityScope  Spacer 2androidx.compose.animation.AnimatedVisibilityScope  dp 2androidx.compose.animation.AnimatedVisibilityScope  height 2androidx.compose.animation.AnimatedVisibilityScope  let 2androidx.compose.animation.AnimatedVisibilityScope  plus *androidx.compose.animation.EnterTransition  plus )androidx.compose.animation.ExitTransition  Image androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Activity "androidx.compose.foundation.layout  ActivityResultContracts "androidx.compose.foundation.layout  AdBanner "androidx.compose.foundation.layout  	AdUnitIds "androidx.compose.foundation.layout  Add "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  AnimatedVisibility "androidx.compose.foundation.layout  ApiException "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
AuthViewModel "androidx.compose.foundation.layout  BenefitItem "androidx.compose.foundation.layout  BenefitsSection "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Check "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  	DateField "androidx.compose.foundation.layout  
DatePicker "androidx.compose.foundation.layout  DatePickerDialog "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  	ErrorCard "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExposedDropdownMenuBox "androidx.compose.foundation.layout  ExposedDropdownMenuDefaults "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  GoogleSignIn "androidx.compose.foundation.layout  GoogleSignInButton "androidx.compose.foundation.layout  GoogleSignInOptions "androidx.compose.foundation.layout  Home "androidx.compose.foundation.layout  
HomeViewModel "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  ImageVector "androidx.compose.foundation.layout  Instant "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  	LocalDate "androidx.compose.foundation.layout  Long "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Program "androidx.compose.foundation.layout  ProgramCard "androidx.compose.foundation.layout  ProgramDetailsViewModel "androidx.compose.foundation.layout  ProgramDropdown "androidx.compose.foundation.layout  Purchase "androidx.compose.foundation.layout  PurchaseCard "androidx.compose.foundation.layout  PurchaseFormViewModel "androidx.compose.foundation.layout  R "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  SettingsViewModel "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  SplashViewModel "androidx.compose.foundation.layout  Star "androidx.compose.foundation.layout  
StatisticItem "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Suppress "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TimeZone "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TrailingIcon "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  delay "androidx.compose.foundation.layout  fadeIn "androidx.compose.foundation.layout  fadeOut "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  fromEpochMilliseconds "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberDatePickerState "androidx.compose.foundation.layout  rememberScrollState "androidx.compose.foundation.layout  run "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  slideInVertically "androidx.compose.foundation.layout  slideOutVertically "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  stringResource "androidx.compose.foundation.layout  
updateCost "androidx.compose.foundation.layout  updateDescription "androidx.compose.foundation.layout  updateMiles "androidx.compose.foundation.layout  verticalGradient "androidx.compose.foundation.layout  verticalScroll "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  AnimatedVisibility +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  BenefitsSection +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Brush +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  	ErrorCard +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  GoogleSignInButton +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  R +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Star +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fadeIn +androidx.compose.foundation.layout.BoxScope  fadeOut +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  rememberScrollState +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  slideInVertically +androidx.compose.foundation.layout.BoxScope  slideOutVertically +androidx.compose.foundation.layout.BoxScope  stringResource +androidx.compose.foundation.layout.BoxScope  verticalGradient +androidx.compose.foundation.layout.BoxScope  verticalScroll +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  AdBanner .androidx.compose.foundation.layout.ColumnScope  	AdUnitIds .androidx.compose.foundation.layout.ColumnScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  AnimatedVisibility .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  BenefitItem .androidx.compose.foundation.layout.ColumnScope  BenefitsSection .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Check .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  	DateField .androidx.compose.foundation.layout.ColumnScope  
DatePicker .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  	ErrorCard .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  GoogleSignInButton .androidx.compose.foundation.layout.ColumnScope  Home .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  ProgramCard .androidx.compose.foundation.layout.ColumnScope  ProgramDropdown .androidx.compose.foundation.layout.ColumnScope  PurchaseCard .androidx.compose.foundation.layout.ColumnScope  R .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Settings .androidx.compose.foundation.layout.ColumnScope  ShoppingCart .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Star .androidx.compose.foundation.layout.ColumnScope  
StatisticItem .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fadeIn .androidx.compose.foundation.layout.ColumnScope  fadeOut .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  run .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  slideInVertically .androidx.compose.foundation.layout.ColumnScope  slideOutVertically .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  stringResource .androidx.compose.foundation.layout.ColumnScope  
updateCost .androidx.compose.foundation.layout.ColumnScope  updateDescription .androidx.compose.foundation.layout.ColumnScope  updateMiles .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  Check +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Home +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  R +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  ShoppingCart +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Star +androidx.compose.foundation.layout.RowScope  
StatisticItem +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  format +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  stringResource +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  activity +androidx.compose.foundation.layout.androidx  ComponentActivity 4androidx.compose.foundation.layout.androidx.activity  sirch &androidx.compose.foundation.layout.com  mileagetracker ,androidx.compose.foundation.layout.com.sirch  data ;androidx.compose.foundation.layout.com.sirch.mileagetracker  local @androidx.compose.foundation.layout.com.sirch.mileagetracker.data  dao Fandroidx.compose.foundation.layout.com.sirch.mileagetracker.data.local  ProgramStatisticsWithName Jandroidx.compose.foundation.layout.com.sirch.mileagetracker.data.local.dao  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  AdBanner .androidx.compose.foundation.lazy.LazyItemScope  	AdUnitIds .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  ProgramCard .androidx.compose.foundation.lazy.LazyItemScope  PurchaseCard .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  AdBanner .androidx.compose.foundation.lazy.LazyListScope  	AdUnitIds .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  ProgramCard .androidx.compose.foundation.lazy.LazyListScope  PurchaseCard .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  AutoMirrored %androidx.compose.material.icons.Icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Filled 2androidx.compose.material.icons.Icons.AutoMirrored  	ArrowBack 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  Add ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  	DateRange ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  ShoppingCart ,androidx.compose.material.icons.Icons.Filled  Star ,androidx.compose.material.icons.Icons.Filled  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  Activity &androidx.compose.material.icons.filled  ActivityResultContracts &androidx.compose.material.icons.filled  Add &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  AnimatedVisibility &androidx.compose.material.icons.filled  ApiException &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  
AuthViewModel &androidx.compose.material.icons.filled  BenefitItem &androidx.compose.material.icons.filled  BenefitsSection &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Brush &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  	DateRange &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  	ErrorCard &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  GoogleSignIn &androidx.compose.material.icons.filled  GoogleSignInButton &androidx.compose.material.icons.filled  GoogleSignInOptions &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  ImageVector &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  OptIn &androidx.compose.material.icons.filled  R &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  ShoppingCart &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  Star &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Suppress &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  	TextAlign &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  buttonColors &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  collectAsState &androidx.compose.material.icons.filled  fadeIn &androidx.compose.material.icons.filled  fadeOut &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  java &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  rememberScrollState &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  slideInVertically &androidx.compose.material.icons.filled  slideOutVertically &androidx.compose.material.icons.filled  stringResource &androidx.compose.material.icons.filled  verticalGradient &androidx.compose.material.icons.filled  verticalScroll &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  Activity androidx.compose.material3  ActivityResultContracts androidx.compose.material3  AdBanner androidx.compose.material3  	AdUnitIds androidx.compose.material3  Add androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  AnimatedVisibility androidx.compose.material3  ApiException androidx.compose.material3  Arrangement androidx.compose.material3  
AuthViewModel androidx.compose.material3  BenefitItem androidx.compose.material3  BenefitsSection androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Brush androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  Check androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  	DateField androidx.compose.material3  
DatePicker androidx.compose.material3  DatePickerDialog androidx.compose.material3  DatePickerState androidx.compose.material3  DropdownMenuItem androidx.compose.material3  	ErrorCard androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuBox androidx.compose.material3  ExposedDropdownMenuBoxScope androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  FloatingActionButton androidx.compose.material3  
FontWeight androidx.compose.material3  GoogleSignIn androidx.compose.material3  GoogleSignInButton androidx.compose.material3  GoogleSignInOptions androidx.compose.material3  Home androidx.compose.material3  
HomeViewModel androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  ImageVector androidx.compose.material3  Instant androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  List androidx.compose.material3  	LocalDate androidx.compose.material3  Long androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  Program androidx.compose.material3  ProgramCard androidx.compose.material3  ProgramDetailsViewModel androidx.compose.material3  ProgramDropdown androidx.compose.material3  Purchase androidx.compose.material3  PurchaseCard androidx.compose.material3  PurchaseFormViewModel androidx.compose.material3  R androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Settings androidx.compose.material3  SettingsViewModel androidx.compose.material3  Spacer androidx.compose.material3  SplashViewModel androidx.compose.material3  Star androidx.compose.material3  
StatisticItem androidx.compose.material3  String androidx.compose.material3  Suppress androidx.compose.material3  Switch androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  TimeZone androidx.compose.material3  	TopAppBar androidx.compose.material3  TrailingIcon androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  androidx androidx.compose.material3  
background androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  collectAsState androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  delay androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fadeIn androidx.compose.material3  fadeOut androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  format androidx.compose.material3  fromEpochMilliseconds androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  java androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberDatePickerState androidx.compose.material3  rememberScrollState androidx.compose.material3  run androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  slideInVertically androidx.compose.material3  slideOutVertically androidx.compose.material3  spacedBy androidx.compose.material3  stringResource androidx.compose.material3  
updateCost androidx.compose.material3  updateDescription androidx.compose.material3  updateMiles androidx.compose.material3  verticalGradient androidx.compose.material3  verticalScroll androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  selectedDateMillis *androidx.compose.material3.DatePickerState  DropdownMenuItem 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenu 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenuDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Modifier 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextField 6androidx.compose.material3.ExposedDropdownMenuBoxScope  R 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Text 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuBoxScope  fillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
menuAnchor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  stringResource 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuDefaults  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  activity #androidx.compose.material3.androidx  ComponentActivity ,androidx.compose.material3.androidx.activity  sirch androidx.compose.material3.com  mileagetracker $androidx.compose.material3.com.sirch  data 3androidx.compose.material3.com.sirch.mileagetracker  local 8androidx.compose.material3.com.sirch.mileagetracker.data  dao >androidx.compose.material3.com.sirch.mileagetracker.data.local  ProgramStatisticsWithName Bandroidx.compose.material3.com.sirch.mileagetracker.data.local.dao  Activity androidx.compose.runtime  ActivityResultContracts androidx.compose.runtime  AdBanner androidx.compose.runtime  AdError androidx.compose.runtime  AdEvent androidx.compose.runtime  	AdRequest androidx.compose.runtime  AdSize androidx.compose.runtime  	AdUnitIds androidx.compose.runtime  Add androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  AndroidEntryPoint androidx.compose.runtime  AnimatedVisibility androidx.compose.runtime  Any androidx.compose.runtime  ApiException androidx.compose.runtime  Arrangement androidx.compose.runtime  
AuthViewModel androidx.compose.runtime  BenefitItem androidx.compose.runtime  BenefitsSection androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Brush androidx.compose.runtime  BuildConfig androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  Channel androidx.compose.runtime  Check androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  Context androidx.compose.runtime  	DateField androidx.compose.runtime  
DatePicker androidx.compose.runtime  DatePickerDialog androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  Dp androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  	ErrorCard androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExposedDropdownMenuBox androidx.compose.runtime  ExposedDropdownMenuDefaults androidx.compose.runtime  Float androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  
FontWeight androidx.compose.runtime  FullScreenContentCallback androidx.compose.runtime  GoogleSignIn androidx.compose.runtime  GoogleSignInButton androidx.compose.runtime  GoogleSignInOptions androidx.compose.runtime  Home androidx.compose.runtime  
HomeScreen androidx.compose.runtime  
HomeViewModel androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  ImageVector androidx.compose.runtime  Instant androidx.compose.runtime  Int androidx.compose.runtime  InterstitialAd androidx.compose.runtime  InterstitialAdLoadCallback androidx.compose.runtime  InterstitialAdManager androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  List androidx.compose.runtime  LoadAdError androidx.compose.runtime  	LocalDate androidx.compose.runtime  LocalDensity androidx.compose.runtime  LoginScreen androidx.compose.runtime  Long androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  MileageTrackerApp androidx.compose.runtime  MileageTrackerTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  Preview androidx.compose.runtime  Program androidx.compose.runtime  ProgramCard androidx.compose.runtime  ProgramDetailsScreen androidx.compose.runtime  ProgramDetailsViewModel androidx.compose.runtime  ProgramDropdown androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Purchase androidx.compose.runtime  PurchaseCard androidx.compose.runtime  PurchaseFormScreen androidx.compose.runtime  PurchaseFormViewModel androidx.compose.runtime  R androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  Settings androidx.compose.runtime  SettingsScreen androidx.compose.runtime  SettingsViewModel androidx.compose.runtime  Spacer androidx.compose.runtime  SplashScreen androidx.compose.runtime  SplashViewModel androidx.compose.runtime  Stable androidx.compose.runtime  StableHolder androidx.compose.runtime  StableLambda androidx.compose.runtime  Star androidx.compose.runtime  State androidx.compose.runtime  
StatisticItem androidx.compose.runtime  String androidx.compose.runtime  Suppress androidx.compose.runtime  Switch androidx.compose.runtime  T androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  TimeZone androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TrailingIcon androidx.compose.runtime  Unit androidx.compose.runtime  	_adEvents androidx.compose.runtime  androidx androidx.compose.runtime  apply androidx.compose.runtime  
background androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  collectAsState androidx.compose.runtime  com androidx.compose.runtime  delay androidx.compose.runtime  derivedStateOf androidx.compose.runtime  fadeIn androidx.compose.runtime  fadeOut androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  format androidx.compose.runtime  fromEpochMilliseconds androidx.compose.runtime  getValue androidx.compose.runtime  hashCode androidx.compose.runtime  height androidx.compose.runtime  interstitialAd androidx.compose.runtime  	isLoading androidx.compose.runtime  java androidx.compose.runtime  kotlinx androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  loadAd androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  
receiveAsFlow androidx.compose.runtime  remember androidx.compose.runtime  rememberDatePickerState androidx.compose.runtime  rememberScrollState androidx.compose.runtime  run androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  slideInVertically androidx.compose.runtime  slideOutVertically androidx.compose.runtime  spacedBy androidx.compose.runtime  stringResource androidx.compose.runtime  toLongOrNull androidx.compose.runtime  
updateCost androidx.compose.runtime  updateDescription androidx.compose.runtime  updateMiles androidx.compose.runtime  verticalGradient androidx.compose.runtime  verticalScroll androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  with androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  	onDispose .androidx.compose.runtime.DisposableEffectScope  AdEvent .androidx.compose.runtime.InterstitialAdManager  AdDismissed 6androidx.compose.runtime.InterstitialAdManager.AdEvent  AdFailedToLoad 6androidx.compose.runtime.InterstitialAdManager.AdEvent  AdFailedToShow 6androidx.compose.runtime.InterstitialAdManager.AdEvent  AdLoaded 6androidx.compose.runtime.InterstitialAdManager.AdEvent  AdShown 6androidx.compose.runtime.InterstitialAdManager.AdEvent  setValue %androidx.compose.runtime.MutableState  value %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  activity !androidx.compose.runtime.androidx  compose !androidx.compose.runtime.androidx  ComponentActivity *androidx.compose.runtime.androidx.activity  
foundation )androidx.compose.runtime.androidx.compose  layout 4androidx.compose.runtime.androidx.compose.foundation  
PaddingValues ;androidx.compose.runtime.androidx.compose.foundation.layout  google androidx.compose.runtime.com  sirch androidx.compose.runtime.com  android #androidx.compose.runtime.com.google  gms +androidx.compose.runtime.com.google.android  ads /androidx.compose.runtime.com.google.android.gms  
AdListener 3androidx.compose.runtime.com.google.android.gms.ads  mileagetracker "androidx.compose.runtime.com.sirch  data 1androidx.compose.runtime.com.sirch.mileagetracker  local 6androidx.compose.runtime.com.sirch.mileagetracker.data  dao <androidx.compose.runtime.com.sirch.mileagetracker.data.local  ProgramStatisticsWithName @androidx.compose.runtime.com.sirch.mileagetracker.data.local.dao  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  End androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Top androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  End 'androidx.compose.ui.Alignment.Companion  Top 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  
menuAnchor androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  copy "androidx.compose.ui.graphics.Color  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  LocalDensity androidx.compose.ui.platform  painterResource androidx.compose.ui.res  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  	Companion +androidx.compose.ui.text.input.KeyboardType  Decimal +androidx.compose.ui.text.input.KeyboardType  Number +androidx.compose.ui.text.input.KeyboardType  Decimal 5androidx.compose.ui.text.input.KeyboardType.Companion  Number 5androidx.compose.ui.text.input.KeyboardType.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Density androidx.compose.ui.unit  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  toPx  androidx.compose.ui.unit.Density  LocalDensity androidx.compose.ui.unit.Dp  remember androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  with androidx.compose.ui.unit.Dp  AndroidView androidx.compose.ui.viewinterop  Bundle #androidx.core.app.ComponentActivity  MileageTrackerApp #androidx.core.app.ComponentActivity  MileageTrackerTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  	arguments %androidx.navigation.NavBackStackEntry  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  
HomeScreen #androidx.navigation.NavGraphBuilder  LoginScreen #androidx.navigation.NavGraphBuilder  ProgramDetailsScreen #androidx.navigation.NavGraphBuilder  PurchaseFormScreen #androidx.navigation.NavGraphBuilder  SettingsScreen #androidx.navigation.NavGraphBuilder  SplashScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  toLongOrNull #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  popUpTo %androidx.navigation.NavOptionsBuilder  	inclusive "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  Pager androidx.paging  PagingConfig androidx.paging  
PagingData androidx.paging  PagingSource androidx.paging  cachedIn androidx.paging  flow androidx.paging.Pager  Boolean 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Double 
androidx.room  Entity 
androidx.room  Flow 
androidx.room  GlobalStatistics 
androidx.room  Insert 
androidx.room  Int 
androidx.room  List 
androidx.room  Long 
androidx.room  OnConflictStrategy 
androidx.room  PagingSource 
androidx.room  
PrimaryKey 
androidx.room  Program 
androidx.room  ProgramStatistics 
androidx.room  ProgramStatisticsWithName 
androidx.room  Purchase 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Settings 
androidx.room  String 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  Builder androidx.room.RoomDatabase  	Companion androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  MileageTrackerDatabase androidx.room.RoomDatabase  
ProgramDao androidx.room.RoomDatabase  PurchaseDao androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  SettingsDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  databaseBuilder androidx.room.RoomDatabase  java androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  MileageTrackerDatabase $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  databaseBuilder $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  AcknowledgePurchaseParams com.android.billingclient.api  #AcknowledgePurchaseResponseListener com.android.billingclient.api  Activity com.android.billingclient.api  
AdsRepository com.android.billingclient.api  ApplicationContext com.android.billingclient.api  
BillingClient com.android.billingclient.api  BillingClientStateListener com.android.billingclient.api  BillingConnectionState com.android.billingclient.api  BillingFlowParams com.android.billingclient.api  
BillingResult com.android.billingclient.api  Boolean com.android.billingclient.api  Channel com.android.billingclient.api  Context com.android.billingclient.api  CoroutineScope com.android.billingclient.api  Dispatchers com.android.billingclient.api  	Exception com.android.billingclient.api  Flow com.android.billingclient.api  INAPP com.android.billingclient.api  Inject com.android.billingclient.api  List com.android.billingclient.api  MutableList com.android.billingclient.api  MutableStateFlow com.android.billingclient.api  PendingPurchasesParams com.android.billingclient.api  ProductDetails com.android.billingclient.api  ProductDetailsResponseListener com.android.billingclient.api  Purchase com.android.billingclient.api  
PurchaseEvent com.android.billingclient.api  PurchasesResponseListener com.android.billingclient.api  PurchasesUpdatedListener com.android.billingclient.api  QueryProductDetailsParams com.android.billingclient.api  QueryPurchasesParams com.android.billingclient.api  REMOVE_ADS_PRODUCT_ID com.android.billingclient.api  	Singleton com.android.billingclient.api  	StateFlow com.android.billingclient.api  String com.android.billingclient.api  asStateFlow com.android.billingclient.api  	emptyList com.android.billingclient.api  find com.android.billingclient.api  handlePurchases com.android.billingclient.api  launch com.android.billingclient.api  let com.android.billingclient.api  listOf com.android.billingclient.api  
receiveAsFlow com.android.billingclient.api  resume com.android.billingclient.api  suspendCancellableCoroutine com.android.billingclient.api  Builder 7com.android.billingclient.api.AcknowledgePurchaseParams  
newBuilder 7com.android.billingclient.api.AcknowledgePurchaseParams  build ?com.android.billingclient.api.AcknowledgePurchaseParams.Builder  setPurchaseToken ?com.android.billingclient.api.AcknowledgePurchaseParams.Builder  <SAM-CONSTRUCTOR> Acom.android.billingclient.api.AcknowledgePurchaseResponseListener  Builder +com.android.billingclient.api.BillingClient  acknowledgePurchase +com.android.billingclient.api.BillingClient  
endConnection +com.android.billingclient.api.BillingClient  launchBillingFlow +com.android.billingclient.api.BillingClient  
newBuilder +com.android.billingclient.api.BillingClient  queryProductDetailsAsync +com.android.billingclient.api.BillingClient  queryPurchasesAsync +com.android.billingclient.api.BillingClient  startConnection +com.android.billingclient.api.BillingClient  ITEM_UNAVAILABLE ?com.android.billingclient.api.BillingClient.BillingResponseCode  OK ?com.android.billingclient.api.BillingClient.BillingResponseCode  
USER_CANCELED ?com.android.billingclient.api.BillingClient.BillingResponseCode  build 3com.android.billingclient.api.BillingClient.Builder  enablePendingPurchases 3com.android.billingclient.api.BillingClient.Builder  setListener 3com.android.billingclient.api.BillingClient.Builder  INAPP 7com.android.billingclient.api.BillingClient.ProductType  Builder /com.android.billingclient.api.BillingFlowParams  ProductDetailsParams /com.android.billingclient.api.BillingFlowParams  
newBuilder /com.android.billingclient.api.BillingFlowParams  build 7com.android.billingclient.api.BillingFlowParams.Builder  setProductDetailsParamsList 7com.android.billingclient.api.BillingFlowParams.Builder  Builder Dcom.android.billingclient.api.BillingFlowParams.ProductDetailsParams  
newBuilder Dcom.android.billingclient.api.BillingFlowParams.ProductDetailsParams  build Lcom.android.billingclient.api.BillingFlowParams.ProductDetailsParams.Builder  setProductDetails Lcom.android.billingclient.api.BillingFlowParams.ProductDetailsParams.Builder  Builder +com.android.billingclient.api.BillingResult  debugMessage +com.android.billingclient.api.BillingResult  
newBuilder +com.android.billingclient.api.BillingResult  responseCode +com.android.billingclient.api.BillingResult  build 3com.android.billingclient.api.BillingResult.Builder  setResponseCode 3com.android.billingclient.api.BillingResult.Builder  Builder 4com.android.billingclient.api.PendingPurchasesParams  
newBuilder 4com.android.billingclient.api.PendingPurchasesParams  build <com.android.billingclient.api.PendingPurchasesParams.Builder  enableOneTimeProducts <com.android.billingclient.api.PendingPurchasesParams.Builder  OneTimePurchaseOfferDetails ,com.android.billingclient.api.ProductDetails  oneTimePurchaseOfferDetails ,com.android.billingclient.api.ProductDetails  	productId ,com.android.billingclient.api.ProductDetails  formattedPrice Hcom.android.billingclient.api.ProductDetails.OneTimePurchaseOfferDetails  <SAM-CONSTRUCTOR> <com.android.billingclient.api.ProductDetailsResponseListener  isAcknowledged &com.android.billingclient.api.Purchase  products &com.android.billingclient.api.Purchase  
purchaseState &com.android.billingclient.api.Purchase  
purchaseToken &com.android.billingclient.api.Purchase  	PURCHASED 4com.android.billingclient.api.Purchase.PurchaseState  <SAM-CONSTRUCTOR> 7com.android.billingclient.api.PurchasesResponseListener  Builder 7com.android.billingclient.api.QueryProductDetailsParams  Product 7com.android.billingclient.api.QueryProductDetailsParams  
newBuilder 7com.android.billingclient.api.QueryProductDetailsParams  build ?com.android.billingclient.api.QueryProductDetailsParams.Builder  setProductList ?com.android.billingclient.api.QueryProductDetailsParams.Builder  Builder ?com.android.billingclient.api.QueryProductDetailsParams.Product  
newBuilder ?com.android.billingclient.api.QueryProductDetailsParams.Product  build Gcom.android.billingclient.api.QueryProductDetailsParams.Product.Builder  setProductId Gcom.android.billingclient.api.QueryProductDetailsParams.Product.Builder  setProductType Gcom.android.billingclient.api.QueryProductDetailsParams.Product.Builder  Builder 2com.android.billingclient.api.QueryPurchasesParams  
newBuilder 2com.android.billingclient.api.QueryPurchasesParams  build :com.android.billingclient.api.QueryPurchasesParams.Builder  setProductType :com.android.billingclient.api.QueryPurchasesParams.Builder  AdError com.google.android.gms.ads  
AdListener com.google.android.gms.ads  	AdRequest com.google.android.gms.ads  AdSize com.google.android.gms.ads  AdView com.google.android.gms.ads  FullScreenContentCallback com.google.android.gms.ads  LoadAdError com.google.android.gms.ads  onAdClicked %com.google.android.gms.ads.AdListener  onAdFailedToLoad %com.google.android.gms.ads.AdListener  
onAdLoaded %com.google.android.gms.ads.AdListener  AdError )com.google.android.gms.ads.AdLoadCallback  AdEvent )com.google.android.gms.ads.AdLoadCallback  FullScreenContentCallback )com.google.android.gms.ads.AdLoadCallback  	_adEvents )com.google.android.gms.ads.AdLoadCallback  interstitialAd )com.google.android.gms.ads.AdLoadCallback  	isLoading )com.google.android.gms.ads.AdLoadCallback  loadAd )com.google.android.gms.ads.AdLoadCallback  Builder $com.google.android.gms.ads.AdRequest  build ,com.google.android.gms.ads.AdRequest.Builder  BANNER !com.google.android.gms.ads.AdSize  	AdRequest !com.google.android.gms.ads.AdView  
adListener !com.google.android.gms.ads.AdView  adUnitId !com.google.android.gms.ads.AdView  apply !com.google.android.gms.ads.AdView  loadAd !com.google.android.gms.ads.AdView  	setAdSize !com.google.android.gms.ads.AdView  
adListener %com.google.android.gms.ads.BaseAdView  adUnitId %com.google.android.gms.ads.BaseAdView  loadAd %com.google.android.gms.ads.BaseAdView  	setAdSize %com.google.android.gms.ads.BaseAdView  AdEvent 4com.google.android.gms.ads.FullScreenContentCallback  	_adEvents 4com.google.android.gms.ads.FullScreenContentCallback  interstitialAd 4com.google.android.gms.ads.FullScreenContentCallback  loadAd 4com.google.android.gms.ads.FullScreenContentCallback  AdManagerAdRequest $com.google.android.gms.ads.admanager  AdManagerAdView $com.google.android.gms.ads.admanager  InterstitialAd 'com.google.android.gms.ads.interstitial  InterstitialAdLoadCallback 'com.google.android.gms.ads.interstitial  fullScreenContentCallback 6com.google.android.gms.ads.interstitial.InterstitialAd  load 6com.google.android.gms.ads.interstitial.InterstitialAd  show 6com.google.android.gms.ads.interstitial.InterstitialAd  AdError Bcom.google.android.gms.ads.interstitial.InterstitialAdLoadCallback  AdEvent Bcom.google.android.gms.ads.interstitial.InterstitialAdLoadCallback  FullScreenContentCallback Bcom.google.android.gms.ads.interstitial.InterstitialAdLoadCallback  	_adEvents Bcom.google.android.gms.ads.interstitial.InterstitialAdLoadCallback  interstitialAd Bcom.google.android.gms.ads.interstitial.InterstitialAdLoadCallback  	isLoading Bcom.google.android.gms.ads.interstitial.InterstitialAdLoadCallback  loadAd Bcom.google.android.gms.ads.interstitial.InterstitialAdLoadCallback  GoogleSignIn &com.google.android.gms.auth.api.signin  GoogleSignInClient &com.google.android.gms.auth.api.signin  GoogleSignInOptions &com.google.android.gms.auth.api.signin  	getClient 3com.google.android.gms.auth.api.signin.GoogleSignIn  getSignedInAccountFromIntent 3com.google.android.gms.auth.api.signin.GoogleSignIn  idToken :com.google.android.gms.auth.api.signin.GoogleSignInAccount  signInIntent 9com.google.android.gms.auth.api.signin.GoogleSignInClient  Builder :com.google.android.gms.auth.api.signin.GoogleSignInOptions  DEFAULT_SIGN_IN :com.google.android.gms.auth.api.signin.GoogleSignInOptions  build Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  requestEmail Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  requestIdToken Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  ApiException !com.google.android.gms.common.api  
statusCode .com.google.android.gms.common.api.ApiException  Task com.google.android.gms.tasks  await !com.google.android.gms.tasks.Task  	getResult !com.google.android.gms.tasks.Task  FirebaseAnalytics com.google.firebase.analytics  	analytics !com.google.firebase.analytics.ktx  AuthCredential com.google.firebase.auth  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  GoogleAuthProvider com.google.firebase.auth  user #com.google.firebase.auth.AuthResult  currentUser %com.google.firebase.auth.FirebaseAuth  signInWithCredential %com.google.firebase.auth.FirebaseAuth  signOut %com.google.firebase.auth.FirebaseAuth  displayName %com.google.firebase.auth.FirebaseUser  email %com.google.firebase.auth.FirebaseUser  photoUrl %com.google.firebase.auth.FirebaseUser  uid %com.google.firebase.auth.FirebaseUser  
getCredential +com.google.firebase.auth.GoogleAuthProvider  auth com.google.firebase.auth.ktx  Firebase com.google.firebase.ktx  	analytics  com.google.firebase.ktx.Firebase  auth  com.google.firebase.ktx.Firebase  AndroidEntryPoint com.sirch.mileagetracker  Application com.sirch.mileagetracker  BuildConfig com.sirch.mileagetracker  Bundle com.sirch.mileagetracker  ComponentActivity com.sirch.mileagetracker  
Composable com.sirch.mileagetracker  HiltAndroidApp com.sirch.mileagetracker  MainActivity com.sirch.mileagetracker  MileageTrackerApp com.sirch.mileagetracker  MileageTrackerApplication com.sirch.mileagetracker  MileageTrackerPreview com.sirch.mileagetracker  MileageTrackerTheme com.sirch.mileagetracker  Preview com.sirch.mileagetracker  R com.sirch.mileagetracker  DEBUG $com.sirch.mileagetracker.BuildConfig  MileageTrackerApp %com.sirch.mileagetracker.MainActivity  MileageTrackerTheme %com.sirch.mileagetracker.MainActivity  enableEdgeToEdge %com.sirch.mileagetracker.MainActivity  
setContent %com.sirch.mileagetracker.MainActivity  add_purchase !com.sirch.mileagetracker.R.string  app_name !com.sirch.mileagetracker.R.string  average_cost_per_mile !com.sirch.mileagetracker.R.string  currency_symbol !com.sirch.mileagetracker.R.string  default_web_client_id !com.sirch.mileagetracker.R.string  loading !com.sirch.mileagetracker.R.string  login_benefit_backup !com.sirch.mileagetracker.R.string  login_benefit_premium !com.sirch.mileagetracker.R.string  login_benefit_sync !com.sirch.mileagetracker.R.string  login_benefits_title !com.sirch.mileagetracker.R.string  login_description !com.sirch.mileagetracker.R.string  login_error_cancelled !com.sirch.mileagetracker.R.string  login_error_generic !com.sirch.mileagetracker.R.string  login_error_network !com.sirch.mileagetracker.R.string  programs_title !com.sirch.mileagetracker.R.string  
purchase_cost !com.sirch.mileagetracker.R.string  
purchase_date !com.sirch.mileagetracker.R.string  purchase_miles !com.sirch.mileagetracker.R.string  purchase_program !com.sirch.mileagetracker.R.string  purchases_title !com.sirch.mileagetracker.R.string  save !com.sirch.mileagetracker.R.string  settings_title !com.sirch.mileagetracker.R.string  sign_in_with_google !com.sirch.mileagetracker.R.string  total_miles !com.sirch.mileagetracker.R.string  total_spent !com.sirch.mileagetracker.R.string  welcome !com.sirch.mileagetracker.R.string  welcome_subtitle !com.sirch.mileagetracker.R.string  AuthRepository "com.sirch.mileagetracker.data.auth  Boolean "com.sirch.mileagetracker.data.auth  	Exception "com.sirch.mileagetracker.data.auth  FirebaseAuth "com.sirch.mileagetracker.data.auth  FirebaseUser "com.sirch.mileagetracker.data.auth  Flow "com.sirch.mileagetracker.data.auth  GoogleAuthProvider "com.sirch.mileagetracker.data.auth  Inject "com.sirch.mileagetracker.data.auth  Result "com.sirch.mileagetracker.data.auth  	Singleton "com.sirch.mileagetracker.data.auth  String "com.sirch.mileagetracker.data.auth  Unit "com.sirch.mileagetracker.data.auth  await "com.sirch.mileagetracker.data.auth  currentUser "com.sirch.mileagetracker.data.auth  failure "com.sirch.mileagetracker.data.auth  flow "com.sirch.mileagetracker.data.auth  success "com.sirch.mileagetracker.data.auth  	Exception 1com.sirch.mileagetracker.data.auth.AuthRepository  GoogleAuthProvider 1com.sirch.mileagetracker.data.auth.AuthRepository  Result 1com.sirch.mileagetracker.data.auth.AuthRepository  Unit 1com.sirch.mileagetracker.data.auth.AuthRepository  await 1com.sirch.mileagetracker.data.auth.AuthRepository  currentUser 1com.sirch.mileagetracker.data.auth.AuthRepository  failure 1com.sirch.mileagetracker.data.auth.AuthRepository  firebaseAuth 1com.sirch.mileagetracker.data.auth.AuthRepository  flow 1com.sirch.mileagetracker.data.auth.AuthRepository  getCurrentUserFlow 1com.sirch.mileagetracker.data.auth.AuthRepository  isUserSignedIn 1com.sirch.mileagetracker.data.auth.AuthRepository  signInWithGoogle 1com.sirch.mileagetracker.data.auth.AuthRepository  signOut 1com.sirch.mileagetracker.data.auth.AuthRepository  success 1com.sirch.mileagetracker.data.auth.AuthRepository  AcknowledgePurchaseParams %com.sirch.mileagetracker.data.billing  Activity %com.sirch.mileagetracker.data.billing  
AdsRepository %com.sirch.mileagetracker.data.billing  ApplicationContext %com.sirch.mileagetracker.data.billing  
BillingClient %com.sirch.mileagetracker.data.billing  BillingClientStateListener %com.sirch.mileagetracker.data.billing  BillingConnectionState %com.sirch.mileagetracker.data.billing  BillingFlowParams %com.sirch.mileagetracker.data.billing  BillingManager %com.sirch.mileagetracker.data.billing  
BillingResult %com.sirch.mileagetracker.data.billing  Boolean %com.sirch.mileagetracker.data.billing  Channel %com.sirch.mileagetracker.data.billing  Context %com.sirch.mileagetracker.data.billing  CoroutineScope %com.sirch.mileagetracker.data.billing  Dispatchers %com.sirch.mileagetracker.data.billing  	Exception %com.sirch.mileagetracker.data.billing  Flow %com.sirch.mileagetracker.data.billing  INAPP %com.sirch.mileagetracker.data.billing  Inject %com.sirch.mileagetracker.data.billing  List %com.sirch.mileagetracker.data.billing  MutableList %com.sirch.mileagetracker.data.billing  MutableStateFlow %com.sirch.mileagetracker.data.billing  PendingPurchasesParams %com.sirch.mileagetracker.data.billing  ProductDetails %com.sirch.mileagetracker.data.billing  Purchase %com.sirch.mileagetracker.data.billing  
PurchaseEvent %com.sirch.mileagetracker.data.billing  PurchasesUpdatedListener %com.sirch.mileagetracker.data.billing  QueryProductDetailsParams %com.sirch.mileagetracker.data.billing  QueryPurchasesParams %com.sirch.mileagetracker.data.billing  REMOVE_ADS_PRODUCT_ID %com.sirch.mileagetracker.data.billing  	Singleton %com.sirch.mileagetracker.data.billing  	StateFlow %com.sirch.mileagetracker.data.billing  String %com.sirch.mileagetracker.data.billing  asStateFlow %com.sirch.mileagetracker.data.billing  	emptyList %com.sirch.mileagetracker.data.billing  find %com.sirch.mileagetracker.data.billing  handlePurchases %com.sirch.mileagetracker.data.billing  launch %com.sirch.mileagetracker.data.billing  let %com.sirch.mileagetracker.data.billing  listOf %com.sirch.mileagetracker.data.billing  
receiveAsFlow %com.sirch.mileagetracker.data.billing  resume %com.sirch.mileagetracker.data.billing  suspendCancellableCoroutine %com.sirch.mileagetracker.data.billing  AcknowledgePurchaseParams 4com.sirch.mileagetracker.data.billing.BillingManager  Activity 4com.sirch.mileagetracker.data.billing.BillingManager  
AdsRepository 4com.sirch.mileagetracker.data.billing.BillingManager  ApplicationContext 4com.sirch.mileagetracker.data.billing.BillingManager  
BillingClient 4com.sirch.mileagetracker.data.billing.BillingManager  BillingConnectionState 4com.sirch.mileagetracker.data.billing.BillingManager  BillingFlowParams 4com.sirch.mileagetracker.data.billing.BillingManager  
BillingResult 4com.sirch.mileagetracker.data.billing.BillingManager  Boolean 4com.sirch.mileagetracker.data.billing.BillingManager  Channel 4com.sirch.mileagetracker.data.billing.BillingManager  	Companion 4com.sirch.mileagetracker.data.billing.BillingManager  Context 4com.sirch.mileagetracker.data.billing.BillingManager  CoroutineScope 4com.sirch.mileagetracker.data.billing.BillingManager  Dispatchers 4com.sirch.mileagetracker.data.billing.BillingManager  	Exception 4com.sirch.mileagetracker.data.billing.BillingManager  Flow 4com.sirch.mileagetracker.data.billing.BillingManager  INAPP 4com.sirch.mileagetracker.data.billing.BillingManager  Inject 4com.sirch.mileagetracker.data.billing.BillingManager  List 4com.sirch.mileagetracker.data.billing.BillingManager  MutableList 4com.sirch.mileagetracker.data.billing.BillingManager  MutableStateFlow 4com.sirch.mileagetracker.data.billing.BillingManager  PendingPurchasesParams 4com.sirch.mileagetracker.data.billing.BillingManager  ProductDetails 4com.sirch.mileagetracker.data.billing.BillingManager  Purchase 4com.sirch.mileagetracker.data.billing.BillingManager  
PurchaseEvent 4com.sirch.mileagetracker.data.billing.BillingManager  QueryProductDetailsParams 4com.sirch.mileagetracker.data.billing.BillingManager  QueryPurchasesParams 4com.sirch.mileagetracker.data.billing.BillingManager  REMOVE_ADS_PRODUCT_ID 4com.sirch.mileagetracker.data.billing.BillingManager  	StateFlow 4com.sirch.mileagetracker.data.billing.BillingManager  String 4com.sirch.mileagetracker.data.billing.BillingManager  _connectionState 4com.sirch.mileagetracker.data.billing.BillingManager  _productDetails 4com.sirch.mileagetracker.data.billing.BillingManager  _purchaseEvents 4com.sirch.mileagetracker.data.billing.BillingManager  acknowledgePurchase 4com.sirch.mileagetracker.data.billing.BillingManager  
adsRepository 4com.sirch.mileagetracker.data.billing.BillingManager  asStateFlow 4com.sirch.mileagetracker.data.billing.BillingManager  
billingClient 4com.sirch.mileagetracker.data.billing.BillingManager  connectionState 4com.sirch.mileagetracker.data.billing.BillingManager  context 4com.sirch.mileagetracker.data.billing.BillingManager  coroutineScope 4com.sirch.mileagetracker.data.billing.BillingManager  	emptyList 4com.sirch.mileagetracker.data.billing.BillingManager  find 4com.sirch.mileagetracker.data.billing.BillingManager  handlePurchases 4com.sirch.mileagetracker.data.billing.BillingManager  launch 4com.sirch.mileagetracker.data.billing.BillingManager  launchBillingFlow 4com.sirch.mileagetracker.data.billing.BillingManager  let 4com.sirch.mileagetracker.data.billing.BillingManager  listOf 4com.sirch.mileagetracker.data.billing.BillingManager  productDetails 4com.sirch.mileagetracker.data.billing.BillingManager  purchaseEvents 4com.sirch.mileagetracker.data.billing.BillingManager  queryProductDetails 4com.sirch.mileagetracker.data.billing.BillingManager  queryPurchases 4com.sirch.mileagetracker.data.billing.BillingManager  
receiveAsFlow 4com.sirch.mileagetracker.data.billing.BillingManager  resume 4com.sirch.mileagetracker.data.billing.BillingManager  startConnection 4com.sirch.mileagetracker.data.billing.BillingManager  suspendCancellableCoroutine 4com.sirch.mileagetracker.data.billing.BillingManager  BillingConnectionState Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState  	CONNECTED Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState  
CONNECTING Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState  DISCONNECTED Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState  ERROR Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState  String Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState  AcknowledgePurchaseParams >com.sirch.mileagetracker.data.billing.BillingManager.Companion  
BillingClient >com.sirch.mileagetracker.data.billing.BillingManager.Companion  BillingConnectionState >com.sirch.mileagetracker.data.billing.BillingManager.Companion  BillingFlowParams >com.sirch.mileagetracker.data.billing.BillingManager.Companion  
BillingResult >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Channel >com.sirch.mileagetracker.data.billing.BillingManager.Companion  CoroutineScope >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Dispatchers >com.sirch.mileagetracker.data.billing.BillingManager.Companion  INAPP >com.sirch.mileagetracker.data.billing.BillingManager.Companion  MutableStateFlow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  PendingPurchasesParams >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Purchase >com.sirch.mileagetracker.data.billing.BillingManager.Companion  
PurchaseEvent >com.sirch.mileagetracker.data.billing.BillingManager.Companion  QueryProductDetailsParams >com.sirch.mileagetracker.data.billing.BillingManager.Companion  QueryPurchasesParams >com.sirch.mileagetracker.data.billing.BillingManager.Companion  REMOVE_ADS_PRODUCT_ID >com.sirch.mileagetracker.data.billing.BillingManager.Companion  asStateFlow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  	emptyList >com.sirch.mileagetracker.data.billing.BillingManager.Companion  find >com.sirch.mileagetracker.data.billing.BillingManager.Companion  handlePurchases >com.sirch.mileagetracker.data.billing.BillingManager.Companion  launch >com.sirch.mileagetracker.data.billing.BillingManager.Companion  let >com.sirch.mileagetracker.data.billing.BillingManager.Companion  listOf >com.sirch.mileagetracker.data.billing.BillingManager.Companion  
receiveAsFlow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  resume >com.sirch.mileagetracker.data.billing.BillingManager.Companion  suspendCancellableCoroutine >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Purchase Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent  PurchaseCancelled Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent  PurchaseCompleted Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent  
PurchaseEvent Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent  PurchaseFailed Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent  String Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent  error Qcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent.PurchaseFailed  BackupResult #com.sirch.mileagetracker.data.cloud  Boolean #com.sirch.mileagetracker.data.cloud  CloudBackupService #com.sirch.mileagetracker.data.cloud  	Exception #com.sirch.mileagetracker.data.cloud  FirebaseAuth #com.sirch.mileagetracker.data.cloud  Inject #com.sirch.mileagetracker.data.cloud  Int #com.sirch.mileagetracker.data.cloud  
RestoreResult #com.sirch.mileagetracker.data.cloud  SettingsRepository #com.sirch.mileagetracker.data.cloud  	Singleton #com.sirch.mileagetracker.data.cloud  String #com.sirch.mileagetracker.data.cloud  System #com.sirch.mileagetracker.data.cloud  kotlinx #com.sirch.mileagetracker.data.cloud  BackupResult 6com.sirch.mileagetracker.data.cloud.CloudBackupService  Boolean 6com.sirch.mileagetracker.data.cloud.CloudBackupService  	Exception 6com.sirch.mileagetracker.data.cloud.CloudBackupService  FirebaseAuth 6com.sirch.mileagetracker.data.cloud.CloudBackupService  Inject 6com.sirch.mileagetracker.data.cloud.CloudBackupService  Int 6com.sirch.mileagetracker.data.cloud.CloudBackupService  
RestoreResult 6com.sirch.mileagetracker.data.cloud.CloudBackupService  SettingsRepository 6com.sirch.mileagetracker.data.cloud.CloudBackupService  String 6com.sirch.mileagetracker.data.cloud.CloudBackupService  System 6com.sirch.mileagetracker.data.cloud.CloudBackupService  
backupToCloud 6com.sirch.mileagetracker.data.cloud.CloudBackupService  firebaseAuth 6com.sirch.mileagetracker.data.cloud.CloudBackupService  getCurrentUserId 6com.sirch.mileagetracker.data.cloud.CloudBackupService  kotlinx 6com.sirch.mileagetracker.data.cloud.CloudBackupService  restoreFromCloud 6com.sirch.mileagetracker.data.cloud.CloudBackupService  settingsRepository 6com.sirch.mileagetracker.data.cloud.CloudBackupService  BackupResult Ccom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult  Error Ccom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult  String Ccom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult  Success Ccom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult  UserNotLoggedIn Ccom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult  message Icom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult.Error  BackupResult @com.sirch.mileagetracker.data.cloud.CloudBackupService.Companion  
RestoreResult @com.sirch.mileagetracker.data.cloud.CloudBackupService.Companion  System @com.sirch.mileagetracker.data.cloud.CloudBackupService.Companion  kotlinx @com.sirch.mileagetracker.data.cloud.CloudBackupService.Companion  Error Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult  Int Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult  
NoBackupFound Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult  
RestoreResult Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult  String Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult  Success Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult  UserNotLoggedIn Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult  message Jcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult.Error  
programsCount Lcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult.Success  purchasesCount Lcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult.Success  Context #com.sirch.mileagetracker.data.local  Database #com.sirch.mileagetracker.data.local  
DateConverter #com.sirch.mileagetracker.data.local  MileageTrackerDatabase #com.sirch.mileagetracker.data.local  Program #com.sirch.mileagetracker.data.local  
ProgramDao #com.sirch.mileagetracker.data.local  Purchase #com.sirch.mileagetracker.data.local  PurchaseDao #com.sirch.mileagetracker.data.local  Room #com.sirch.mileagetracker.data.local  RoomDatabase #com.sirch.mileagetracker.data.local  Settings #com.sirch.mileagetracker.data.local  SettingsDao #com.sirch.mileagetracker.data.local  TypeConverters #com.sirch.mileagetracker.data.local  Volatile #com.sirch.mileagetracker.data.local  databaseBuilder #com.sirch.mileagetracker.data.local  java #com.sirch.mileagetracker.data.local  synchronized #com.sirch.mileagetracker.data.local  	Companion :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  Context :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  
DATABASE_NAME :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  INSTANCE :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  MileageTrackerDatabase :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  
ProgramDao :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  PurchaseDao :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  Room :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  SettingsDao :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  Volatile :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  databaseBuilder :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  java :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  
programDao :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  purchaseDao :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  settingsDao :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  synchronized :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  
DATABASE_NAME Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  INSTANCE Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  MileageTrackerDatabase Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  Room Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  databaseBuilder Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  java Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  synchronized Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  
DateConverter .com.sirch.mileagetracker.data.local.converters  	LocalDate .com.sirch.mileagetracker.data.local.converters  String .com.sirch.mileagetracker.data.local.converters  
TypeConverter .com.sirch.mileagetracker.data.local.converters  let .com.sirch.mileagetracker.data.local.converters  parse .com.sirch.mileagetracker.data.local.converters  	LocalDate <com.sirch.mileagetracker.data.local.converters.DateConverter  let <com.sirch.mileagetracker.data.local.converters.DateConverter  parse <com.sirch.mileagetracker.data.local.converters.DateConverter  Boolean 'com.sirch.mileagetracker.data.local.dao  Dao 'com.sirch.mileagetracker.data.local.dao  Delete 'com.sirch.mileagetracker.data.local.dao  Double 'com.sirch.mileagetracker.data.local.dao  Flow 'com.sirch.mileagetracker.data.local.dao  GlobalStatistics 'com.sirch.mileagetracker.data.local.dao  Insert 'com.sirch.mileagetracker.data.local.dao  Int 'com.sirch.mileagetracker.data.local.dao  List 'com.sirch.mileagetracker.data.local.dao  Long 'com.sirch.mileagetracker.data.local.dao  OnConflictStrategy 'com.sirch.mileagetracker.data.local.dao  PagingSource 'com.sirch.mileagetracker.data.local.dao  Program 'com.sirch.mileagetracker.data.local.dao  
ProgramDao 'com.sirch.mileagetracker.data.local.dao  ProgramStatistics 'com.sirch.mileagetracker.data.local.dao  ProgramStatisticsWithName 'com.sirch.mileagetracker.data.local.dao  Purchase 'com.sirch.mileagetracker.data.local.dao  PurchaseDao 'com.sirch.mileagetracker.data.local.dao  Query 'com.sirch.mileagetracker.data.local.dao  Settings 'com.sirch.mileagetracker.data.local.dao  SettingsDao 'com.sirch.mileagetracker.data.local.dao  String 'com.sirch.mileagetracker.data.local.dao  Update 'com.sirch.mileagetracker.data.local.dao  averageCostPerMile 8com.sirch.mileagetracker.data.local.dao.GlobalStatistics  	totalCost 8com.sirch.mileagetracker.data.local.dao.GlobalStatistics  
totalMiles 8com.sirch.mileagetracker.data.local.dao.GlobalStatistics  OnConflictStrategy 2com.sirch.mileagetracker.data.local.dao.ProgramDao  
deleteProgram 2com.sirch.mileagetracker.data.local.dao.ProgramDao  getAllActivePrograms 2com.sirch.mileagetracker.data.local.dao.ProgramDao  getProgramById 2com.sirch.mileagetracker.data.local.dao.ProgramDao  getProgramByIdFlow 2com.sirch.mileagetracker.data.local.dao.ProgramDao  getProgramCount 2com.sirch.mileagetracker.data.local.dao.ProgramDao  
insertProgram 2com.sirch.mileagetracker.data.local.dao.ProgramDao  insertPrograms 2com.sirch.mileagetracker.data.local.dao.ProgramDao  
updateProgram 2com.sirch.mileagetracker.data.local.dao.ProgramDao  averageCostPerMile 9com.sirch.mileagetracker.data.local.dao.ProgramStatistics  	totalCost 9com.sirch.mileagetracker.data.local.dao.ProgramStatistics  
totalMiles 9com.sirch.mileagetracker.data.local.dao.ProgramStatistics  averageCostPerMile Acom.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName  	programId Acom.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName  programName Acom.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName  	totalCost Acom.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName  
totalMiles Acom.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName  OnConflictStrategy 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  deletePurchase 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  deletePurchasesByProgram 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  getAllProgramStatistics 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  getAllPurchases 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  getAllPurchasesPaged 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  getGlobalStatistics 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  getProgramStatistics 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  getPurchaseById 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  getPurchasesByProgram 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  getPurchasesByProgramPaged 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  getTotalPurchaseCount 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  insertPurchase 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  updatePurchase 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  OnConflictStrategy 3com.sirch.mileagetracker.data.local.dao.SettingsDao  getSettings 3com.sirch.mileagetracker.data.local.dao.SettingsDao  getSettingsOnce 3com.sirch.mileagetracker.data.local.dao.SettingsDao  insertSettings 3com.sirch.mileagetracker.data.local.dao.SettingsDao  updateAdsRemoved 3com.sirch.mileagetracker.data.local.dao.SettingsDao  updateLastSyncTime 3com.sirch.mileagetracker.data.local.dao.SettingsDao  updateSettings 3com.sirch.mileagetracker.data.local.dao.SettingsDao  updateUserId 3com.sirch.mileagetracker.data.local.dao.SettingsDao  Boolean ,com.sirch.mileagetracker.data.local.entities  Double ,com.sirch.mileagetracker.data.local.entities  Entity ,com.sirch.mileagetracker.data.local.entities  Int ,com.sirch.mileagetracker.data.local.entities  	LocalDate ,com.sirch.mileagetracker.data.local.entities  Long ,com.sirch.mileagetracker.data.local.entities  
PrimaryKey ,com.sirch.mileagetracker.data.local.entities  Program ,com.sirch.mileagetracker.data.local.entities  Purchase ,com.sirch.mileagetracker.data.local.entities  Settings ,com.sirch.mileagetracker.data.local.entities  String ,com.sirch.mileagetracker.data.local.entities  description 4com.sirch.mileagetracker.data.local.entities.Program  id 4com.sirch.mileagetracker.data.local.entities.Program  let 4com.sirch.mileagetracker.data.local.entities.Program  name 4com.sirch.mileagetracker.data.local.entities.Program  cost 5com.sirch.mileagetracker.data.local.entities.Purchase  date 5com.sirch.mileagetracker.data.local.entities.Purchase  description 5com.sirch.mileagetracker.data.local.entities.Purchase  id 5com.sirch.mileagetracker.data.local.entities.Purchase  miles 5com.sirch.mileagetracker.data.local.entities.Purchase  	programId 5com.sirch.mileagetracker.data.local.entities.Purchase  
adsRemoved 5com.sirch.mileagetracker.data.local.entities.Settings  cloudBackupEnabled 5com.sirch.mileagetracker.data.local.entities.Settings  copy 5com.sirch.mileagetracker.data.local.entities.Settings  darkModeEnabled 5com.sirch.mileagetracker.data.local.entities.Settings  lastBackupTimestamp 5com.sirch.mileagetracker.data.local.entities.Settings  
AdsRepository (com.sirch.mileagetracker.data.repository  Boolean (com.sirch.mileagetracker.data.repository  	Exception (com.sirch.mileagetracker.data.repository  Flow (com.sirch.mileagetracker.data.repository  GlobalStatistics (com.sirch.mileagetracker.data.repository  Inject (com.sirch.mileagetracker.data.repository  Int (com.sirch.mileagetracker.data.repository  List (com.sirch.mileagetracker.data.repository  Long (com.sirch.mileagetracker.data.repository  Pager (com.sirch.mileagetracker.data.repository  PagingConfig (com.sirch.mileagetracker.data.repository  
PagingData (com.sirch.mileagetracker.data.repository  Program (com.sirch.mileagetracker.data.repository  
ProgramDao (com.sirch.mileagetracker.data.repository  ProgramRepository (com.sirch.mileagetracker.data.repository  ProgramStatistics (com.sirch.mileagetracker.data.repository  ProgramStatisticsWithName (com.sirch.mileagetracker.data.repository  Purchase (com.sirch.mileagetracker.data.repository  PurchaseDao (com.sirch.mileagetracker.data.repository  PurchaseRepository (com.sirch.mileagetracker.data.repository  Settings (com.sirch.mileagetracker.data.repository  SettingsDao (com.sirch.mileagetracker.data.repository  SettingsRepository (com.sirch.mileagetracker.data.repository  	Singleton (com.sirch.mileagetracker.data.repository  String (com.sirch.mileagetracker.data.repository  listOf (com.sirch.mileagetracker.data.repository  map (com.sirch.mileagetracker.data.repository  
areAdsRemoved 6com.sirch.mileagetracker.data.repository.AdsRepository  getCurrentAdsStatus 6com.sirch.mileagetracker.data.repository.AdsRepository  map 6com.sirch.mileagetracker.data.repository.AdsRepository  	removeAds 6com.sirch.mileagetracker.data.repository.AdsRepository  settingsRepository 6com.sirch.mileagetracker.data.repository.AdsRepository  
shouldShowAds 6com.sirch.mileagetracker.data.repository.AdsRepository  Program :com.sirch.mileagetracker.data.repository.ProgramRepository  getAllActivePrograms :com.sirch.mileagetracker.data.repository.ProgramRepository  getProgramById :com.sirch.mileagetracker.data.repository.ProgramRepository  getProgramByIdFlow :com.sirch.mileagetracker.data.repository.ProgramRepository  getProgramCount :com.sirch.mileagetracker.data.repository.ProgramRepository  insertPrograms :com.sirch.mileagetracker.data.repository.ProgramRepository  listOf :com.sirch.mileagetracker.data.repository.ProgramRepository  
programDao :com.sirch.mileagetracker.data.repository.ProgramRepository  seedDefaultPrograms :com.sirch.mileagetracker.data.repository.ProgramRepository  Pager ;com.sirch.mileagetracker.data.repository.PurchaseRepository  PagingConfig ;com.sirch.mileagetracker.data.repository.PurchaseRepository  deletePurchase ;com.sirch.mileagetracker.data.repository.PurchaseRepository  getAllProgramStatistics ;com.sirch.mileagetracker.data.repository.PurchaseRepository  getGlobalStatistics ;com.sirch.mileagetracker.data.repository.PurchaseRepository  getProgramStatistics ;com.sirch.mileagetracker.data.repository.PurchaseRepository  getPurchaseById ;com.sirch.mileagetracker.data.repository.PurchaseRepository  getPurchasesByProgram ;com.sirch.mileagetracker.data.repository.PurchaseRepository  insertPurchase ;com.sirch.mileagetracker.data.repository.PurchaseRepository  purchaseDao ;com.sirch.mileagetracker.data.repository.PurchaseRepository  updatePurchase ;com.sirch.mileagetracker.data.repository.PurchaseRepository  Settings ;com.sirch.mileagetracker.data.repository.SettingsRepository  getCurrentSettings ;com.sirch.mileagetracker.data.repository.SettingsRepository  getSettings ;com.sirch.mileagetracker.data.repository.SettingsRepository  getSettingsOnce ;com.sirch.mileagetracker.data.repository.SettingsRepository  initializeSettings ;com.sirch.mileagetracker.data.repository.SettingsRepository  insertSettings ;com.sirch.mileagetracker.data.repository.SettingsRepository  settingsDao ;com.sirch.mileagetracker.data.repository.SettingsRepository  updateAdsRemoved ;com.sirch.mileagetracker.data.repository.SettingsRepository  updateDarkMode ;com.sirch.mileagetracker.data.repository.SettingsRepository  updateLastBackupTimestamp ;com.sirch.mileagetracker.data.repository.SettingsRepository  updateSettings ;com.sirch.mileagetracker.data.repository.SettingsRepository  
AdsRepository com.sirch.mileagetracker.di  ApplicationContext com.sirch.mileagetracker.di  AuthRepository com.sirch.mileagetracker.di  BillingManager com.sirch.mileagetracker.di  CloudBackupService com.sirch.mileagetracker.di  Context com.sirch.mileagetracker.di  DatabaseModule com.sirch.mileagetracker.di  Firebase com.sirch.mileagetracker.di  FirebaseAnalytics com.sirch.mileagetracker.di  FirebaseAuth com.sirch.mileagetracker.di  FirebaseModule com.sirch.mileagetracker.di  	InstallIn com.sirch.mileagetracker.di  MileageTrackerDatabase com.sirch.mileagetracker.di  Module com.sirch.mileagetracker.di  
ProgramDao com.sirch.mileagetracker.di  ProgramRepository com.sirch.mileagetracker.di  Provides com.sirch.mileagetracker.di  PurchaseDao com.sirch.mileagetracker.di  PurchaseRepository com.sirch.mileagetracker.di  RepositoryModule com.sirch.mileagetracker.di  Room com.sirch.mileagetracker.di  SettingsDao com.sirch.mileagetracker.di  SettingsRepository com.sirch.mileagetracker.di  	Singleton com.sirch.mileagetracker.di  SingletonComponent com.sirch.mileagetracker.di  databaseBuilder com.sirch.mileagetracker.di  java com.sirch.mileagetracker.di  MileageTrackerDatabase *com.sirch.mileagetracker.di.DatabaseModule  Room *com.sirch.mileagetracker.di.DatabaseModule  databaseBuilder *com.sirch.mileagetracker.di.DatabaseModule  java *com.sirch.mileagetracker.di.DatabaseModule  Firebase *com.sirch.mileagetracker.di.FirebaseModule  	analytics *com.sirch.mileagetracker.di.FirebaseModule  auth *com.sirch.mileagetracker.di.FirebaseModule  
AdsRepository ,com.sirch.mileagetracker.di.RepositoryModule  AuthRepository ,com.sirch.mileagetracker.di.RepositoryModule  BillingManager ,com.sirch.mileagetracker.di.RepositoryModule  CloudBackupService ,com.sirch.mileagetracker.di.RepositoryModule  ProgramRepository ,com.sirch.mileagetracker.di.RepositoryModule  PurchaseRepository ,com.sirch.mileagetracker.di.RepositoryModule  SettingsRepository ,com.sirch.mileagetracker.di.RepositoryModule  InitializeAppUseCase 'com.sirch.mileagetracker.domain.usecase  Inject 'com.sirch.mileagetracker.domain.usecase  ProgramRepository 'com.sirch.mileagetracker.domain.usecase  PurchaseRepository 'com.sirch.mileagetracker.domain.usecase  SettingsRepository 'com.sirch.mileagetracker.domain.usecase  invoke <com.sirch.mileagetracker.domain.usecase.InitializeAppUseCase  programRepository <com.sirch.mileagetracker.domain.usecase.InitializeAppUseCase  settingsRepository <com.sirch.mileagetracker.domain.usecase.InitializeAppUseCase  Activity )com.sirch.mileagetracker.presentation.ads  AdBanner )com.sirch.mileagetracker.presentation.ads  AdBannerWithCallback )com.sirch.mileagetracker.presentation.ads  AdError )com.sirch.mileagetracker.presentation.ads  AdEvent )com.sirch.mileagetracker.presentation.ads  	AdRequest )com.sirch.mileagetracker.presentation.ads  AdSize )com.sirch.mileagetracker.presentation.ads  	AdUnitIds )com.sirch.mileagetracker.presentation.ads  Boolean )com.sirch.mileagetracker.presentation.ads  BuildConfig )com.sirch.mileagetracker.presentation.ads  Channel )com.sirch.mileagetracker.presentation.ads  
Composable )com.sirch.mileagetracker.presentation.ads  Context )com.sirch.mileagetracker.presentation.ads  DisposableEffect )com.sirch.mileagetracker.presentation.ads  FullScreenContentCallback )com.sirch.mileagetracker.presentation.ads  InterstitialAd )com.sirch.mileagetracker.presentation.ads  InterstitialAdEffect )com.sirch.mileagetracker.presentation.ads  InterstitialAdLoadCallback )com.sirch.mileagetracker.presentation.ads  InterstitialAdManager )com.sirch.mileagetracker.presentation.ads  LaunchedEffect )com.sirch.mileagetracker.presentation.ads  LoadAdError )com.sirch.mileagetracker.presentation.ads  Modifier )com.sirch.mileagetracker.presentation.ads  String )com.sirch.mileagetracker.presentation.ads  Unit )com.sirch.mileagetracker.presentation.ads  	_adEvents )com.sirch.mileagetracker.presentation.ads  apply )com.sirch.mileagetracker.presentation.ads  com )com.sirch.mileagetracker.presentation.ads  interstitialAd )com.sirch.mileagetracker.presentation.ads  	isLoading )com.sirch.mileagetracker.presentation.ads  loadAd )com.sirch.mileagetracker.presentation.ads  
receiveAsFlow )com.sirch.mileagetracker.presentation.ads  remember )com.sirch.mileagetracker.presentation.ads  rememberInterstitialAdManager )com.sirch.mileagetracker.presentation.ads  	BANNER_ID 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  BANNER_PROD 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  BANNER_TEST 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  BuildConfig 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  INTERSTITIAL_ID 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  INTERSTITIAL_PROD 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  INTERSTITIAL_TEST 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  IS_DEBUG 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  Activity ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  AdError ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  AdEvent ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  	AdRequest ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  Boolean ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  Channel ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  Context ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  FullScreenContentCallback ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  InterstitialAd ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  InterstitialAdLoadCallback ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  LoadAdError ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  String ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  	_adEvents ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  adEvents ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  adUnitId ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  context ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  destroy ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  interstitialAd ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  	isLoading ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  loadAd ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  
receiveAsFlow ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  showAd ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  showAds ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  AdDismissed Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent  AdError Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent  AdEvent Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent  AdFailedToLoad Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent  AdFailedToShow Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent  AdLoaded Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent  AdShown Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent  LoadAdError Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent  error Vcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent.AdFailedToLoad  error Vcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent.AdFailedToShow  google -com.sirch.mileagetracker.presentation.ads.com  android 4com.sirch.mileagetracker.presentation.ads.com.google  gms <com.sirch.mileagetracker.presentation.ads.com.google.android  ads @com.sirch.mileagetracker.presentation.ads.com.google.android.gms  
AdListener Dcom.sirch.mileagetracker.presentation.ads.com.google.android.gms.ads  Activity *com.sirch.mileagetracker.presentation.auth  ActivityResultContracts *com.sirch.mileagetracker.presentation.auth  Add *com.sirch.mileagetracker.presentation.auth  	Alignment *com.sirch.mileagetracker.presentation.auth  AnimatedVisibility *com.sirch.mileagetracker.presentation.auth  ApiException *com.sirch.mileagetracker.presentation.auth  Arrangement *com.sirch.mileagetracker.presentation.auth  AuthRepository *com.sirch.mileagetracker.presentation.auth  AuthUiState *com.sirch.mileagetracker.presentation.auth  
AuthViewModel *com.sirch.mileagetracker.presentation.auth  BenefitItem *com.sirch.mileagetracker.presentation.auth  BenefitsSection *com.sirch.mileagetracker.presentation.auth  Boolean *com.sirch.mileagetracker.presentation.auth  Box *com.sirch.mileagetracker.presentation.auth  Brush *com.sirch.mileagetracker.presentation.auth  Button *com.sirch.mileagetracker.presentation.auth  ButtonDefaults *com.sirch.mileagetracker.presentation.auth  Card *com.sirch.mileagetracker.presentation.auth  CardDefaults *com.sirch.mileagetracker.presentation.auth  Check *com.sirch.mileagetracker.presentation.auth  CircularProgressIndicator *com.sirch.mileagetracker.presentation.auth  Column *com.sirch.mileagetracker.presentation.auth  
Composable *com.sirch.mileagetracker.presentation.auth  	ErrorCard *com.sirch.mileagetracker.presentation.auth  ExperimentalMaterial3Api *com.sirch.mileagetracker.presentation.auth  FirebaseUser *com.sirch.mileagetracker.presentation.auth  
FontWeight *com.sirch.mileagetracker.presentation.auth  GoogleSignIn *com.sirch.mileagetracker.presentation.auth  GoogleSignInButton *com.sirch.mileagetracker.presentation.auth  GoogleSignInOptions *com.sirch.mileagetracker.presentation.auth  
HiltViewModel *com.sirch.mileagetracker.presentation.auth  Home *com.sirch.mileagetracker.presentation.auth  Icon *com.sirch.mileagetracker.presentation.auth  Icons *com.sirch.mileagetracker.presentation.auth  ImageVector *com.sirch.mileagetracker.presentation.auth  Inject *com.sirch.mileagetracker.presentation.auth  LaunchedEffect *com.sirch.mileagetracker.presentation.auth  LoginScreen *com.sirch.mileagetracker.presentation.auth  
MaterialTheme *com.sirch.mileagetracker.presentation.auth  Modifier *com.sirch.mileagetracker.presentation.auth  MutableStateFlow *com.sirch.mileagetracker.presentation.auth  OptIn *com.sirch.mileagetracker.presentation.auth  R *com.sirch.mileagetracker.presentation.auth  RoundedCornerShape *com.sirch.mileagetracker.presentation.auth  Row *com.sirch.mileagetracker.presentation.auth  Settings *com.sirch.mileagetracker.presentation.auth  Spacer *com.sirch.mileagetracker.presentation.auth  Star *com.sirch.mileagetracker.presentation.auth  	StateFlow *com.sirch.mileagetracker.presentation.auth  String *com.sirch.mileagetracker.presentation.auth  Suppress *com.sirch.mileagetracker.presentation.auth  Text *com.sirch.mileagetracker.presentation.auth  	TextAlign *com.sirch.mileagetracker.presentation.auth  Unit *com.sirch.mileagetracker.presentation.auth  	ViewModel *com.sirch.mileagetracker.presentation.auth  _uiState *com.sirch.mileagetracker.presentation.auth  asStateFlow *com.sirch.mileagetracker.presentation.auth  authRepository *com.sirch.mileagetracker.presentation.auth  
background *com.sirch.mileagetracker.presentation.auth  buttonColors *com.sirch.mileagetracker.presentation.auth  
cardColors *com.sirch.mileagetracker.presentation.auth  collectAsState *com.sirch.mileagetracker.presentation.auth  fadeIn *com.sirch.mileagetracker.presentation.auth  fadeOut *com.sirch.mileagetracker.presentation.auth  fillMaxSize *com.sirch.mileagetracker.presentation.auth  fillMaxWidth *com.sirch.mileagetracker.presentation.auth  getValue *com.sirch.mileagetracker.presentation.auth  height *com.sirch.mileagetracker.presentation.auth  java *com.sirch.mileagetracker.presentation.auth  launch *com.sirch.mileagetracker.presentation.auth  let *com.sirch.mileagetracker.presentation.auth  listOf *com.sirch.mileagetracker.presentation.auth  	onFailure *com.sirch.mileagetracker.presentation.auth  	onSuccess *com.sirch.mileagetracker.presentation.auth  padding *com.sirch.mileagetracker.presentation.auth  provideDelegate *com.sirch.mileagetracker.presentation.auth  remember *com.sirch.mileagetracker.presentation.auth  rememberScrollState *com.sirch.mileagetracker.presentation.auth  size *com.sirch.mileagetracker.presentation.auth  slideInVertically *com.sirch.mileagetracker.presentation.auth  slideOutVertically *com.sirch.mileagetracker.presentation.auth  stringResource *com.sirch.mileagetracker.presentation.auth  verticalGradient *com.sirch.mileagetracker.presentation.auth  verticalScroll *com.sirch.mileagetracker.presentation.auth  width *com.sirch.mileagetracker.presentation.auth  copy 6com.sirch.mileagetracker.presentation.auth.AuthUiState  error 6com.sirch.mileagetracker.presentation.auth.AuthUiState  	isLoading 6com.sirch.mileagetracker.presentation.auth.AuthUiState  
isSignedIn 6com.sirch.mileagetracker.presentation.auth.AuthUiState  AuthUiState 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  MutableStateFlow 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  _uiState 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  asStateFlow 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  authRepository 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  checkAuthState 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  launch 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  	onFailure 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  	onSuccess 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  setError 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  signInWithGoogle 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  uiState 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  viewModelScope 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  AdBanner *com.sirch.mileagetracker.presentation.home  	AdUnitIds *com.sirch.mileagetracker.presentation.home  
AdsRepository *com.sirch.mileagetracker.presentation.home  	Alignment *com.sirch.mileagetracker.presentation.home  Arrangement *com.sirch.mileagetracker.presentation.home  Boolean *com.sirch.mileagetracker.presentation.home  Box *com.sirch.mileagetracker.presentation.home  Card *com.sirch.mileagetracker.presentation.home  CardDefaults *com.sirch.mileagetracker.presentation.home  CircularProgressIndicator *com.sirch.mileagetracker.presentation.home  Column *com.sirch.mileagetracker.presentation.home  
Composable *com.sirch.mileagetracker.presentation.home  Double *com.sirch.mileagetracker.presentation.home  	Exception *com.sirch.mileagetracker.presentation.home  ExperimentalMaterial3Api *com.sirch.mileagetracker.presentation.home  FloatingActionButton *com.sirch.mileagetracker.presentation.home  
FontWeight *com.sirch.mileagetracker.presentation.home  
HiltViewModel *com.sirch.mileagetracker.presentation.home  
HomeScreen *com.sirch.mileagetracker.presentation.home  HomeUiState *com.sirch.mileagetracker.presentation.home  
HomeViewModel *com.sirch.mileagetracker.presentation.home  Icon *com.sirch.mileagetracker.presentation.home  
IconButton *com.sirch.mileagetracker.presentation.home  Icons *com.sirch.mileagetracker.presentation.home  Inject *com.sirch.mileagetracker.presentation.home  Int *com.sirch.mileagetracker.presentation.home  
LazyColumn *com.sirch.mileagetracker.presentation.home  List *com.sirch.mileagetracker.presentation.home  Long *com.sirch.mileagetracker.presentation.home  
MaterialTheme *com.sirch.mileagetracker.presentation.home  Modifier *com.sirch.mileagetracker.presentation.home  MutableStateFlow *com.sirch.mileagetracker.presentation.home  OptIn *com.sirch.mileagetracker.presentation.home  ProgramCard *com.sirch.mileagetracker.presentation.home  ProgramStatisticsWithName *com.sirch.mileagetracker.presentation.home  PurchaseRepository *com.sirch.mileagetracker.presentation.home  R *com.sirch.mileagetracker.presentation.home  Row *com.sirch.mileagetracker.presentation.home  Scaffold *com.sirch.mileagetracker.presentation.home  Spacer *com.sirch.mileagetracker.presentation.home  	StateFlow *com.sirch.mileagetracker.presentation.home  String *com.sirch.mileagetracker.presentation.home  Text *com.sirch.mileagetracker.presentation.home  	TopAppBar *com.sirch.mileagetracker.presentation.home  Unit *com.sirch.mileagetracker.presentation.home  	ViewModel *com.sirch.mileagetracker.presentation.home  _uiState *com.sirch.mileagetracker.presentation.home  
adsRepository *com.sirch.mileagetracker.presentation.home  asStateFlow *com.sirch.mileagetracker.presentation.home  
cardColors *com.sirch.mileagetracker.presentation.home  catch *com.sirch.mileagetracker.presentation.home  collectAsState *com.sirch.mileagetracker.presentation.home  com *com.sirch.mileagetracker.presentation.home  	emptyList *com.sirch.mileagetracker.presentation.home  fillMaxSize *com.sirch.mileagetracker.presentation.home  fillMaxWidth *com.sirch.mileagetracker.presentation.home  format *com.sirch.mileagetracker.presentation.home  getValue *com.sirch.mileagetracker.presentation.home  height *com.sirch.mileagetracker.presentation.home  launch *com.sirch.mileagetracker.presentation.home  padding *com.sirch.mileagetracker.presentation.home  provideDelegate *com.sirch.mileagetracker.presentation.home  purchaseRepository *com.sirch.mileagetracker.presentation.home  spacedBy *com.sirch.mileagetracker.presentation.home  stringResource *com.sirch.mileagetracker.presentation.home  sumOf *com.sirch.mileagetracker.presentation.home  averageCostPerMile 6com.sirch.mileagetracker.presentation.home.HomeUiState  copy 6com.sirch.mileagetracker.presentation.home.HomeUiState  error 6com.sirch.mileagetracker.presentation.home.HomeUiState  	isLoading 6com.sirch.mileagetracker.presentation.home.HomeUiState  programStatistics 6com.sirch.mileagetracker.presentation.home.HomeUiState  showAds 6com.sirch.mileagetracker.presentation.home.HomeUiState  
totalMiles 6com.sirch.mileagetracker.presentation.home.HomeUiState  
totalSpent 6com.sirch.mileagetracker.presentation.home.HomeUiState  HomeUiState 8com.sirch.mileagetracker.presentation.home.HomeViewModel  MutableStateFlow 8com.sirch.mileagetracker.presentation.home.HomeViewModel  _uiState 8com.sirch.mileagetracker.presentation.home.HomeViewModel  
adsRepository 8com.sirch.mileagetracker.presentation.home.HomeViewModel  asStateFlow 8com.sirch.mileagetracker.presentation.home.HomeViewModel  catch 8com.sirch.mileagetracker.presentation.home.HomeViewModel  launch 8com.sirch.mileagetracker.presentation.home.HomeViewModel  
loadAdsStatus 8com.sirch.mileagetracker.presentation.home.HomeViewModel  loadProgramStatistics 8com.sirch.mileagetracker.presentation.home.HomeViewModel  purchaseRepository 8com.sirch.mileagetracker.presentation.home.HomeViewModel  sumOf 8com.sirch.mileagetracker.presentation.home.HomeViewModel  uiState 8com.sirch.mileagetracker.presentation.home.HomeViewModel  viewModelScope 8com.sirch.mileagetracker.presentation.home.HomeViewModel  sirch .com.sirch.mileagetracker.presentation.home.com  mileagetracker 4com.sirch.mileagetracker.presentation.home.com.sirch  data Ccom.sirch.mileagetracker.presentation.home.com.sirch.mileagetracker  local Hcom.sirch.mileagetracker.presentation.home.com.sirch.mileagetracker.data  dao Ncom.sirch.mileagetracker.presentation.home.com.sirch.mileagetracker.data.local  ProgramStatisticsWithName Rcom.sirch.mileagetracker.presentation.home.com.sirch.mileagetracker.data.local.dao  
AuthViewModel 0com.sirch.mileagetracker.presentation.navigation  
Composable 0com.sirch.mileagetracker.presentation.navigation  
HomeScreen 0com.sirch.mileagetracker.presentation.navigation  LoginScreen 0com.sirch.mileagetracker.presentation.navigation  MileageTrackerApp 0com.sirch.mileagetracker.presentation.navigation  ProgramDetailsScreen 0com.sirch.mileagetracker.presentation.navigation  PurchaseFormScreen 0com.sirch.mileagetracker.presentation.navigation  SettingsScreen 0com.sirch.mileagetracker.presentation.navigation  SplashScreen 0com.sirch.mileagetracker.presentation.navigation  String 0com.sirch.mileagetracker.presentation.navigation  androidx 0com.sirch.mileagetracker.presentation.navigation  collectAsState 0com.sirch.mileagetracker.presentation.navigation  getValue 0com.sirch.mileagetracker.presentation.navigation  provideDelegate 0com.sirch.mileagetracker.presentation.navigation  toLongOrNull 0com.sirch.mileagetracker.presentation.navigation  activity 9com.sirch.mileagetracker.presentation.navigation.androidx  ComponentActivity Bcom.sirch.mileagetracker.presentation.navigation.androidx.activity  	Alignment -com.sirch.mileagetracker.presentation.program  Arrangement -com.sirch.mileagetracker.presentation.program  Boolean -com.sirch.mileagetracker.presentation.program  Box -com.sirch.mileagetracker.presentation.program  Card -com.sirch.mileagetracker.presentation.program  CardDefaults -com.sirch.mileagetracker.presentation.program  CircularProgressIndicator -com.sirch.mileagetracker.presentation.program  Column -com.sirch.mileagetracker.presentation.program  
Composable -com.sirch.mileagetracker.presentation.program  	Exception -com.sirch.mileagetracker.presentation.program  ExperimentalMaterial3Api -com.sirch.mileagetracker.presentation.program  FloatingActionButton -com.sirch.mileagetracker.presentation.program  
FontWeight -com.sirch.mileagetracker.presentation.program  
HiltViewModel -com.sirch.mileagetracker.presentation.program  Icon -com.sirch.mileagetracker.presentation.program  
IconButton -com.sirch.mileagetracker.presentation.program  Icons -com.sirch.mileagetracker.presentation.program  Inject -com.sirch.mileagetracker.presentation.program  LaunchedEffect -com.sirch.mileagetracker.presentation.program  
LazyColumn -com.sirch.mileagetracker.presentation.program  List -com.sirch.mileagetracker.presentation.program  Long -com.sirch.mileagetracker.presentation.program  
MaterialTheme -com.sirch.mileagetracker.presentation.program  Modifier -com.sirch.mileagetracker.presentation.program  MutableStateFlow -com.sirch.mileagetracker.presentation.program  OptIn -com.sirch.mileagetracker.presentation.program  Program -com.sirch.mileagetracker.presentation.program  ProgramDetailsScreen -com.sirch.mileagetracker.presentation.program  ProgramDetailsUiState -com.sirch.mileagetracker.presentation.program  ProgramDetailsViewModel -com.sirch.mileagetracker.presentation.program  ProgramRepository -com.sirch.mileagetracker.presentation.program  ProgramStatistics -com.sirch.mileagetracker.presentation.program  Purchase -com.sirch.mileagetracker.presentation.program  PurchaseCard -com.sirch.mileagetracker.presentation.program  PurchaseRepository -com.sirch.mileagetracker.presentation.program  R -com.sirch.mileagetracker.presentation.program  Row -com.sirch.mileagetracker.presentation.program  Scaffold -com.sirch.mileagetracker.presentation.program  Spacer -com.sirch.mileagetracker.presentation.program  	StateFlow -com.sirch.mileagetracker.presentation.program  
StatisticItem -com.sirch.mileagetracker.presentation.program  String -com.sirch.mileagetracker.presentation.program  Text -com.sirch.mileagetracker.presentation.program  	TopAppBar -com.sirch.mileagetracker.presentation.program  Triple -com.sirch.mileagetracker.presentation.program  Unit -com.sirch.mileagetracker.presentation.program  	ViewModel -com.sirch.mileagetracker.presentation.program  _uiState -com.sirch.mileagetracker.presentation.program  asStateFlow -com.sirch.mileagetracker.presentation.program  
cardColors -com.sirch.mileagetracker.presentation.program  catch -com.sirch.mileagetracker.presentation.program  collectAsState -com.sirch.mileagetracker.presentation.program  combine -com.sirch.mileagetracker.presentation.program  	emptyList -com.sirch.mileagetracker.presentation.program  fillMaxSize -com.sirch.mileagetracker.presentation.program  fillMaxWidth -com.sirch.mileagetracker.presentation.program  format -com.sirch.mileagetracker.presentation.program  getValue -com.sirch.mileagetracker.presentation.program  height -com.sirch.mileagetracker.presentation.program  
isNotEmpty -com.sirch.mileagetracker.presentation.program  launch -com.sirch.mileagetracker.presentation.program  let -com.sirch.mileagetracker.presentation.program  padding -com.sirch.mileagetracker.presentation.program  programRepository -com.sirch.mileagetracker.presentation.program  provideDelegate -com.sirch.mileagetracker.presentation.program  purchaseRepository -com.sirch.mileagetracker.presentation.program  remember -com.sirch.mileagetracker.presentation.program  spacedBy -com.sirch.mileagetracker.presentation.program  stringResource -com.sirch.mileagetracker.presentation.program  weight -com.sirch.mileagetracker.presentation.program  copy Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  error Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  	isLoading Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  program Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  	purchases Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  
statistics Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  MutableStateFlow Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  ProgramDetailsUiState Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  Triple Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  _uiState Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  asStateFlow Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  catch Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  combine Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  
isNotEmpty Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  launch Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  loadProgramDetails Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  programRepository Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  purchaseRepository Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  uiState Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  viewModelScope Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  AlertDialog .com.sirch.mileagetracker.presentation.purchase  	Alignment .com.sirch.mileagetracker.presentation.purchase  Arrangement .com.sirch.mileagetracker.presentation.purchase  Boolean .com.sirch.mileagetracker.presentation.purchase  Box .com.sirch.mileagetracker.presentation.purchase  Button .com.sirch.mileagetracker.presentation.purchase  Card .com.sirch.mileagetracker.presentation.purchase  CardDefaults .com.sirch.mileagetracker.presentation.purchase  CircularProgressIndicator .com.sirch.mileagetracker.presentation.purchase  Clock .com.sirch.mileagetracker.presentation.purchase  Column .com.sirch.mileagetracker.presentation.purchase  
Composable .com.sirch.mileagetracker.presentation.purchase  	DateField .com.sirch.mileagetracker.presentation.purchase  
DatePicker .com.sirch.mileagetracker.presentation.purchase  DatePickerDialog .com.sirch.mileagetracker.presentation.purchase  DropdownMenuItem .com.sirch.mileagetracker.presentation.purchase  	Exception .com.sirch.mileagetracker.presentation.purchase  ExperimentalMaterial3Api .com.sirch.mileagetracker.presentation.purchase  ExposedDropdownMenuBox .com.sirch.mileagetracker.presentation.purchase  ExposedDropdownMenuDefaults .com.sirch.mileagetracker.presentation.purchase  
HiltViewModel .com.sirch.mileagetracker.presentation.purchase  Icon .com.sirch.mileagetracker.presentation.purchase  
IconButton .com.sirch.mileagetracker.presentation.purchase  Icons .com.sirch.mileagetracker.presentation.purchase  Inject .com.sirch.mileagetracker.presentation.purchase  Instant .com.sirch.mileagetracker.presentation.purchase  KeyboardOptions .com.sirch.mileagetracker.presentation.purchase  KeyboardType .com.sirch.mileagetracker.presentation.purchase  LaunchedEffect .com.sirch.mileagetracker.presentation.purchase  List .com.sirch.mileagetracker.presentation.purchase  	LocalDate .com.sirch.mileagetracker.presentation.purchase  Long .com.sirch.mileagetracker.presentation.purchase  
MaterialTheme .com.sirch.mileagetracker.presentation.purchase  Modifier .com.sirch.mileagetracker.presentation.purchase  MutableStateFlow .com.sirch.mileagetracker.presentation.purchase  OptIn .com.sirch.mileagetracker.presentation.purchase  OutlinedTextField .com.sirch.mileagetracker.presentation.purchase  Program .com.sirch.mileagetracker.presentation.purchase  ProgramDropdown .com.sirch.mileagetracker.presentation.purchase  ProgramRepository .com.sirch.mileagetracker.presentation.purchase  Purchase .com.sirch.mileagetracker.presentation.purchase  PurchaseFormScreen .com.sirch.mileagetracker.presentation.purchase  PurchaseFormUiState .com.sirch.mileagetracker.presentation.purchase  PurchaseFormViewModel .com.sirch.mileagetracker.presentation.purchase  PurchaseRepository .com.sirch.mileagetracker.presentation.purchase  R .com.sirch.mileagetracker.presentation.purchase  Scaffold .com.sirch.mileagetracker.presentation.purchase  Spacer .com.sirch.mileagetracker.presentation.purchase  	StateFlow .com.sirch.mileagetracker.presentation.purchase  String .com.sirch.mileagetracker.presentation.purchase  Text .com.sirch.mileagetracker.presentation.purchase  
TextButton .com.sirch.mileagetracker.presentation.purchase  TimeZone .com.sirch.mileagetracker.presentation.purchase  	TopAppBar .com.sirch.mileagetracker.presentation.purchase  TrailingIcon .com.sirch.mileagetracker.presentation.purchase  Unit .com.sirch.mileagetracker.presentation.purchase  	ViewModel .com.sirch.mileagetracker.presentation.purchase  _uiState .com.sirch.mileagetracker.presentation.purchase  asStateFlow .com.sirch.mileagetracker.presentation.purchase  
cardColors .com.sirch.mileagetracker.presentation.purchase  collectAsState .com.sirch.mileagetracker.presentation.purchase  currentPurchaseId .com.sirch.mileagetracker.presentation.purchase  currentSystemDefault .com.sirch.mileagetracker.presentation.purchase  	emptyList .com.sirch.mileagetracker.presentation.purchase  fillMaxSize .com.sirch.mileagetracker.presentation.purchase  fillMaxWidth .com.sirch.mileagetracker.presentation.purchase  first .com.sirch.mileagetracker.presentation.purchase  forEach .com.sirch.mileagetracker.presentation.purchase  fromEpochMilliseconds .com.sirch.mileagetracker.presentation.purchase  getValue .com.sirch.mileagetracker.presentation.purchase  height .com.sirch.mileagetracker.presentation.purchase  isBlank .com.sirch.mileagetracker.presentation.purchase  
isNotBlank .com.sirch.mileagetracker.presentation.purchase  launch .com.sirch.mileagetracker.presentation.purchase  let .com.sirch.mileagetracker.presentation.purchase  mutableStateOf .com.sirch.mileagetracker.presentation.purchase  padding .com.sirch.mileagetracker.presentation.purchase  programRepository .com.sirch.mileagetracker.presentation.purchase  provideDelegate .com.sirch.mileagetracker.presentation.purchase  purchaseRepository .com.sirch.mileagetracker.presentation.purchase  remember .com.sirch.mileagetracker.presentation.purchase  rememberDatePickerState .com.sirch.mileagetracker.presentation.purchase  setValue .com.sirch.mileagetracker.presentation.purchase  size .com.sirch.mileagetracker.presentation.purchase  spacedBy .com.sirch.mileagetracker.presentation.purchase  stringResource .com.sirch.mileagetracker.presentation.purchase  takeIf .com.sirch.mileagetracker.presentation.purchase  toDouble .com.sirch.mileagetracker.presentation.purchase  toDoubleOrNull .com.sirch.mileagetracker.presentation.purchase  toInt .com.sirch.mileagetracker.presentation.purchase  toIntOrNull .com.sirch.mileagetracker.presentation.purchase  todayIn .com.sirch.mileagetracker.presentation.purchase  
updateCost .com.sirch.mileagetracker.presentation.purchase  updateDescription .com.sirch.mileagetracker.presentation.purchase  updateMiles .com.sirch.mileagetracker.presentation.purchase  width .com.sirch.mileagetracker.presentation.purchase  copy Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  cost Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  	costError Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  date Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  description Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  error Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  	isLoading Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  isOperationComplete Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  isSaving Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  miles Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  
milesError Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  programs Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  selectedProgram Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  showDeleteConfirmation Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  Clock Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  MutableStateFlow Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  Purchase Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  PurchaseFormUiState Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  TimeZone Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  _uiState Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  asStateFlow Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  currentPurchaseId Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  currentSystemDefault Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  deletePurchase Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  first Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  hideDeleteConfirmation Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  initializeForProgram Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  isBlank Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  
isNotBlank Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  launch Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  loadPrograms Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  loadPurchase Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  programRepository Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  purchaseRepository Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  savePurchase Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  
selectDate Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  
selectProgram Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  showDeleteConfirmation Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  takeIf Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  toDouble Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  toDoubleOrNull Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  toInt Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  toIntOrNull Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  todayIn Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  uiState Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  
updateCost Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  updateDescription Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  updateMiles Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  validateForm Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  viewModelScope Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  Activity .com.sirch.mileagetracker.presentation.settings  
AdsRepository .com.sirch.mileagetracker.presentation.settings  	Alignment .com.sirch.mileagetracker.presentation.settings  Arrangement .com.sirch.mileagetracker.presentation.settings  AuthRepository .com.sirch.mileagetracker.presentation.settings  
BillingClient .com.sirch.mileagetracker.presentation.settings  BillingManager .com.sirch.mileagetracker.presentation.settings  Boolean .com.sirch.mileagetracker.presentation.settings  Button .com.sirch.mileagetracker.presentation.settings  Card .com.sirch.mileagetracker.presentation.settings  CardDefaults .com.sirch.mileagetracker.presentation.settings  CircularProgressIndicator .com.sirch.mileagetracker.presentation.settings  CloudBackupService .com.sirch.mileagetracker.presentation.settings  Column .com.sirch.mileagetracker.presentation.settings  
Composable .com.sirch.mileagetracker.presentation.settings  	Exception .com.sirch.mileagetracker.presentation.settings  ExperimentalMaterial3Api .com.sirch.mileagetracker.presentation.settings  
FontWeight .com.sirch.mileagetracker.presentation.settings  
HiltViewModel .com.sirch.mileagetracker.presentation.settings  Icon .com.sirch.mileagetracker.presentation.settings  
IconButton .com.sirch.mileagetracker.presentation.settings  Icons .com.sirch.mileagetracker.presentation.settings  Inject .com.sirch.mileagetracker.presentation.settings  
MaterialTheme .com.sirch.mileagetracker.presentation.settings  Modifier .com.sirch.mileagetracker.presentation.settings  MutableStateFlow .com.sirch.mileagetracker.presentation.settings  OptIn .com.sirch.mileagetracker.presentation.settings  OutlinedButton .com.sirch.mileagetracker.presentation.settings  Pair .com.sirch.mileagetracker.presentation.settings  R .com.sirch.mileagetracker.presentation.settings  Row .com.sirch.mileagetracker.presentation.settings  Scaffold .com.sirch.mileagetracker.presentation.settings  SettingsRepository .com.sirch.mileagetracker.presentation.settings  SettingsScreen .com.sirch.mileagetracker.presentation.settings  SettingsUiState .com.sirch.mileagetracker.presentation.settings  SettingsViewModel .com.sirch.mileagetracker.presentation.settings  Spacer .com.sirch.mileagetracker.presentation.settings  	StateFlow .com.sirch.mileagetracker.presentation.settings  String .com.sirch.mileagetracker.presentation.settings  Switch .com.sirch.mileagetracker.presentation.settings  Text .com.sirch.mileagetracker.presentation.settings  	TopAppBar .com.sirch.mileagetracker.presentation.settings  Unit .com.sirch.mileagetracker.presentation.settings  	ViewModel .com.sirch.mileagetracker.presentation.settings  _uiState .com.sirch.mileagetracker.presentation.settings  
adsRepository .com.sirch.mileagetracker.presentation.settings  androidx .com.sirch.mileagetracker.presentation.settings  asStateFlow .com.sirch.mileagetracker.presentation.settings  authRepository .com.sirch.mileagetracker.presentation.settings  billingManager .com.sirch.mileagetracker.presentation.settings  
cardColors .com.sirch.mileagetracker.presentation.settings  catch .com.sirch.mileagetracker.presentation.settings  clearSuccessMessage .com.sirch.mileagetracker.presentation.settings  cloudBackupService .com.sirch.mileagetracker.presentation.settings  collectAsState .com.sirch.mileagetracker.presentation.settings  combine .com.sirch.mileagetracker.presentation.settings  fillMaxSize .com.sirch.mileagetracker.presentation.settings  fillMaxWidth .com.sirch.mileagetracker.presentation.settings  find .com.sirch.mileagetracker.presentation.settings  getValue .com.sirch.mileagetracker.presentation.settings  height .com.sirch.mileagetracker.presentation.settings  java .com.sirch.mileagetracker.presentation.settings  kotlinx .com.sirch.mileagetracker.presentation.settings  launch .com.sirch.mileagetracker.presentation.settings  let .com.sirch.mileagetracker.presentation.settings  loadSettings .com.sirch.mileagetracker.presentation.settings  padding .com.sirch.mileagetracker.presentation.settings  provideDelegate .com.sirch.mileagetracker.presentation.settings  run .com.sirch.mileagetracker.presentation.settings  settingsRepository .com.sirch.mileagetracker.presentation.settings  size .com.sirch.mileagetracker.presentation.settings  spacedBy .com.sirch.mileagetracker.presentation.settings  stringResource .com.sirch.mileagetracker.presentation.settings  weight .com.sirch.mileagetracker.presentation.settings  width .com.sirch.mileagetracker.presentation.settings  BillingConnectionState =com.sirch.mileagetracker.presentation.settings.BillingManager  
PurchaseEvent =com.sirch.mileagetracker.presentation.settings.BillingManager  	CONNECTED Tcom.sirch.mileagetracker.presentation.settings.BillingManager.BillingConnectionState  PurchaseCancelled Kcom.sirch.mileagetracker.presentation.settings.BillingManager.PurchaseEvent  PurchaseCompleted Kcom.sirch.mileagetracker.presentation.settings.BillingManager.PurchaseEvent  PurchaseFailed Kcom.sirch.mileagetracker.presentation.settings.BillingManager.PurchaseEvent  BackupResult Acom.sirch.mileagetracker.presentation.settings.CloudBackupService  
RestoreResult Acom.sirch.mileagetracker.presentation.settings.CloudBackupService  Error Ncom.sirch.mileagetracker.presentation.settings.CloudBackupService.BackupResult  Success Ncom.sirch.mileagetracker.presentation.settings.CloudBackupService.BackupResult  UserNotLoggedIn Ncom.sirch.mileagetracker.presentation.settings.CloudBackupService.BackupResult  Error Ocom.sirch.mileagetracker.presentation.settings.CloudBackupService.RestoreResult  
NoBackupFound Ocom.sirch.mileagetracker.presentation.settings.CloudBackupService.RestoreResult  Success Ocom.sirch.mileagetracker.presentation.settings.CloudBackupService.RestoreResult  UserNotLoggedIn Ocom.sirch.mileagetracker.presentation.settings.CloudBackupService.RestoreResult  copy >com.sirch.mileagetracker.presentation.settings.SettingsUiState  darkModeEnabled >com.sirch.mileagetracker.presentation.settings.SettingsUiState  error >com.sirch.mileagetracker.presentation.settings.SettingsUiState  isBackupInProgress >com.sirch.mileagetracker.presentation.settings.SettingsUiState  isBillingConnected >com.sirch.mileagetracker.presentation.settings.SettingsUiState  	isPremium >com.sirch.mileagetracker.presentation.settings.SettingsUiState  isPurchasing >com.sirch.mileagetracker.presentation.settings.SettingsUiState  lastBackupDate >com.sirch.mileagetracker.presentation.settings.SettingsUiState  removeAdsPrice >com.sirch.mileagetracker.presentation.settings.SettingsUiState  successMessage >com.sirch.mileagetracker.presentation.settings.SettingsUiState  	userEmail >com.sirch.mileagetracker.presentation.settings.SettingsUiState  
BillingClient @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  BillingManager @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  MutableStateFlow @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  Pair @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  SettingsUiState @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  _uiState @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  
adsRepository @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  asStateFlow @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  authRepository @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  
backupToCloud @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  billingManager @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  catch @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  clearSuccessMessage @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  cloudBackupService @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  combine @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  find @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  java @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  kotlinx @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  launch @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  loadPremiumStatus @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  loadRemoveAdsPrice @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  loadSettings @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  loadUserInfo @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  observeBillingEvents @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  purchaseRemoveAds @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  restoreFromCloud @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  settingsRepository @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  signOut @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  toggleDarkMode @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  uiState @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  viewModelScope @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  activity 7com.sirch.mileagetracker.presentation.settings.androidx  ComponentActivity @com.sirch.mileagetracker.presentation.settings.androidx.activity  	Alignment ,com.sirch.mileagetracker.presentation.splash  Arrangement ,com.sirch.mileagetracker.presentation.splash  
AuthViewModel ,com.sirch.mileagetracker.presentation.splash  Boolean ,com.sirch.mileagetracker.presentation.splash  CircularProgressIndicator ,com.sirch.mileagetracker.presentation.splash  Column ,com.sirch.mileagetracker.presentation.splash  
Composable ,com.sirch.mileagetracker.presentation.splash  	Exception ,com.sirch.mileagetracker.presentation.splash  
HiltViewModel ,com.sirch.mileagetracker.presentation.splash  InitializeAppUseCase ,com.sirch.mileagetracker.presentation.splash  Inject ,com.sirch.mileagetracker.presentation.splash  LaunchedEffect ,com.sirch.mileagetracker.presentation.splash  
MaterialTheme ,com.sirch.mileagetracker.presentation.splash  Modifier ,com.sirch.mileagetracker.presentation.splash  MutableStateFlow ,com.sirch.mileagetracker.presentation.splash  R ,com.sirch.mileagetracker.presentation.splash  Spacer ,com.sirch.mileagetracker.presentation.splash  SplashScreen ,com.sirch.mileagetracker.presentation.splash  
SplashUiState ,com.sirch.mileagetracker.presentation.splash  SplashViewModel ,com.sirch.mileagetracker.presentation.splash  	StateFlow ,com.sirch.mileagetracker.presentation.splash  String ,com.sirch.mileagetracker.presentation.splash  Text ,com.sirch.mileagetracker.presentation.splash  	TextAlign ,com.sirch.mileagetracker.presentation.splash  Unit ,com.sirch.mileagetracker.presentation.splash  	ViewModel ,com.sirch.mileagetracker.presentation.splash  _uiState ,com.sirch.mileagetracker.presentation.splash  asStateFlow ,com.sirch.mileagetracker.presentation.splash  collectAsState ,com.sirch.mileagetracker.presentation.splash  delay ,com.sirch.mileagetracker.presentation.splash  fillMaxSize ,com.sirch.mileagetracker.presentation.splash  getValue ,com.sirch.mileagetracker.presentation.splash  height ,com.sirch.mileagetracker.presentation.splash  initializeAppUseCase ,com.sirch.mileagetracker.presentation.splash  launch ,com.sirch.mileagetracker.presentation.splash  padding ,com.sirch.mileagetracker.presentation.splash  provideDelegate ,com.sirch.mileagetracker.presentation.splash  stringResource ,com.sirch.mileagetracker.presentation.splash  copy :com.sirch.mileagetracker.presentation.splash.SplashUiState  MutableStateFlow <com.sirch.mileagetracker.presentation.splash.SplashViewModel  
SplashUiState <com.sirch.mileagetracker.presentation.splash.SplashViewModel  _uiState <com.sirch.mileagetracker.presentation.splash.SplashViewModel  asStateFlow <com.sirch.mileagetracker.presentation.splash.SplashViewModel  
initializeApp <com.sirch.mileagetracker.presentation.splash.SplashViewModel  initializeAppUseCase <com.sirch.mileagetracker.presentation.splash.SplashViewModel  launch <com.sirch.mileagetracker.presentation.splash.SplashViewModel  uiState <com.sirch.mileagetracker.presentation.splash.SplashViewModel  viewModelScope <com.sirch.mileagetracker.presentation.splash.SplashViewModel  
AdsRepository +com.sirch.mileagetracker.presentation.theme  Boolean +com.sirch.mileagetracker.presentation.theme  
HiltViewModel +com.sirch.mileagetracker.presentation.theme  Inject +com.sirch.mileagetracker.presentation.theme  SettingsRepository +com.sirch.mileagetracker.presentation.theme  	Singleton +com.sirch.mileagetracker.presentation.theme  ThemeManager +com.sirch.mileagetracker.presentation.theme  
ThemeState +com.sirch.mileagetracker.presentation.theme  ThemeViewModel +com.sirch.mileagetracker.presentation.theme  	ViewModel +com.sirch.mileagetracker.presentation.theme  combine +com.sirch.mileagetracker.presentation.theme  launch +com.sirch.mileagetracker.presentation.theme  themeManager +com.sirch.mileagetracker.presentation.theme  
ThemeState 8com.sirch.mileagetracker.presentation.theme.ThemeManager  
adsRepository 8com.sirch.mileagetracker.presentation.theme.ThemeManager  combine 8com.sirch.mileagetracker.presentation.theme.ThemeManager  setDarkMode 8com.sirch.mileagetracker.presentation.theme.ThemeManager  settingsRepository 8com.sirch.mileagetracker.presentation.theme.ThemeManager  toggleDarkMode 8com.sirch.mileagetracker.presentation.theme.ThemeManager  launch :com.sirch.mileagetracker.presentation.theme.ThemeViewModel  themeManager :com.sirch.mileagetracker.presentation.theme.ThemeViewModel  viewModelScope :com.sirch.mileagetracker.presentation.theme.ThemeViewModel  Boolean !com.sirch.mileagetracker.ui.theme  Build !com.sirch.mileagetracker.ui.theme  
Composable !com.sirch.mileagetracker.ui.theme  DarkColorScheme !com.sirch.mileagetracker.ui.theme  
FontFamily !com.sirch.mileagetracker.ui.theme  
FontWeight !com.sirch.mileagetracker.ui.theme  LightColorScheme !com.sirch.mileagetracker.ui.theme  MileageTrackerTheme !com.sirch.mileagetracker.ui.theme  Pink40 !com.sirch.mileagetracker.ui.theme  Pink80 !com.sirch.mileagetracker.ui.theme  Purple40 !com.sirch.mileagetracker.ui.theme  Purple80 !com.sirch.mileagetracker.ui.theme  PurpleGrey40 !com.sirch.mileagetracker.ui.theme  PurpleGrey80 !com.sirch.mileagetracker.ui.theme  
Typography !com.sirch.mileagetracker.ui.theme  Unit !com.sirch.mileagetracker.ui.theme  Any com.sirch.mileagetracker.utils  Boolean com.sirch.mileagetracker.utils  
Composable com.sirch.mileagetracker.utils  Context com.sirch.mileagetracker.utils  
DatabaseUtils com.sirch.mileagetracker.utils  Dp com.sirch.mileagetracker.utils  	Exception com.sirch.mileagetracker.utils  File com.sirch.mileagetracker.utils  Float com.sirch.mileagetracker.utils  ImageLoadingState com.sirch.mileagetracker.utils  Int com.sirch.mileagetracker.utils  LaunchedEffect com.sirch.mileagetracker.utils  List com.sirch.mileagetracker.utils  LocalDensity com.sirch.mileagetracker.utils  Long com.sirch.mileagetracker.utils  MutableState com.sirch.mileagetracker.utils  Stable com.sirch.mileagetracker.utils  StableHolder com.sirch.mileagetracker.utils  StableLambda com.sirch.mileagetracker.utils  State com.sirch.mileagetracker.utils  String com.sirch.mileagetracker.utils  T com.sirch.mileagetracker.utils  androidx com.sirch.mileagetracker.utils  derivedStateOf com.sirch.mileagetracker.utils  forEach com.sirch.mileagetracker.utils  generateStableKey com.sirch.mileagetracker.utils  hashCode com.sirch.mileagetracker.utils  kotlinx com.sirch.mileagetracker.utils  listOf com.sirch.mileagetracker.utils  mutableStateOf com.sirch.mileagetracker.utils  remember com.sirch.mileagetracker.utils  rememberCached com.sirch.mileagetracker.utils  rememberContentPadding com.sirch.mileagetracker.utils  rememberDebouncedState com.sirch.mileagetracker.utils  rememberDerivedState com.sirch.mileagetracker.utils  rememberStableHolder com.sirch.mileagetracker.utils  rememberStableLambda com.sirch.mileagetracker.utils  rememberStableList com.sirch.mileagetracker.utils  
toPxCached com.sirch.mileagetracker.utils  with com.sirch.mileagetracker.utils  Context ,com.sirch.mileagetracker.utils.DatabaseUtils  File ,com.sirch.mileagetracker.utils.DatabaseUtils  listOf ,com.sirch.mileagetracker.utils.DatabaseUtils  lambda +com.sirch.mileagetracker.utils.StableLambda  compose 'com.sirch.mileagetracker.utils.androidx  
foundation /com.sirch.mileagetracker.utils.androidx.compose  layout :com.sirch.mileagetracker.utils.androidx.compose.foundation  
PaddingValues Acom.sirch.mileagetracker.utils.androidx.compose.foundation.layout  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  File java.io  delete java.io.File  exists java.io.File  path java.io.File  Class 	java.lang  	Exception 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Date 	java.util  
getDefault java.util.Locale  Inject javax.inject  	Singleton javax.inject  Array kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  Suppress kotlin  	Throwable kotlin  Triple kotlin  apply kotlin  hashCode kotlin  let kotlin  	onFailure kotlin  	onSuccess kotlin  run kotlin  synchronized kotlin  takeIf kotlin  with kotlin  hashCode 
kotlin.Any  not kotlin.Boolean  	compareTo 
kotlin.Double  div 
kotlin.Double  sp 
kotlin.Double  toString 
kotlin.Double  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  toString 
kotlin.Int  	compareTo kotlin.Long  let kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.Result  exceptionOrNull 
kotlin.Result  failure 
kotlin.Result  	isSuccess 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  format 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  let 
kotlin.String  plus 
kotlin.String  takeIf 
kotlin.String  toDouble 
kotlin.String  toDoubleOrNull 
kotlin.String  toInt 
kotlin.String  toIntOrNull 
kotlin.String  toLongOrNull 
kotlin.String  message kotlin.Throwable  printStackTrace kotlin.Throwable  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  Iterator kotlin.collections  List kotlin.collections  MutableList kotlin.collections  	emptyList kotlin.collections  find kotlin.collections  forEach kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  sumOf kotlin.collections  sumOfDouble kotlin.collections  sumOfInt kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  find kotlin.collections.List  hashCode kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  size kotlin.collections.List  sumOf kotlin.collections.List  contains kotlin.collections.MutableList  let kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  resume kotlin.coroutines  Volatile 
kotlin.jvm  java 
kotlin.jvm  KClass kotlin.reflect  
KFunction0 kotlin.reflect  
KFunction1 kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  find kotlin.sequences  forEach kotlin.sequences  sumOf kotlin.sequences  find kotlin.text  forEach kotlin.text  format kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  sumOf kotlin.text  toDouble kotlin.text  toDoubleOrNull kotlin.text  toInt kotlin.text  toIntOrNull kotlin.text  toLongOrNull kotlin.text  CancellableContinuation kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  suspendCancellableCoroutine kotlinx.coroutines  resume *kotlinx.coroutines.CancellableContinuation  
BillingClient !kotlinx.coroutines.CoroutineScope  BillingManager !kotlinx.coroutines.CoroutineScope  Clock !kotlinx.coroutines.CoroutineScope  Pair !kotlinx.coroutines.CoroutineScope  Purchase !kotlinx.coroutines.CoroutineScope  TimeZone !kotlinx.coroutines.CoroutineScope  Triple !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  
adsRepository !kotlinx.coroutines.CoroutineScope  authRepository !kotlinx.coroutines.CoroutineScope  billingManager !kotlinx.coroutines.CoroutineScope  catch !kotlinx.coroutines.CoroutineScope  clearSuccessMessage !kotlinx.coroutines.CoroutineScope  cloudBackupService !kotlinx.coroutines.CoroutineScope  combine !kotlinx.coroutines.CoroutineScope  currentPurchaseId !kotlinx.coroutines.CoroutineScope  currentSystemDefault !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  find !kotlinx.coroutines.CoroutineScope  first !kotlinx.coroutines.CoroutineScope  handlePurchases !kotlinx.coroutines.CoroutineScope  initializeAppUseCase !kotlinx.coroutines.CoroutineScope  
isNotBlank !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  loadSettings !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  	onSuccess !kotlinx.coroutines.CoroutineScope  programRepository !kotlinx.coroutines.CoroutineScope  purchaseRepository !kotlinx.coroutines.CoroutineScope  settingsRepository !kotlinx.coroutines.CoroutineScope  sumOf !kotlinx.coroutines.CoroutineScope  takeIf !kotlinx.coroutines.CoroutineScope  themeManager !kotlinx.coroutines.CoroutineScope  toDouble !kotlinx.coroutines.CoroutineScope  toInt !kotlinx.coroutines.CoroutineScope  todayIn !kotlinx.coroutines.CoroutineScope  Main kotlinx.coroutines.Dispatchers  Channel kotlinx.coroutines.channels  
ChannelResult kotlinx.coroutines.channels  Factory #kotlinx.coroutines.channels.Channel  	UNLIMITED #kotlinx.coroutines.channels.Channel  close #kotlinx.coroutines.channels.Channel  
receiveAsFlow #kotlinx.coroutines.channels.Channel  trySend #kotlinx.coroutines.channels.Channel  	UNLIMITED +kotlinx.coroutines.channels.Channel.Factory  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  first kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  map kotlinx.coroutines.flow  
receiveAsFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow.Flow  collect kotlinx.coroutines.flow.Flow  first kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  _uiState %kotlinx.coroutines.flow.FlowCollector  currentUser %kotlinx.coroutines.flow.FlowCollector  emit %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collect !kotlinx.coroutines.flow.StateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  await kotlinx.coroutines.tasks  Clock kotlinx.datetime  FixedOffsetTimeZone kotlinx.datetime  Instant kotlinx.datetime  	LocalDate kotlinx.datetime  
LocalDateTime kotlinx.datetime  TimeZone kotlinx.datetime  toLocalDateTime kotlinx.datetime  todayIn kotlinx.datetime  	Companion kotlinx.datetime.Clock  System kotlinx.datetime.Clock  todayIn kotlinx.datetime.Clock.System  	Companion kotlinx.datetime.Instant  fromEpochMilliseconds kotlinx.datetime.Instant  toLocalDateTime kotlinx.datetime.Instant  fromEpochMilliseconds "kotlinx.datetime.Instant.Companion  	Companion kotlinx.datetime.LocalDate  parse kotlinx.datetime.LocalDate  toString kotlinx.datetime.LocalDate  parse $kotlinx.datetime.LocalDate.Companion  date kotlinx.datetime.LocalDateTime  	Companion kotlinx.datetime.TimeZone  UTC kotlinx.datetime.TimeZone  currentSystemDefault kotlinx.datetime.TimeZone  UTC #kotlinx.datetime.TimeZone.Companion  currentSystemDefault #kotlinx.datetime.TimeZone.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          