{"logs": [{"outputFile": "com.sirch.mileagetracker.app-mergeReleaseResources-3:/values/values.xml", "map": [{"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\92d695355f5d2a2536cf2e448dac4831\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "97,104", "startColumns": "4,4", "startOffsets": "6337,6690", "endColumns": "53,66", "endOffsets": "6386,6752"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\3793d766ef17a5c374cbb3773985b3be\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "16,17,18,19,20,21,22,23,148,149,150,151,152,153,154,155,157,158,159,160,161,162,163,164,165,516,589", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1066,1156,1236,1326,1416,1496,1577,1657,9331,9436,9617,9742,9849,10029,10152,10268,10538,10726,10831,11012,11137,11312,11460,11523,11585,31205,34035", "endLines": "16,17,18,19,20,21,22,23,148,149,150,151,152,153,154,155,157,158,159,160,161,162,163,164,165,528,607", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "1151,1231,1321,1411,1491,1572,1652,1732,9431,9612,9737,9844,10024,10147,10263,10366,10721,10826,11007,11132,11307,11455,11518,11580,11659,31515,34447"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\8d1bf2e31701539c6e06f44761c220aa\\transformed\\recyclerview-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,38", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,2084"}, "to": {"startLines": "3,35,36,37,38,39,40,98,568", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "210,2500,2559,2607,2663,2738,2814,6391,33195", "endLines": "3,35,36,37,38,39,40,98,588", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "261,2554,2602,2658,2733,2809,2881,6452,34030"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\17af91994e13f583c0b6091f8c6fb16f\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "2,8,9,14,15,24,25,28,29,30,31,32,33,34,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,99,100,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,130,138,139,140,141,142,143,144,320,343,344,348,349,353,380,381,396,402,412,447,477,510", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,526,598,935,1000,1737,1806,2012,2082,2150,2222,2292,2353,2427,2886,2947,3008,3070,3134,3196,3257,3325,3425,3485,3551,3624,3693,3750,3802,3864,3936,4012,6457,6492,6864,6919,6982,7037,7095,7153,7214,7277,7334,7385,7435,7496,7553,7619,7653,7688,8115,8657,8724,8796,8865,8934,9008,9080,23074,24282,24399,24600,24710,24911,26423,26495,27046,27249,27550,29356,30356,31038", "endLines": "2,8,9,14,15,24,25,28,29,30,31,32,33,34,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,99,100,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,130,138,139,140,141,142,143,144,320,343,347,348,352,353,380,381,401,411,446,467,509,515", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,593,681,995,1061,1801,1864,2077,2145,2217,2287,2348,2422,2495,2942,3003,3065,3129,3191,3252,3320,3420,3480,3546,3619,3688,3745,3797,3859,3931,4007,4072,6487,6522,6914,6977,7032,7090,7148,7209,7272,7329,7380,7430,7491,7548,7614,7648,7683,7718,8180,8719,8791,8860,8929,9003,9075,9163,23140,24394,24595,24705,24906,25035,26490,26557,27244,27545,29351,30032,31033,31200"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\e995885063661f84453093b90e093640\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "10,11,12,13,26,27,166,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "686,744,810,873,1869,1940,11664,12302,12369,12448", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "739,805,868,930,1935,2007,11727,12364,12443,12512"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\3990bfda9ffd8f92176a2bef9f4c6552\\transformed\\core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "372", "startColumns": "4", "startOffsets": "26013", "endLines": "379", "endColumns": "8", "endOffsets": "26418"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\1cc1f4db42179ea51d7963b43c0e49be\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "101,382,557,560", "startColumns": "4,4,4,4", "startOffsets": "6527,26562,32783,32898", "endLines": "101,388,559,562", "endColumns": "52,24,24,24", "endOffsets": "6575,26861,32893,33008"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\51f776a662999a2c4066cf59dc47f5c4\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "129,156", "startColumns": "4,4", "startOffsets": "8047,10371", "endColumns": "67,166", "endOffsets": "8110,10533"}}, {"source": "C:\\projetos\\MileageTracker\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "47,33,1,27,41,56,35,34,51,50,17,18,16,15,14,21,22,20,19,4,5,6,7,53,46,26,25,39,36,38,37,32,45,52,40,44,10,11,28,29,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2353,1694,16,1438,2116,2611,1808,1752,2464,2414,847,912,775,705,577,1124,1203,1039,979,94,136,187,237,2554,2296,1373,1308,2028,1867,1978,1924,1642,2240,2503,2076,2183,311,377,1510,1566,419,484", "endColumns": "39,57,52,71,43,46,58,55,38,49,64,66,71,69,127,78,81,84,59,41,50,49,54,33,56,64,64,47,56,49,53,51,55,50,39,56,65,41,55,51,64,92", "endOffsets": "2388,1747,64,1505,2155,2653,1862,1803,2498,2459,907,974,842,770,700,1198,1280,1119,1034,131,182,232,287,2583,2348,1433,1368,2071,1919,2023,1973,1689,2291,2549,2111,2235,372,414,1561,1613,479,572"}, "to": {"startLines": "131,132,136,137,145,167,171,173,174,185,186,187,188,189,190,191,192,193,194,264,265,266,267,293,295,296,297,299,300,301,302,303,306,307,315,317,318,319,326,327,331,332", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8185,8225,8532,8585,9168,11732,12090,12207,12263,13156,13206,13271,13338,13410,13480,13608,13687,13769,13854,19080,19122,19173,19223,21659,21727,21784,21849,21995,22043,22100,22150,22204,22360,22416,22823,22909,22966,23032,23429,23485,23706,23771", "endColumns": "39,57,52,71,43,46,58,55,38,49,64,66,71,69,127,78,81,84,59,41,50,49,54,33,56,64,64,47,56,49,53,51,55,50,39,56,65,41,55,51,64,92", "endOffsets": "8220,8278,8580,8652,9207,11774,12144,12258,12297,13201,13266,13333,13405,13475,13603,13682,13764,13849,13909,19117,19168,19218,19273,21688,21779,21844,21909,22038,22095,22145,22199,22251,22411,22462,22858,22961,23027,23069,23480,23532,23766,23859"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\442d5ca857bbad7a471a6cbe01619b10\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "125", "startColumns": "4", "startOffsets": "7826", "endColumns": "53", "endOffsets": "7875"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\74b24aea6cb3f186a9f3826296a9dedb\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "94,106,127,468,473", "startColumns": "4,4,4,4,4", "startOffsets": "6174,6799,7930,30037,30207", "endLines": "94,106,127,472,476", "endColumns": "56,64,63,24,24", "endOffsets": "6226,6859,7989,30202,30351"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\a24ca3f7559becade3059179d8654f9c\\transformed\\play-services-ads-lite-23.5.0\\res\\values\\values.xml", "from": {"startLines": "4,14", "startColumns": "0,0", "startOffsets": "167,661", "endLines": "11,20", "endColumns": "8,20", "endOffsets": "560,836"}, "to": {"startLines": "358,389", "startColumns": "4,4", "startOffsets": "25263,26866", "endLines": "365,395", "endColumns": "8,20", "endOffsets": "25656,27041"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\458afcae9cfab2e24d54d1af183fe921\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "135", "startColumns": "4", "startOffsets": "8449", "endColumns": "82", "endOffsets": "8527"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\a6eb76435759cf266af4e2b252857fcf\\transformed\\work-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "4,5,6,7", "startColumns": "4,4,4,4", "startOffsets": "266,331,401,465", "endColumns": "64,69,63,60", "endOffsets": "326,396,460,521"}}, {"source": "C:\\projetos\\MileageTracker\\app\\build\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,726", "endColumns": "143,81,103,108,119,111,80", "endOffsets": "194,276,380,489,609,721,802"}, "to": {"startLines": "170,178,179,180,181,182,298", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "11946,12517,12599,12703,12812,12932,21914", "endColumns": "143,81,103,108,119,111,80", "endOffsets": "12085,12594,12698,12807,12927,13039,21990"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\ce7063c7507d7ba5446267220fbed412\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "133,134", "startColumns": "4,4", "startOffsets": "8283,8365", "endColumns": "81,83", "endOffsets": "8360,8444"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\261871324870a4e9135a74180032be55\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "354", "startColumns": "4", "startOffsets": "25040", "endLines": "357", "endColumns": "12", "endOffsets": "25258"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\4f87b4fe7e5f13faf091c8d26d70e189\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "195,196,197,198,199,200,201,202,203,204,207,208,209,210,211,212,213,214,215,216,217,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13914,14002,14088,14169,14253,14322,14387,14470,14576,14662,14782,14836,14905,14966,15035,15124,15219,15293,15390,15483,15581,15730,15821,15909,16005,16103,16167,16235,16322,16416,16483,16555,16627,16728,16837,16913,16982,17030,17096,17160,17217,17274,17346,17396,17450,17521,17592,17662,17731,17789,17865,17936,18010,18096,18146,18216", "endLines": "195,196,197,198,199,200,201,202,203,206,207,208,209,210,211,212,213,214,215,216,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "13997,14083,14164,14248,14317,14382,14465,14571,14657,14777,14831,14900,14961,15030,15119,15214,15288,15385,15478,15576,15725,15816,15904,16000,16098,16162,16230,16317,16411,16478,16550,16622,16723,16832,16908,16977,17025,17091,17155,17212,17269,17341,17391,17445,17516,17587,17657,17726,17784,17860,17931,18005,18091,18141,18211,18276"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\fcd13fdc941282eaa2071fb31e6df1d3\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,95,96,128,146,147,168,169,172,183,184,268,269,276,294,304,305,316,321,322,323,333,336,339", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4077,4136,4195,4255,4315,4375,4435,4495,4555,4615,4675,4735,4795,4854,4914,4974,5034,5094,5154,5214,5274,5334,5394,5454,5513,5573,5633,5692,5751,5810,5869,5928,5987,6061,6119,6231,6282,7994,9212,9277,11779,11845,12149,13044,13096,19278,19340,19914,21693,22256,22306,22863,23145,23192,23228,23864,23976,24087", "endLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,95,96,128,146,147,168,169,172,183,184,268,269,276,294,304,305,316,321,322,323,335,338,342", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "4131,4190,4250,4310,4370,4430,4490,4550,4610,4670,4730,4790,4849,4909,4969,5029,5089,5149,5209,5269,5329,5389,5449,5508,5568,5628,5687,5746,5805,5864,5923,5982,6056,6114,6169,6277,6332,8042,9272,9326,11840,11941,12202,13091,13151,19335,19389,19945,21722,22301,22355,22904,23187,23223,23313,23971,24082,24277"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\d2a075533edca0ecf15999b66412bea2\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "126", "startColumns": "4", "startOffsets": "7880", "endColumns": "49", "endOffsets": "7925"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\4a76ad41d50695e7020c9840e9e4ecf3\\transformed\\navigation-common-2.7.7\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "529,542,548,554,563", "startColumns": "4,4,4,4,4", "startOffsets": "31520,32159,32403,32650,33013", "endLines": "541,547,553,556,567", "endColumns": "24,24,24,24,24", "endOffsets": "32154,32398,32645,32778,33190"}}, {"source": "C:\\projetos\\MileageTracker\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "139", "endLines": "8", "endColumns": "12", "endOffsets": "529"}, "to": {"startLines": "366", "startColumns": "4", "startOffsets": "25661", "endLines": "371", "endColumns": "12", "endOffsets": "26008"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\831f7f0f93fd70afcef46ca0120dc927\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "324,325", "startColumns": "4,4", "startOffsets": "23318,23374", "endColumns": "55,54", "endOffsets": "23369,23424"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\3d72df6bea2f26e767ce2603fe599f97\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7723", "endColumns": "42", "endOffsets": "7761"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\317f5b3d9a3eeaad7df4aac804d9c848\\transformed\\activity-1.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "105,124", "startColumns": "4,4", "startOffsets": "6757,7766", "endColumns": "41,59", "endOffsets": "6794,7821"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\10e14192bcf7bea6e0ef145c1ad40304\\transformed\\play-services-ads-23.5.0\\res\\values\\values.xml", "from": {"startLines": "5,7,10,13,16,19,21,23,25,27,29,31,33,35,37,39,41,42,43,44,45,46,47,48", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "167,224,373,620,869,1145,1284,1441,1653,1857,2085,2309,2572,2743,2926,3145,3330,3368,3441,3475,3510,3559,3623,3658", "endLines": "5,7,12,15,18,20,22,24,26,28,30,32,34,36,38,40,41,42,43,44,45,46,47,50", "endColumns": "55,45,11,11,11,20,27,51,19,86,68,62,17,24,81,48,37,72,33,34,48,63,34,11", "endOffsets": "222,269,619,868,1144,1283,1440,1652,1856,2084,2308,2571,2742,2925,3144,3329,3367,3440,3474,3509,3558,3622,3657,3822"}, "to": {"startLines": "102,103,255,258,261,270,272,274,277,279,281,283,285,287,289,291,308,309,310,311,312,313,314,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6580,6640,18281,18537,18795,19394,19537,19698,19950,20158,20390,20618,20885,21060,21247,21470,22467,22509,22586,22624,22663,22716,22784,23537", "endLines": "102,103,257,260,263,271,273,275,278,280,282,284,286,288,290,292,308,309,310,311,312,313,314,330", "endColumns": "59,49,11,11,11,20,27,51,19,86,68,62,17,24,81,48,41,76,37,38,52,67,38,11", "endOffsets": "6635,6685,18532,18790,19075,19532,19693,19909,20153,20385,20613,20880,21055,21242,21465,21654,22504,22581,22619,22658,22711,22779,22818,23701"}}]}]}