package com.sirch.mileagetracker.data.local.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.Index
import kotlinx.datetime.LocalDateTime

/**
 * Entity for storing user-defined report templates and configurations.
 * Supports both basic and premium report features with customizable filters.
 *
 * @property id Unique identifier for the report template
 * @property userId Firebase user ID
 * @property name User-defined name for the template
 * @property description Optional description of the report
 * @property filters JSON string containing filter configurations
 * @property isPremium Whether this is a premium template
 * @property createdAt Template creation timestamp
 * @property lastUsed Last time this template was used
 * @property useCount Number of times template has been used
 * @property reportType Type of report (SUMMARY, DETAILED, EFFICIENCY, etc.)
 * @property outputFormat Preferred output format (PDF, EXCEL, CSV)
 * @property dateRange Date range configuration for the report
 * @property programIds List of program IDs to include (null for all)
 * @property includeCharts Whether to include charts in the report
 * @property includeEfficiencyScore Whether to include efficiency score data
 * @property customFields Additional custom fields to include
 * @property isShared Whether template is shared with other users (premium)
 * @property shareCode Unique code for sharing templates (premium)
 */
@Entity(
    tableName = "report_templates",
    indices = [
        Index(value = ["userId"]),
        Index(value = ["name"]),
        Index(value = ["reportType"]),
        Index(value = ["isPremium"]),
        Index(value = ["createdAt"]),
        Index(value = ["lastUsed"]),
        Index(value = ["shareCode"], unique = true),
        Index(value = ["userId", "name"])
    ]
)
data class ReportTemplate(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,

    val userId: String,

    val name: String,

    val description: String? = null,

    val filters: String, // JSON string with filter configurations

    val isPremium: Boolean = false,

    val createdAt: LocalDateTime,

    val lastUsed: LocalDateTime? = null,

    val useCount: Int = 0,

    val reportType: ReportType = ReportType.SUMMARY,

    val outputFormat: OutputFormat = OutputFormat.PDF,

    val dateRange: DateRangeType = DateRangeType.LAST_MONTH,

    val programIds: String? = null, // JSON array of program IDs

    val includeCharts: Boolean = true,

    val includeEfficiencyScore: Boolean = false,

    val customFields: String? = null, // JSON array of custom field names

    val isShared: Boolean = false,

    val shareCode: String? = null
) {
    companion object {
        const val MAX_FREE_TEMPLATES = 3 // Maximum templates for free users
        const val MAX_PREMIUM_TEMPLATES = 50 // Maximum templates for premium users
        const val SHARE_CODE_LENGTH = 8 // Length of share codes
    }

    /**
     * Check if template is recently used (within last 30 days)
     */
    fun isRecentlyUsed(): Boolean {
        // Simplified implementation - will be enhanced later
        return lastUsed != null
    }

    /**
     * Get template age in days
     */
    fun getAgeInDays(): Int {
        // Simplified implementation - will be enhanced later
        return 0
    }

    /**
     * Get usage frequency description
     */
    fun getUsageFrequency(): String {
        return when {
            useCount == 0 -> "Never used"
            useCount == 1 -> "Used once"
            useCount < 5 -> "Used $useCount times"
            useCount < 10 -> "Used frequently"
            else -> "Used very frequently ($useCount times)"
        }
    }

    /**
     * Get report type description
     */
    fun getTypeDescription(): String {
        return when (reportType) {
            ReportType.SUMMARY -> "Summary Report"
            ReportType.DETAILED -> "Detailed Report"
            ReportType.EFFICIENCY -> "Efficiency Analysis"
            ReportType.COMPARISON -> "Program Comparison"
            ReportType.TRENDS -> "Trends Analysis"
            ReportType.GOALS -> "Goals Progress"
            ReportType.CUSTOM -> "Custom Report"
        }
    }

    /**
     * Get output format description
     */
    fun getFormatDescription(): String {
        return when (outputFormat) {
            OutputFormat.PDF -> "PDF Document"
            OutputFormat.EXCEL -> "Excel Spreadsheet"
            OutputFormat.CSV -> "CSV File"
            OutputFormat.JSON -> "JSON Data"
        }
    }

    /**
     * Get date range description
     */
    fun getDateRangeDescription(): String {
        return when (dateRange) {
            DateRangeType.LAST_WEEK -> "Last Week"
            DateRangeType.LAST_MONTH -> "Last Month"
            DateRangeType.LAST_QUARTER -> "Last Quarter"
            DateRangeType.LAST_YEAR -> "Last Year"
            DateRangeType.YEAR_TO_DATE -> "Year to Date"
            DateRangeType.ALL_TIME -> "All Time"
            DateRangeType.CUSTOM -> "Custom Range"
        }
    }

    /**
     * Parse program IDs from JSON string
     */
    fun getProgramIdsList(): List<Long> {
        return try {
            programIds?.let { ids ->
                // Simple JSON parsing for array of numbers
                ids.removeSurrounding("[", "]")
                    .split(",")
                    .mapNotNull { it.trim().toLongOrNull() }
            } ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * Parse custom fields from JSON string
     */
    fun getCustomFieldsList(): List<String> {
        return try {
            customFields?.let { fields ->
                // Simple JSON parsing for array of strings
                fields.removeSurrounding("[", "]")
                    .split(",")
                    .map { it.trim().removeSurrounding("\"") }
                    .filter { it.isNotBlank() }
            } ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * Check if template can be shared (premium feature)
     */
    fun canShare(): Boolean {
        return isPremium && !isShared
    }

    /**
     * Generate a unique share code
     */
    fun generateShareCode(): String {
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return (1..SHARE_CODE_LENGTH)
            .map { chars.random() }
            .joinToString("")
    }

    /**
     * Get template summary for UI display
     */
    fun getSummary(): String {
        val parts = mutableListOf<String>()
        parts.add(getTypeDescription())
        parts.add(getDateRangeDescription())
        if (includeCharts) parts.add("with charts")
        if (includeEfficiencyScore) parts.add("with efficiency score")
        return parts.joinToString(", ")
    }
}

/**
 * Types of reports supported by the application
 */
enum class ReportType {
    SUMMARY,        // Basic summary report
    DETAILED,       // Detailed transaction report
    EFFICIENCY,     // Efficiency score analysis
    COMPARISON,     // Program comparison report
    TRENDS,         // Trends and patterns analysis
    GOALS,          // Goals progress report
    CUSTOM          // Custom user-defined report
}

/**
 * Output formats for reports
 */
enum class OutputFormat {
    PDF,            // PDF document
    EXCEL,          // Excel spreadsheet
    CSV,            // CSV file
    JSON            // JSON data export
}

/**
 * Date range options for reports
 */
enum class DateRangeType {
    LAST_WEEK,      // Last 7 days
    LAST_MONTH,     // Last 30 days
    LAST_QUARTER,   // Last 3 months
    LAST_YEAR,      // Last 12 months
    YEAR_TO_DATE,   // From January 1st to today
    ALL_TIME,       // All available data
    CUSTOM          // User-defined date range
}
