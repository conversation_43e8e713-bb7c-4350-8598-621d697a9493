package com.sirch.mileagetracker.di;

import com.sirch.mileagetracker.data.local.MileageTrackerDatabase;
import com.sirch.mileagetracker.data.local.dao.ProgramDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideProgramDaoFactory implements Factory<ProgramDao> {
  private final Provider<MileageTrackerDatabase> databaseProvider;

  public DatabaseModule_ProvideProgramDaoFactory(
      Provider<MileageTrackerDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public ProgramDao get() {
    return provideProgramDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideProgramDaoFactory create(
      Provider<MileageTrackerDatabase> databaseProvider) {
    return new DatabaseModule_ProvideProgramDaoFactory(databaseProvider);
  }

  public static ProgramDao provideProgramDao(MileageTrackerDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideProgramDao(database));
  }
}
