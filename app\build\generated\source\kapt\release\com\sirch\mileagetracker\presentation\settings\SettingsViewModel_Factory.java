package com.sirch.mileagetracker.presentation.settings;

import com.sirch.mileagetracker.data.auth.AuthRepository;
import com.sirch.mileagetracker.data.billing.BillingManager;
import com.sirch.mileagetracker.data.cloud.CloudBackupService;
import com.sirch.mileagetracker.data.repository.AdsRepository;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SettingsViewModel_Factory implements Factory<SettingsViewModel> {
  private final Provider<AuthRepository> authRepositoryProvider;

  private final Provider<AdsRepository> adsRepositoryProvider;

  private final Provider<BillingManager> billingManagerProvider;

  private final Provider<SettingsRepository> settingsRepositoryProvider;

  private final Provider<CloudBackupService> cloudBackupServiceProvider;

  public SettingsViewModel_Factory(Provider<AuthRepository> authRepositoryProvider,
      Provider<AdsRepository> adsRepositoryProvider,
      Provider<BillingManager> billingManagerProvider,
      Provider<SettingsRepository> settingsRepositoryProvider,
      Provider<CloudBackupService> cloudBackupServiceProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
    this.adsRepositoryProvider = adsRepositoryProvider;
    this.billingManagerProvider = billingManagerProvider;
    this.settingsRepositoryProvider = settingsRepositoryProvider;
    this.cloudBackupServiceProvider = cloudBackupServiceProvider;
  }

  @Override
  public SettingsViewModel get() {
    return newInstance(authRepositoryProvider.get(), adsRepositoryProvider.get(), billingManagerProvider.get(), settingsRepositoryProvider.get(), cloudBackupServiceProvider.get());
  }

  public static SettingsViewModel_Factory create(Provider<AuthRepository> authRepositoryProvider,
      Provider<AdsRepository> adsRepositoryProvider,
      Provider<BillingManager> billingManagerProvider,
      Provider<SettingsRepository> settingsRepositoryProvider,
      Provider<CloudBackupService> cloudBackupServiceProvider) {
    return new SettingsViewModel_Factory(authRepositoryProvider, adsRepositoryProvider, billingManagerProvider, settingsRepositoryProvider, cloudBackupServiceProvider);
  }

  public static SettingsViewModel newInstance(AuthRepository authRepository,
      AdsRepository adsRepository, BillingManager billingManager,
      SettingsRepository settingsRepository, CloudBackupService cloudBackupService) {
    return new SettingsViewModel(authRepository, adsRepository, billingManager, settingsRepository, cloudBackupService);
  }
}
