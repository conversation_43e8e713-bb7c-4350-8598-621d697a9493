package com.sirch.mileagetracker.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AdsRepository_Factory implements Factory<AdsRepository> {
  private final Provider<SettingsRepository> settingsRepositoryProvider;

  public AdsRepository_Factory(Provider<SettingsRepository> settingsRepositoryProvider) {
    this.settingsRepositoryProvider = settingsRepositoryProvider;
  }

  @Override
  public AdsRepository get() {
    return newInstance(settingsRepositoryProvider.get());
  }

  public static AdsRepository_Factory create(
      Provider<SettingsRepository> settingsRepositoryProvider) {
    return new AdsRepository_Factory(settingsRepositoryProvider);
  }

  public static AdsRepository newInstance(SettingsRepository settingsRepository) {
    return new AdsRepository(settingsRepository);
  }
}
