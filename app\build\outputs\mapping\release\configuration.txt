# The proguard configuration file for the following section is C:\projetos\MileageTracker\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.credentials.playservices.CredentialProviderMetadataHolder { <init>(); }
-keep class androidx.credentials.playservices.HiddenActivity { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.android.billingclient.api.ProxyBillingActivity { <init>(); }
-keep class com.android.billingclient.api.ProxyBillingActivityV2 { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.ads.AdActivity { <init>(); }
-keep class com.google.android.gms.ads.AdService { <init>(); }
-keep class com.google.android.gms.ads.MobileAdsInitProvider { <init>(); }
-keep class com.google.android.gms.ads.NotificationHandlerActivity { <init>(); }
-keep class com.google.android.gms.ads.OutOfContextTestingActivity { <init>(); }
-keep class com.google.android.gms.auth.api.signin.RevocationBoundService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.internal.SignInHubActivity { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementJobService { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementReceiver { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementService { <init>(); }
-keep class com.google.android.play.core.common.PlayCoreDialogWrapperActivity { <init>(); }
-keep class com.google.firebase.auth.internal.GenericIdpActivity { <init>(); }
-keep class com.google.firebase.auth.internal.RecaptchaActivity { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class com.sirch.mileagetracker.MainActivity { <init>(); }
-keep class com.sirch.mileagetracker.MileageTrackerApplication { <init>(); }
-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from C:\projetos\MileageTracker\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.6.0
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from C:\projetos\MileageTracker\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.6.0
# The proguard configuration file for the following section is C:\projetos\MileageTracker\app\proguard-rules.pro
# Mileage Tracker - ProGuard Rules for Production
# Otimizado para performance e segurança

# Preserve line numbers for crash reports
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# Keep generic signatures for reflection
-keepattributes Signature
-keepattributes *Annotation*

# Kotlin specific
-keep class kotlin.** { *; }
-keep class kotlinx.** { *; }
-dontwarn kotlin.**
-dontwarn kotlinx.**

# Room Database
-keep class com.sirch.mileagetracker.data.local.entities.** { *; }
-keep class com.sirch.mileagetracker.data.local.dao.** { *; }
-keep class androidx.room.** { *; }
-dontwarn androidx.room.**

# Firebase & Google Play Services
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Hilt Dependency Injection
-keep class dagger.hilt.** { *; }
-keep class * extends dagger.hilt.android.HiltAndroidApp
-keep @dagger.hilt.android.AndroidEntryPoint class * { *; }
-keep @dagger.hilt.android.HiltAndroidApp class * { *; }
-dontwarn dagger.hilt.**

# Compose
-keep class androidx.compose.** { *; }
-dontwarn androidx.compose.**

# Coroutines
-keepclassmembers class kotlinx.coroutines.** { *; }
-dontwarn kotlinx.coroutines.**

# Billing
-keep class com.android.billingclient.** { *; }
-dontwarn com.android.billingclient.**

# AdMob
-keep class com.google.android.gms.ads.** { *; }
-dontwarn com.google.android.gms.ads.**

# Paging
-keep class androidx.paging.** { *; }
-dontwarn androidx.paging.**

# Navigation
-keep class androidx.navigation.** { *; }
-dontwarn androidx.navigation.**

# End of content from C:\projetos\MileageTracker\app\proguard-rules.pro
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\10e14192bcf7bea6e0ef145c1ad40304\transformed\play-services-ads-23.5.0\proguard.txt
-keep public class com.google.android.gms.ads.internal.ClientApi {
  <init>();
}

# When built for Android API level < 30, Proguard warns that it can't find
# android.telephony.TelephonyDisplayInfo (since it was added only in API level
# 30). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.telephony.TelephonyDisplayInfo

# When built for Android API level < 30, Proguard warns that it can't find
# android.view.Surface#setFrameRate(float, int) (since it was added only in API
# level 30). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.view.Surface

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.ApplicationMediaCapabilities (since it was added only in API
# level 31). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.ApplicationMediaCapabilities

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.MediaFeature (since it was added only in API level 31). But,
# all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.MediaFeature

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.ApplicationMediaCapabilities$Builder (since it was added only in
# API level 31). But, all its usages are guarded by runtime checks of the API
# level. Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.ApplicationMediaCapabilities$Builder

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.MediaFeature$HdrType (since it was added only in API level 31).
# But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.MediaFeature$HdrType

# When built for Android API level < 32, Proguard warns that it can't find
# android.media.AudioAttributes$Builder (since it was added only in API level
# 32). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.AudioAttributes$Builder

# When built for Android API level < 33, Proguard warns that it can't find
# android.adservices.measurement.MeasurementManager (since it was added only
# in API level 33). But, all its usages are guarded by runtime checks of the
# API level. Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.adservices.measurement.MeasurementManager

# When built for Android API level < 33, Proguard warns that it can't find
# javax.lang.model.element.Modifier (since it was added only in API level 33).
# But, all its usages are guarded by runtime checks of the API level. Hence, it
# is safe to suppress Proguard's warnings.
-dontwarn javax.lang.model.element.Modifier

# These are checked at runtime for whether they exist, so it is fine if the API level doesn't include these.
-dontwarn android.content.pm.ApkChecksum
-dontwarn android.content.pm.PackageManager$OnChecksumsReadyListener
# Only for the requestChecksums method, but sadly -dontwarn can't take just a single method.
-dontwarn android.content.pm.PackageManager

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.ads.zzgyx {
  <fields>;
}

# Auto-generated proguard rule(s) with obfuscated symbol
-dontwarn com.google.android.gms.ads.internal.util.zzx


# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\10e14192bcf7bea6e0ef145c1ad40304\transformed\play-services-ads-23.5.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\proguard.txt
# Keep implementations of the AdMob mediation adapter interfaces. Adapters for
# third party ad networks implement these interfaces and are invoked by the
# AdMob SDK via reflection.

-keep class * implements com.google.android.gms.ads.mediation.MediationAdapter {
  public *;
}
-keep class * implements com.google.ads.mediation.MediationAdapter {
  public *;
}
-keep class * implements com.google.android.gms.ads.mediation.customevent.CustomEvent {
  public *;
}
-keep class * implements com.google.ads.mediation.customevent.CustomEvent {
  public *;
}
-keep class * extends com.google.android.gms.ads.mediation.MediationAdNetworkAdapter {
  public *;
}
-keep class * extends com.google.android.gms.ads.mediation.Adapter {
  public *;
}

# Keep classes used for offline ads created by reflection. WorkManagerUtil is
# created reflectively by callers within GMSCore and OfflineNotificationPoster
# is created reflectively by WorkManager.
-keep class com.google.android.gms.ads.internal.util.WorkManagerUtil {
  public *;
}
-keep class com.google.android.gms.ads.internal.offline.buffering.OfflineNotificationPoster {
  public *;
}
-keep class com.google.android.gms.ads.internal.offline.buffering.OfflinePingSender {
  public *;
}

# Keeps the entry for full SDK to access via reflection.
-keep class com.google.android.gms.ads.internal.client.LiteSdkInfo {
  public *;
}

# Keeps the entry for first party plugins to access via reflection.
-keep class com.google.android.gms.ads.MobileAds {
  private void setPlugin(java.lang.String);
}

# Keep recordEvent API for Immersive SDK to access via reflection.
-keep class com.google.android.gms.ads.nativead.NativeAd {
  protected abstract void recordEvent(android.os.Bundle);
}

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.ads.zzgyx {
  <fields>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\proguard.txt
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
# Keep all constructors on ListenableWorker, Worker (also marked with @Keep)
-keep public class * extends androidx.work.ListenableWorker {
    public <init>(...);
}
# We need to keep WorkerParameters for the ListenableWorker constructor
-keep class androidx.work.WorkerParameters

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\4f09aa758a1e6e549b8dfe1fa013af4f\transformed\room-runtime-2.6.1\proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**
-dontwarn androidx.lifecycle.LiveData

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\4f09aa758a1e6e549b8dfe1fa013af4f\transformed\room-runtime-2.6.1\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\proguard.txt
-dontwarn com.google.appengine.api.**
-dontwarn okio.**
-dontwarn org.apache.**
-dontwarn retrofit.android.**
-dontwarn retrofit.appengine.**
-dontwarn retrofit.client.**
-dontwarn rx.**

# This is necessary for keeping SecureTokenHttpApi and IdentityToolkitHttpApi
# Otherwise those classes get stripped out, as they are only being used
# reflectively.

-keepclasseswithmembernames interface * {
    @retrofit.http.* <methods>;
}

# This is necessary for parsing JSON responses, since the JSON converter uses reflection to figure out the class/type of response.
# We mainly need the *Response.classes to not be stripped out. All the firebase-auth classes are proguarded into "com.google.android.gms.internal.firebase-auth-api*".

-keep class com.google.android.gms.internal.** { *; }

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.firebase-auth-api.zzajy {
  <fields>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\proguard.txt
-if class androidx.credentials.CredentialManager
-keep class androidx.credentials.playservices.** {
  *;
}
# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\69f3503a1ab7ef94adda78b85fa69b1e\transformed\play-services-auth-base-18.0.10\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.auth.zzev {
  <fields>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\69f3503a1ab7ef94adda78b85fa69b1e\transformed\play-services-auth-base-18.0.10\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\3ef9735ff38f33d5e208d1392f33ce95\transformed\billing-ktx-7.1.1\proguard.txt
# See https://discuss.gradle.org/t/meta-inf-version-duplicate-error-when-using-proguard/31380
-dontwarn module-info

-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal
# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\3ef9735ff38f33d5e208d1392f33ce95\transformed\billing-ktx-7.1.1\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\proguard.txt
# Keep the AIDL interface
-keep class com.android.vending.billing.** { *; }
-keep class com.google.android.apps.play.billingtestcompanion.aidl.** { *; }

-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.android.apps.common.proguard.UsedByReflection

-keepnames class com.android.billingclient.api.ProxyBillingActivity
-keepnames class com.android.billingclient.api.ProxyBillingActivityV2

# Avoids Proguard warning at build time due to Protobuf use of sun.misc.Unsafe
# and libcore.io.Memory which are available at runtime.
-dontwarn libcore.io.Memory
-dontwarn sun.misc.Unsafe


# For Phenotype
# An unused P/H transitive dependency: com.google.android.libraries.phenotype.registration.PhenotypeResourceReader is stripped out from all Granular normal deps and "can't find reference..." DepsVersionCompat test warning
# is suppressed by ProGuard -dontwarn config.
-dontwarn com.google.android.libraries.phenotype.registration.PhenotypeResourceReader
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Uses reflection to determine if these classes are present and has a graceful
# fallback if they aren't. The test failure it fixes appears to be caused by flogger.
-dontwarn dalvik.system.VMStack
-dontwarn com.google.common.flogger.backend.google.GooglePlatform
-dontwarn com.google.common.flogger.backend.system.DefaultPlatform
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.play_billing.zzhk {
  <fields>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\416f03570aa05b43cf0e1f2c187d9e5d\transformed\play-services-measurement-api-22.1.2\proguard.txt
# Can be removed once we pull in a dependency on firebase-common that includes
# https://github.com/firebase/firebase-android-sdk/pull/1472/commits/856f1ca1151cdd88679bbc778892f23dfa34fc06#diff-a2ed34b5a38b4c6c686b09e54865eb48
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\416f03570aa05b43cf0e1f2c187d9e5d\transformed\play-services-measurement-api-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\bc6f01db09963b6843b36e14090f5a82\transformed\firebase-auth-interop-20.0.0\proguard.txt
# Can be removed once we pull in a dependency on firebase-common that includes
# https://github.com/firebase/firebase-android-sdk/pull/1472/commits/856f1ca1151cdd88679bbc778892f23dfa34fc06#diff-a2ed34b5a38b4c6c686b09e54865eb48
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\bc6f01db09963b6843b36e14090f5a82\transformed\firebase-auth-interop-20.0.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\proguard.txt
-dontwarn com.google.firebase.platforminfo.KotlinDetector
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\d029c1c7d9a2fb35ad74cc27fdb718d7\transformed\play-services-measurement-sdk-22.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\d029c1c7d9a2fb35ad74cc27fdb718d7\transformed\play-services-measurement-sdk-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\4e3d5078365cdf2f027c1755c9a3ae0c\transformed\play-services-measurement-impl-22.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\4e3d5078365cdf2f027c1755c9a3ae0c\transformed\play-services-measurement-impl-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\90d3a6c004314e8ccae7994f409d1d39\transformed\play-services-fido-20.1.0\proguard.txt
# Methods enable and disable in this class are complained as unresolved
# references, but they are system APIs and are not used by Fido client apps.
-dontwarn android.nfc.NfcAdapter

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\90d3a6c004314e8ccae7994f409d1d39\transformed\play-services-fido-20.1.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\********************************\transformed\play-services-base-18.5.0\proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\********************************\transformed\play-services-base-18.5.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\4a76ad41d50695e7020c9840e9e4ecf3\transformed\navigation-common-2.7.7\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# NavArgsLazy creates NavArgs instances using reflection
-if public class ** implements androidx.navigation.NavArgs
-keepclassmembers public class <1> {
    ** fromBundle(android.os.Bundle);
}

# Retain the @Navigator.Name annotation on each subclass of Navigator.
# R8 full mode only retains annotations on items matched by a -keep rule,
# hence the extra -keep rule for the subclasses of Navigator.
#
# A -keep rule for the Navigator.Name annotation class is not required
# since the annotation is referenced from the code.
-keepattributes RuntimeVisibleAnnotations
-keep,allowobfuscation,allowshrinking class * extends androidx.navigation.Navigator

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\4a76ad41d50695e7020c9840e9e4ecf3\transformed\navigation-common-2.7.7\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\4fb0e961ff86dc3d6bfb00eeb925eb52\transformed\hilt-android-2.49\proguard.txt
# Keep for the reflective cast done in EntryPoints.
# See b/183070411#comment4 for more info.
-keep,allowobfuscation,allowshrinking @dagger.hilt.EntryPoint class *# Keep for the reflective cast done in EntryPoints.
# See b/183070411#comment4 for more info.
-keep,allowobfuscation,allowshrinking @dagger.hilt.android.EarlyEntryPoint class *# Keep class names of Hilt injected ViewModels since their name are used as a multibinding map key.
-keepnames @dagger.hilt.android.lifecycle.HiltViewModel class * extends androidx.lifecycle.ViewModel# Keep for the reflective cast done in EntryPoints.
# See b/183070411#comment4 for more info.
-keep,allowobfuscation,allowshrinking @dagger.hilt.internal.ComponentEntryPoint class *
-keep,allowobfuscation,allowshrinking @dagger.hilt.internal.GeneratedEntryPoint class *
# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\4fb0e961ff86dc3d6bfb00eeb925eb52\transformed\hilt-android-2.49\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\c77b8313fb9fa93672f2b458280155ca\transformed\lifecycle-process-2.8.7\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\c77b8313fb9fa93672f2b458280155ca\transformed\lifecycle-process-2.8.7\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\d2a075533edca0ecf15999b66412bea2\transformed\lifecycle-viewmodel-release\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\d2a075533edca0ecf15999b66412bea2\transformed\lifecycle-viewmodel-release\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\442d5ca857bbad7a471a6cbe01619b10\transformed\savedstate-1.2.1\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\442d5ca857bbad7a471a6cbe01619b10\transformed\savedstate-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\3d72df6bea2f26e767ce2603fe599f97\transformed\lifecycle-runtime-release\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# The deprecated `android.app.Fragment` creates `Fragment` instances using reflection.
# See: b/338958225, b/341537875
-keepclasseswithmembers,allowobfuscation public class androidx.lifecycle.ReportFragment {
    public <init>();
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\3d72df6bea2f26e767ce2603fe599f97\transformed\lifecycle-runtime-release\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\080eca4347ce8581fed25677c51e2efa\transformed\lifecycle-viewmodel-savedstate-2.8.7\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\080eca4347ce8581fed25677c51e2efa\transformed\lifecycle-viewmodel-savedstate-2.8.7\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\fcd13fdc941282eaa2071fb31e6df1d3\transformed\ui-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# We supply these as stubs and are able to link to them at runtime
# because they are hidden public classes in Android. We don't want
# R8 to complain about them not being there during optimization.
-dontwarn android.view.RenderNode
-dontwarn android.view.DisplayListCanvas

-keepclassmembers class androidx.compose.ui.platform.ViewLayerContainer {
    protected void dispatchGetDisplayList();
}

-keepclassmembers class androidx.compose.ui.platform.AndroidComposeView {
    android.view.View findViewByAccessibilityIdTraversal(int);
}

# Users can create Modifier.Node instances that implement multiple Modifier.Node interfaces,
# so we cannot tell whether two modifier.node instances are of the same type without using
# reflection to determine the class type. See b/265188224 for more context.
-keep,allowshrinking class * extends androidx.compose.ui.node.ModifierNodeElement

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\fcd13fdc941282eaa2071fb31e6df1d3\transformed\ui-release\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\782e31b20862248d5a2e7e4fc92ba0cd\transformed\runtime-release\proguard.txt
-assumenosideeffects public class androidx.compose.runtime.ComposerKt {
    void sourceInformation(androidx.compose.runtime.Composer,java.lang.String);
    void sourceInformationMarkerStart(androidx.compose.runtime.Composer,int,java.lang.String);
    void sourceInformationMarkerEnd(androidx.compose.runtime.Composer);
}

# Composer's class initializer doesn't do anything but create an EMPTY object. Marking the
# initializers as having no side effects can help encourage shrinkers to merge/devirtualize Composer
# with ComposerImpl.
-assumenosideeffects public class androidx.compose.runtime.Composer {
    void <clinit>();
}
-assumenosideeffects public class androidx.compose.runtime.ComposerImpl {
    void <clinit>();
}
# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\782e31b20862248d5a2e7e4fc92ba0cd\transformed\runtime-release\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\bf682bf27c5d503100d87eb7bcff0cbf\transformed\recaptcha-18.5.1\proguard.txt
# Proguard cannot process META-INF/versions/9.
# See https://discuss.gradle.org/t/meta-inf-version-duplicate-error-when-using-proguard/31380
-dontwarn module-info

# Ignore the warning becuse ClassValueCtorCache is never used on Android.
-dontwarn kotlinx.coroutines.internal.ClassValueCtorCache

# Ignore warning due to the usage from Guava and kotlinx.coroutines.internal.ClassValueCtorCache
-dontwarn java.lang.ClassValue

# Ignore warning to accommodate the missing injar of kotlinx.coroutines.flow for
# androidx.slidingpanelayout.widget.
-dontwarn kotlinx.coroutines.flow.**

# This prevents the SDK to be obfuscated again when building the android app.
-keep class com.google.android.recaptcha.** { *; }

# This is required for recaptcha mobile to function properly.
# See: https://cloud.google.com/recaptcha-enterprise/docs/instrument-android-apps
-keep class com.google.android.play.core.integrity.** { *; }
-keep class com.google.android.gms.tasks.** {*;}

# To keep okhttp3 generated files which are used in our NetworkModule which is
# used widely across the app.
-dontwarn com.squareup.okhttp3.**
-dontwarn okhttp3.**
-keep class com.squareup.okhttp3.* { *;}
-keep class okhttp3.**
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.recaptcha.internal.zzks {
  <fields>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\bf682bf27c5d503100d87eb7bcff0cbf\transformed\recaptcha-18.5.1\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\927488704d32b519ad073b874890ccb4\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\927488704d32b519ad073b874890ccb4\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\0f6deabe0d31a9a254f46677a45ebdce\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\0f6deabe0d31a9a254f46677a45ebdce\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\691d75bc26a13c7c344ce6a7356f942f\transformed\play-services-tasks-18.2.0\proguard.txt


# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\691d75bc26a13c7c344ce6a7356f942f\transformed\play-services-tasks-18.2.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\81a4ed4ba766bf12d2685829ede8ff2c\transformed\play-services-measurement-sdk-api-22.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\81a4ed4ba766bf12d2685829ede8ff2c\transformed\play-services-measurement-sdk-api-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\2fd6f51a9341f95f6f5fae7ecf414eb9\transformed\play-services-measurement-base-22.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjt {
  <fields>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\2fd6f51a9341f95f6f5fae7ecf414eb9\transformed\play-services-measurement-base-22.1.2\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\51f776a662999a2c4066cf59dc47f5c4\transformed\play-services-basement-18.4.0\proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.nullness.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\51f776a662999a2c4066cf59dc47f5c4\transformed\play-services-basement-18.4.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\74b24aea6cb3f186a9f3826296a9dedb\transformed\fragment-1.5.7\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\74b24aea6cb3f186a9f3826296a9dedb\transformed\fragment-1.5.7\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\cefd4a57ff04b24c6ae7c1c9074703e6\transformed\webkit-1.11.0-alpha02\proguard.txt
# Copyright 2018 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# We need to avoid obfuscating the support library boundary interface because
# this API is shared with the Android Support Library.
# Note that we only 'keep' the package org.chromium.support_lib_boundary itself,
# any sub-packages of that package can still be obfuscated.
-keep public class org.chromium.support_lib_boundary.* { public *; }

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent WebViewClientCompat from being renamed, since chromium depends on this name.
-keepnames public class androidx.webkit.WebViewClientCompat

# Prevent ProcessGlobalConfig and member sProcessGlobalConfig from being renamed, since chromium
# depends on this name.
-keepnames public class androidx.webkit.ProcessGlobalConfig {
    private static final *** sProcessGlobalConfig;
}
# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\cefd4a57ff04b24c6ae7c1c9074703e6\transformed\webkit-1.11.0-alpha02\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\8d1bf2e31701539c6e06f44761c220aa\transformed\recyclerview-1.2.1\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When layoutManager xml attribute is used, RecyclerView inflates
#LayoutManagers' constructors using reflection.
-keep public class * extends androidx.recyclerview.widget.RecyclerView$LayoutManager {
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
    public <init>();
}

-keepclassmembers class androidx.recyclerview.widget.RecyclerView {
    public void suppressLayout(boolean);
    public boolean isLayoutSuppressed();
}
# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\8d1bf2e31701539c6e06f44761c220aa\transformed\recyclerview-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\17af91994e13f583c0b6091f8c6fb16f\transformed\core-1.15.0\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\17af91994e13f583c0b6091f8c6fb16f\transformed\core-1.15.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\1f9075f5f54c8de2bebe2ad2fede2432\transformed\rules\lib\META-INF\proguard\datetime.pro
# We depend on kotlinx-serialization as compileOnly.
# If the serializers don't get used, the library works properly even without the
# kotlinx-serialization dependency, but Proguard emits warnings about datetime
# classes mentioning some serialization-related entities.
# These rules should not cause problems: if a project actually relies on
# serialization, then much more than just these two classes will be required,
# so telling Proguard not to worry if these two are missing will not prevent it
# from emitting errors for code that does use serialization but somehow forgot
# to depend on it.
-dontwarn kotlinx.serialization.KSerializer
-dontwarn kotlinx.serialization.Serializable

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\1f9075f5f54c8de2bebe2ad2fede2432\transformed\rules\lib\META-INF\proguard\datetime.pro
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\458afcae9cfab2e24d54d1af183fe921\transformed\startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\458afcae9cfab2e24d54d1af183fe921\transformed\startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\c076c9db53d16a0ba704e5fdac1ad9df\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\c076c9db53d16a0ba704e5fdac1ad9df\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\719eea122b08742a74b3bdb795f00f46\transformed\firebase-components-18.0.0\proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar
-keep,allowshrinking interface com.google.firebase.components.ComponentRegistrar

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\719eea122b08742a74b3bdb795f00f46\transformed\firebase-components-18.0.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\89a12dadb79ce4186493629c0f92b02e\transformed\transport-backend-cct-3.1.8\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\89a12dadb79ce4186493629c0f92b02e\transformed\transport-backend-cct-3.1.8\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\effd5ca4c56007998d331357bfc4e9fe\transformed\transport-api-3.0.0\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\effd5ca4c56007998d331357bfc4e9fe\transformed\transport-api-3.0.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\93764cb915fcca899ea4da4f8369d126\transformed\firebase-encoders-json-18.0.0\proguard.txt

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\93764cb915fcca899ea4da4f8369d126\transformed\firebase-encoders-json-18.0.0\proguard.txt
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\67f41b55f82fe16784861a605b2b5f66\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\67f41b55f82fe16784861a605b2b5f66\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is C:\projetos\MileageTracker\caches\8.14\transforms\949fc5452bb93a3bcd0953502060e503\transformed\googleid-1.1.0\proguard.txt
# Proguard cannot process META-INF/versions/9.
# See https://discuss.gradle.org/t/meta-inf-version-duplicate-error-when-using-proguard/31380
-dontwarn module-info

# End of content from C:\projetos\MileageTracker\caches\8.14\transforms\949fc5452bb93a3bcd0953502060e503\transformed\googleid-1.1.0\proguard.txt
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>