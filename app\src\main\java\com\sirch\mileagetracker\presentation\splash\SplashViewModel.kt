package com.sirch.mileagetracker.presentation.splash

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sirch.mileagetracker.domain.usecase.InitializeAppUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SplashViewModel @Inject constructor(
    private val initializeAppUseCase: InitializeAppUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(SplashUiState())
    val uiState: StateFlow<SplashUiState> = _uiState.asStateFlow()
    
    fun initializeApp() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isInitializing = true)
            
            try {
                initializeAppUseCase()
                _uiState.value = _uiState.value.copy(
                    isInitializing = false,
                    isInitialized = true
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isInitializing = false,
                    error = e.message
                )
            }
        }
    }
}

data class SplashUiState(
    val isInitializing: Boolean = false,
    val isInitialized: Boolean = false,
    val error: String? = null
)
