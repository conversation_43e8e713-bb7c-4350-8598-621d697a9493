package com.sirch.mileagetracker.domain.usecase;

import com.sirch.mileagetracker.data.repository.ProgramRepository;
import com.sirch.mileagetracker.data.repository.PurchaseRepository;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InitializeAppUseCase_Factory implements Factory<InitializeAppUseCase> {
  private final Provider<ProgramRepository> programRepositoryProvider;

  private final Provider<PurchaseRepository> purchaseRepositoryProvider;

  private final Provider<SettingsRepository> settingsRepositoryProvider;

  public InitializeAppUseCase_Factory(Provider<ProgramRepository> programRepositoryProvider,
      Provider<PurchaseRepository> purchaseRepositoryProvider,
      Provider<SettingsRepository> settingsRepositoryProvider) {
    this.programRepositoryProvider = programRepositoryProvider;
    this.purchaseRepositoryProvider = purchaseRepositoryProvider;
    this.settingsRepositoryProvider = settingsRepositoryProvider;
  }

  @Override
  public InitializeAppUseCase get() {
    return newInstance(programRepositoryProvider.get(), purchaseRepositoryProvider.get(), settingsRepositoryProvider.get());
  }

  public static InitializeAppUseCase_Factory create(
      Provider<ProgramRepository> programRepositoryProvider,
      Provider<PurchaseRepository> purchaseRepositoryProvider,
      Provider<SettingsRepository> settingsRepositoryProvider) {
    return new InitializeAppUseCase_Factory(programRepositoryProvider, purchaseRepositoryProvider, settingsRepositoryProvider);
  }

  public static InitializeAppUseCase newInstance(ProgramRepository programRepository,
      PurchaseRepository purchaseRepository, SettingsRepository settingsRepository) {
    return new InitializeAppUseCase(programRepository, purchaseRepository, settingsRepository);
  }
}
