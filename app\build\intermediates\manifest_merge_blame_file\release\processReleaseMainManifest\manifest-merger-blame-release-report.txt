1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.sirch.mileagetracker"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="33"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="com.android.vending.BILLING" />
13-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:7:5-67
13-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:7:22-64
14    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
14-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:26:5-79
14-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:26:22-76
15    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
15-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:27:5-82
15-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:27:22-79
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
16-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:28:5-88
16-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:28:22-85
17    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
17-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:29:5-83
17-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:29:22-80
18    <queries>
18-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:35:5-68:15
19
20        <!-- For browser content -->
21        <intent>
21-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:38:9-44:18
22            <action android:name="android.intent.action.VIEW" />
22-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:39:13-65
22-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:39:21-62
23
24            <category android:name="android.intent.category.BROWSABLE" />
24-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:41:13-74
24-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:41:23-71
25
26            <data android:scheme="https" />
26-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
26-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
27        </intent>
28        <!-- End of browser content -->
29        <!-- For CustomTabsService -->
30        <intent>
30-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:47:9-49:18
31            <action android:name="android.support.customtabs.action.CustomTabsService" />
31-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:48:13-90
31-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:48:21-87
32        </intent>
33        <!-- End of CustomTabsService -->
34        <!-- For MRAID capabilities -->
35        <intent>
35-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:52:9-56:18
36            <action android:name="android.intent.action.INSERT" />
36-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:53:13-67
36-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:53:21-64
37
38            <data android:mimeType="vnd.android.cursor.dir/event" />
38-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
39        </intent>
40        <intent>
40-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:57:9-61:18
41            <action android:name="android.intent.action.VIEW" />
41-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:39:13-65
41-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:39:21-62
42
43            <data android:scheme="sms" />
43-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
43-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
44        </intent>
45        <intent>
45-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:62:9-66:18
46            <action android:name="android.intent.action.DIAL" />
46-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:63:13-65
46-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:63:21-62
47
48            <data android:path="tel:" />
48-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
49        </intent>
50        <!-- End of MRAID capabilities -->
51        <intent>
51-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
52            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
52-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
52-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
53        </intent>
54        <intent>
54-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
55            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
55-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
55-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
56        </intent>
57    </queries>
58
59    <uses-permission android:name="android.permission.WAKE_LOCK" />
59-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
59-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:22-65
60    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
60-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
60-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
61    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
61-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
61-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
62    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
62-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\projetos\MileageTracker\caches\8.14\transforms\bf682bf27c5d503100d87eb7bcff0cbf\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
62-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\projetos\MileageTracker\caches\8.14\transforms\bf682bf27c5d503100d87eb7bcff0cbf\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
63
64    <permission
64-->[androidx.core:core:1.15.0] C:\projetos\MileageTracker\caches\8.14\transforms\17af91994e13f583c0b6091f8c6fb16f\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
65        android:name="com.sirch.mileagetracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
65-->[androidx.core:core:1.15.0] C:\projetos\MileageTracker\caches\8.14\transforms\17af91994e13f583c0b6091f8c6fb16f\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
66        android:protectionLevel="signature" />
66-->[androidx.core:core:1.15.0] C:\projetos\MileageTracker\caches\8.14\transforms\17af91994e13f583c0b6091f8c6fb16f\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
67
68    <uses-permission android:name="com.sirch.mileagetracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
68-->[androidx.core:core:1.15.0] C:\projetos\MileageTracker\caches\8.14\transforms\17af91994e13f583c0b6091f8c6fb16f\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
68-->[androidx.core:core:1.15.0] C:\projetos\MileageTracker\caches\8.14\transforms\17af91994e13f583c0b6091f8c6fb16f\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
69
70    <application
70-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:9:5-43:19
71        android:name="com.sirch.mileagetracker.MileageTrackerApplication"
71-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:10:9-50
72        android:allowBackup="true"
72-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:11:9-35
73        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
73-->[androidx.core:core:1.15.0] C:\projetos\MileageTracker\caches\8.14\transforms\17af91994e13f583c0b6091f8c6fb16f\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
74        android:dataExtractionRules="@xml/data_extraction_rules"
74-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:12:9-65
75        android:extractNativeLibs="false"
76        android:fullBackupContent="@xml/backup_rules"
76-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:13:9-54
77        android:icon="@mipmap/ic_launcher"
77-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:14:9-43
78        android:label="@string/app_name"
78-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:15:9-41
79        android:roundIcon="@mipmap/ic_launcher_round"
79-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:16:9-54
80        android:supportsRtl="true"
80-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:17:9-35
81        android:theme="@style/Theme.MileageTracker" >
81-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:18:9-52
82        <activity
82-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:21:9-30:20
83            android:name="com.sirch.mileagetracker.MainActivity"
83-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:22:13-41
84            android:exported="true"
84-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:23:13-36
85            android:label="@string/app_name"
85-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:24:13-45
86            android:theme="@style/Theme.MileageTracker" >
86-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:25:13-56
87            <intent-filter>
87-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:26:13-29:29
88                <action android:name="android.intent.action.MAIN" />
88-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:27:17-69
88-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:27:25-66
89
90                <category android:name="android.intent.category.LAUNCHER" />
90-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:28:17-77
90-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:28:27-74
91            </intent-filter>
92        </activity>
93
94        <!-- AdMob App ID -->
95        <meta-data
95-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:33:9-35:70
96            android:name="com.google.android.gms.ads.APPLICATION_ID"
96-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:34:13-69
97            android:value="ca-app-pub-8316648275077982~8688147372" />
97-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:35:13-67
98
99        <!-- Fix for AdMob manifest merger conflict -->
100        <property
100-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:38:9-41:48
101            android:name="android.adservices.AD_SERVICES_CONFIG"
101-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:39:13-65
102            android:resource="@xml/gma_ad_services_config" />
102-->C:\projetos\MileageTracker\app\src\main\AndroidManifest.xml:40:13-59
103
104        <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
105        <activity
105-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:73:9-78:43
106            android:name="com.google.android.gms.ads.AdActivity"
106-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:74:13-65
107            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
107-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:75:13-122
108            android:exported="false"
108-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:76:13-37
109            android:theme="@android:style/Theme.Translucent" />
109-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:77:13-61
110
111        <provider
111-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:80:9-85:43
112            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
112-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:81:13-76
113            android:authorities="com.sirch.mileagetracker.mobileadsinitprovider"
113-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:82:13-73
114            android:exported="false"
114-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:83:13-37
115            android:initOrder="100" />
115-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:84:13-36
116
117        <service
117-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:87:9-91:43
118            android:name="com.google.android.gms.ads.AdService"
118-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:88:13-64
119            android:enabled="true"
119-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:89:13-35
120            android:exported="false" />
120-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:90:13-37
121
122        <activity
122-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:93:9-97:43
123            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
123-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:94:13-82
124            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
124-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:95:13-122
125            android:exported="false" />
125-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:96:13-37
126        <activity
126-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:98:9-105:43
127            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
127-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:99:13-82
128            android:excludeFromRecents="true"
128-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:100:13-46
129            android:exported="false"
129-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:101:13-37
130            android:launchMode="singleTask"
130-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:102:13-44
131            android:taskAffinity=""
131-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:103:13-36
132            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
132-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:104:13-72
133
134        <provider
134-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
135            android:name="androidx.startup.InitializationProvider"
135-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
136            android:authorities="com.sirch.mileagetracker.androidx-startup"
136-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
137            android:exported="false" >
137-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
138            <meta-data
138-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
139                android:name="androidx.work.WorkManagerInitializer"
139-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
140                android:value="androidx.startup" />
140-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
141            <meta-data
141-->[androidx.emoji2:emoji2:1.3.0] C:\projetos\MileageTracker\caches\8.14\transforms\9ada9e74759491cf6fbbfd590bfa31ae\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
142                android:name="androidx.emoji2.text.EmojiCompatInitializer"
142-->[androidx.emoji2:emoji2:1.3.0] C:\projetos\MileageTracker\caches\8.14\transforms\9ada9e74759491cf6fbbfd590bfa31ae\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
143                android:value="androidx.startup" />
143-->[androidx.emoji2:emoji2:1.3.0] C:\projetos\MileageTracker\caches\8.14\transforms\9ada9e74759491cf6fbbfd590bfa31ae\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
144            <meta-data
144-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\projetos\MileageTracker\caches\8.14\transforms\c77b8313fb9fa93672f2b458280155ca\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
145                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
145-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\projetos\MileageTracker\caches\8.14\transforms\c77b8313fb9fa93672f2b458280155ca\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
146                android:value="androidx.startup" />
146-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\projetos\MileageTracker\caches\8.14\transforms\c77b8313fb9fa93672f2b458280155ca\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
147            <meta-data
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
148                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
149                android:value="androidx.startup" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
150        </provider>
151
152        <service
152-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
153            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
153-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
154            android:directBootAware="false"
154-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
155            android:enabled="@bool/enable_system_alarm_service_default"
155-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
156            android:exported="false" />
156-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
157        <service
157-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
158            android:name="androidx.work.impl.background.systemjob.SystemJobService"
158-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
159            android:directBootAware="false"
159-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
160            android:enabled="@bool/enable_system_job_service_default"
160-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
161            android:exported="true"
161-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
162            android:permission="android.permission.BIND_JOB_SERVICE" />
162-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
163        <service
163-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
164            android:name="androidx.work.impl.foreground.SystemForegroundService"
164-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
166            android:enabled="@bool/enable_system_foreground_service_default"
166-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
167            android:exported="false" />
167-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
168
169        <receiver
169-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
170            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
170-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
171            android:directBootAware="false"
171-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
172            android:enabled="true"
172-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
173            android:exported="false" />
173-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
174        <receiver
174-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
175            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
175-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
176            android:directBootAware="false"
176-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
177            android:enabled="false"
177-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
178            android:exported="false" >
178-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
179            <intent-filter>
179-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
180                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
180-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
180-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
181                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
181-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
181-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
182            </intent-filter>
183        </receiver>
184        <receiver
184-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
185            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
185-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
186            android:directBootAware="false"
186-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
187            android:enabled="false"
187-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
188            android:exported="false" >
188-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
189            <intent-filter>
189-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
190                <action android:name="android.intent.action.BATTERY_OKAY" />
190-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
190-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
191                <action android:name="android.intent.action.BATTERY_LOW" />
191-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
191-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
192            </intent-filter>
193        </receiver>
194        <receiver
194-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
195            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
195-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
196            android:directBootAware="false"
196-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
197            android:enabled="false"
197-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
198            android:exported="false" >
198-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
199            <intent-filter>
199-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
200                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
200-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
200-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
201                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
201-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
201-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
202            </intent-filter>
203        </receiver>
204        <receiver
204-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
205            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
205-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
206            android:directBootAware="false"
206-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
207            android:enabled="false"
207-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
208            android:exported="false" >
208-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
209            <intent-filter>
209-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
210                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
210-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
210-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
211            </intent-filter>
212        </receiver>
213        <receiver
213-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
214            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
214-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
215            android:directBootAware="false"
215-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
216            android:enabled="false"
216-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
217            android:exported="false" >
217-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
218            <intent-filter>
218-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
219                <action android:name="android.intent.action.BOOT_COMPLETED" />
219-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
219-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
220                <action android:name="android.intent.action.TIME_SET" />
220-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
220-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
221                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
221-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
221-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
222            </intent-filter>
223        </receiver>
224        <receiver
224-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
225            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
225-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
226            android:directBootAware="false"
226-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
227            android:enabled="@bool/enable_system_alarm_service_default"
227-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
228            android:exported="false" >
228-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
229            <intent-filter>
229-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
230                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
230-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
230-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
231            </intent-filter>
232        </receiver>
233        <receiver
233-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
234            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
234-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
235            android:directBootAware="false"
235-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
236            android:enabled="true"
236-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
237            android:exported="true"
237-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
238            android:permission="android.permission.DUMP" >
238-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
239            <intent-filter>
239-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
240                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
240-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
240-->[androidx.work:work-runtime:2.7.0] C:\projetos\MileageTracker\caches\8.14\transforms\a6eb76435759cf266af4e2b252857fcf\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
241            </intent-filter>
242        </receiver>
243
244        <service
244-->[androidx.room:room-runtime:2.6.1] C:\projetos\MileageTracker\caches\8.14\transforms\4f09aa758a1e6e549b8dfe1fa013af4f\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
245            android:name="androidx.room.MultiInstanceInvalidationService"
245-->[androidx.room:room-runtime:2.6.1] C:\projetos\MileageTracker\caches\8.14\transforms\4f09aa758a1e6e549b8dfe1fa013af4f\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
246            android:directBootAware="true"
246-->[androidx.room:room-runtime:2.6.1] C:\projetos\MileageTracker\caches\8.14\transforms\4f09aa758a1e6e549b8dfe1fa013af4f\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
247            android:exported="false" />
247-->[androidx.room:room-runtime:2.6.1] C:\projetos\MileageTracker\caches\8.14\transforms\4f09aa758a1e6e549b8dfe1fa013af4f\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
248        <service
248-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\e64aaad2bad855dad34787359d06f6ca\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:8:9-14:19
249            android:name="com.google.firebase.components.ComponentDiscoveryService"
249-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\e64aaad2bad855dad34787359d06f6ca\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:9:13-84
250            android:directBootAware="true"
250-->[com.google.firebase:firebase-common:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
251            android:exported="false" >
251-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\e64aaad2bad855dad34787359d06f6ca\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:10:13-37
252            <meta-data
252-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\e64aaad2bad855dad34787359d06f6ca\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:11:13-13:85
253                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
253-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\e64aaad2bad855dad34787359d06f6ca\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:12:17-119
254                android:value="com.google.firebase.components.ComponentRegistrar" />
254-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\e64aaad2bad855dad34787359d06f6ca\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:13:17-82
255            <meta-data
255-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
256                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
256-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
257                android:value="com.google.firebase.components.ComponentRegistrar" />
257-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
258            <meta-data
258-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\8338b691d2cb2763d1c9fd1407ce3748\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
259                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
259-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\8338b691d2cb2763d1c9fd1407ce3748\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
260                android:value="com.google.firebase.components.ComponentRegistrar" />
260-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\8338b691d2cb2763d1c9fd1407ce3748\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
261            <meta-data
261-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\416f03570aa05b43cf0e1f2c187d9e5d\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
262                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
262-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\416f03570aa05b43cf0e1f2c187d9e5d\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
263                android:value="com.google.firebase.components.ComponentRegistrar" />
263-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\416f03570aa05b43cf0e1f2c187d9e5d\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
264            <meta-data
264-->[com.google.firebase:firebase-installations:18.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\7a4b5f6b7cc362af12067a432145db4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
265                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
265-->[com.google.firebase:firebase-installations:18.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\7a4b5f6b7cc362af12067a432145db4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
266                android:value="com.google.firebase.components.ComponentRegistrar" />
266-->[com.google.firebase:firebase-installations:18.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\7a4b5f6b7cc362af12067a432145db4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
267            <meta-data
267-->[com.google.firebase:firebase-installations:18.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\7a4b5f6b7cc362af12067a432145db4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
268                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
268-->[com.google.firebase:firebase-installations:18.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\7a4b5f6b7cc362af12067a432145db4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
269                android:value="com.google.firebase.components.ComponentRegistrar" />
269-->[com.google.firebase:firebase-installations:18.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\7a4b5f6b7cc362af12067a432145db4e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
270            <meta-data
270-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\f8d001896dc4bc409aba7ffe659f2801\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
271                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
271-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\f8d001896dc4bc409aba7ffe659f2801\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
272                android:value="com.google.firebase.components.ComponentRegistrar" />
272-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\f8d001896dc4bc409aba7ffe659f2801\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
273            <meta-data
273-->[com.google.firebase:firebase-common:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
274                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
274-->[com.google.firebase:firebase-common:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
275                android:value="com.google.firebase.components.ComponentRegistrar" />
275-->[com.google.firebase:firebase-common:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
276        </service>
277
278        <activity
278-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
279            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
279-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
280            android:excludeFromRecents="true"
280-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
281            android:exported="true"
281-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
282            android:launchMode="singleTask"
282-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
283            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
283-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
284            <intent-filter>
284-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
285                <action android:name="android.intent.action.VIEW" />
285-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:39:13-65
285-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:39:21-62
286
287                <category android:name="android.intent.category.DEFAULT" />
287-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
287-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
288                <category android:name="android.intent.category.BROWSABLE" />
288-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:41:13-74
288-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:41:23-71
289
290                <data
290-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
291                    android:host="firebase.auth"
292                    android:path="/"
293                    android:scheme="genericidp" />
293-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
294            </intent-filter>
295        </activity>
296        <activity
296-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
297            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
297-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
298            android:excludeFromRecents="true"
298-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
299            android:exported="true"
299-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
300            android:launchMode="singleTask"
300-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
301            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
301-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
302            <intent-filter>
302-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
303                <action android:name="android.intent.action.VIEW" />
303-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:39:13-65
303-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:39:21-62
304
305                <category android:name="android.intent.category.DEFAULT" />
305-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
305-->[com.google.firebase:firebase-auth:23.1.0] C:\projetos\MileageTracker\caches\8.14\transforms\7f2e4ecf1262b521cd4ea26b0c6b48df\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
306                <category android:name="android.intent.category.BROWSABLE" />
306-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:41:13-74
306-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:41:23-71
307
308                <data
308-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:43:13-44
309                    android:host="firebase.auth"
310                    android:path="/"
311                    android:scheme="recaptcha" />
311-->[com.google.android.gms:play-services-ads-lite:23.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\a24ca3f7559becade3059179d8654f9c\transformed\play-services-ads-lite-23.5.0\AndroidManifest.xml:43:19-41
312            </intent-filter>
313        </activity>
314
315        <service
315-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
316            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
316-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
317            android:enabled="true"
317-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
318            android:exported="false" >
318-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
319            <meta-data
319-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
320                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
320-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
321                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
321-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
322        </service>
323
324        <activity
324-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
325            android:name="androidx.credentials.playservices.HiddenActivity"
325-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
326            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
326-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
327            android:enabled="true"
327-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
328            android:exported="false"
328-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
329            android:fitsSystemWindows="true"
329-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
330            android:theme="@style/Theme.Hidden" >
330-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\projetos\MileageTracker\caches\8.14\transforms\261871324870a4e9135a74180032be55\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
331        </activity>
332        <activity
332-->[com.google.android.gms:play-services-auth:21.2.0] C:\projetos\MileageTracker\caches\8.14\transforms\1d42414bad37bd09d7156dd8ae399237\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
333            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
333-->[com.google.android.gms:play-services-auth:21.2.0] C:\projetos\MileageTracker\caches\8.14\transforms\1d42414bad37bd09d7156dd8ae399237\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
334            android:excludeFromRecents="true"
334-->[com.google.android.gms:play-services-auth:21.2.0] C:\projetos\MileageTracker\caches\8.14\transforms\1d42414bad37bd09d7156dd8ae399237\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
335            android:exported="false"
335-->[com.google.android.gms:play-services-auth:21.2.0] C:\projetos\MileageTracker\caches\8.14\transforms\1d42414bad37bd09d7156dd8ae399237\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
336            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
336-->[com.google.android.gms:play-services-auth:21.2.0] C:\projetos\MileageTracker\caches\8.14\transforms\1d42414bad37bd09d7156dd8ae399237\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
337        <!--
338            Service handling Google Sign-In user revocation. For apps that do not integrate with
339            Google Sign-In, this service will never be started.
340        -->
341        <service
341-->[com.google.android.gms:play-services-auth:21.2.0] C:\projetos\MileageTracker\caches\8.14\transforms\1d42414bad37bd09d7156dd8ae399237\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
342            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
342-->[com.google.android.gms:play-services-auth:21.2.0] C:\projetos\MileageTracker\caches\8.14\transforms\1d42414bad37bd09d7156dd8ae399237\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
343            android:exported="true"
343-->[com.google.android.gms:play-services-auth:21.2.0] C:\projetos\MileageTracker\caches\8.14\transforms\1d42414bad37bd09d7156dd8ae399237\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
344            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
344-->[com.google.android.gms:play-services-auth:21.2.0] C:\projetos\MileageTracker\caches\8.14\transforms\1d42414bad37bd09d7156dd8ae399237\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
345            android:visibleToInstantApps="true" />
345-->[com.google.android.gms:play-services-auth:21.2.0] C:\projetos\MileageTracker\caches\8.14\transforms\1d42414bad37bd09d7156dd8ae399237\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
346
347        <meta-data
347-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
348            android:name="com.google.android.play.billingclient.version"
348-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
349            android:value="7.1.1" />
349-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
350
351        <activity
351-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
352            android:name="com.android.billingclient.api.ProxyBillingActivity"
352-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
353            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
353-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
354            android:exported="false"
354-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
355            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
355-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
356        <activity
356-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
357            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
357-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
358            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
358-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
359            android:exported="false"
359-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
360            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
360-->[com.android.billingclient:billing:7.1.1] C:\projetos\MileageTracker\caches\8.14\transforms\a8e7831dc305e4b622d3895f10d29aa9\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
361
362        <provider
362-->[com.google.firebase:firebase-common:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
363            android:name="com.google.firebase.provider.FirebaseInitProvider"
363-->[com.google.firebase:firebase-common:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
364            android:authorities="com.sirch.mileagetracker.firebaseinitprovider"
364-->[com.google.firebase:firebase-common:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
365            android:directBootAware="true"
365-->[com.google.firebase:firebase-common:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
366            android:exported="false"
366-->[com.google.firebase:firebase-common:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
367            android:initOrder="100" />
367-->[com.google.firebase:firebase-common:21.0.0] C:\projetos\MileageTracker\caches\8.14\transforms\9f77e88b92689444bef462d4776195a9\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
368
369        <receiver
369-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
370            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
370-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
371            android:enabled="true"
371-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
372            android:exported="false" >
372-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
373        </receiver>
374
375        <service
375-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
376            android:name="com.google.android.gms.measurement.AppMeasurementService"
376-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
377            android:enabled="true"
377-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
378            android:exported="false" />
378-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
379        <service
379-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
380            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
380-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
381            android:enabled="true"
381-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
382            android:exported="false"
382-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
383            android:permission="android.permission.BIND_JOB_SERVICE" />
383-->[com.google.android.gms:play-services-measurement:22.1.2] C:\projetos\MileageTracker\caches\8.14\transforms\eba3ac19b92f986c81205c52349e3f8c\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
384
385        <activity
385-->[com.google.android.gms:play-services-base:18.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\3793d766ef17a5c374cbb3773985b3be\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
386            android:name="com.google.android.gms.common.api.GoogleApiActivity"
386-->[com.google.android.gms:play-services-base:18.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\3793d766ef17a5c374cbb3773985b3be\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
387            android:exported="false"
387-->[com.google.android.gms:play-services-base:18.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\3793d766ef17a5c374cbb3773985b3be\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
388            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
388-->[com.google.android.gms:play-services-base:18.5.0] C:\projetos\MileageTracker\caches\8.14\transforms\3793d766ef17a5c374cbb3773985b3be\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
389
390        <uses-library
390-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\projetos\MileageTracker\caches\8.14\transforms\ccf88f817e5cd212be4877c79da23b54\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
391            android:name="android.ext.adservices"
391-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\projetos\MileageTracker\caches\8.14\transforms\ccf88f817e5cd212be4877c79da23b54\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
392            android:required="false" />
392-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\projetos\MileageTracker\caches\8.14\transforms\ccf88f817e5cd212be4877c79da23b54\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
393
394        <meta-data
394-->[com.google.android.gms:play-services-basement:18.4.0] C:\projetos\MileageTracker\caches\8.14\transforms\51f776a662999a2c4066cf59dc47f5c4\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
395            android:name="com.google.android.gms.version"
395-->[com.google.android.gms:play-services-basement:18.4.0] C:\projetos\MileageTracker\caches\8.14\transforms\51f776a662999a2c4066cf59dc47f5c4\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
396            android:value="@integer/google_play_services_version" />
396-->[com.google.android.gms:play-services-basement:18.4.0] C:\projetos\MileageTracker\caches\8.14\transforms\51f776a662999a2c4066cf59dc47f5c4\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
397
398        <receiver
398-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
399            android:name="androidx.profileinstaller.ProfileInstallReceiver"
399-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
400            android:directBootAware="false"
400-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
401            android:enabled="true"
401-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
402            android:exported="true"
402-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
403            android:permission="android.permission.DUMP" >
403-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
404            <intent-filter>
404-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
405                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
405-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
405-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
406            </intent-filter>
407            <intent-filter>
407-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
408                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
408-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
408-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
409            </intent-filter>
410            <intent-filter>
410-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
411                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
411-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
411-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
412            </intent-filter>
413            <intent-filter>
413-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
414                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
414-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
414-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\projetos\MileageTracker\caches\8.14\transforms\4d47dce466e0b0df295ce7ec310f4bfc\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
415            </intent-filter>
416        </receiver>
417
418        <service
418-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\89a12dadb79ce4186493629c0f92b02e\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
419            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
419-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\89a12dadb79ce4186493629c0f92b02e\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
420            android:exported="false" >
420-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\89a12dadb79ce4186493629c0f92b02e\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
421            <meta-data
421-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\89a12dadb79ce4186493629c0f92b02e\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
422                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
422-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\89a12dadb79ce4186493629c0f92b02e\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
423                android:value="cct" />
423-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\89a12dadb79ce4186493629c0f92b02e\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
424        </service>
425        <service
425-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\2045263076c857ef789ac09dabde30af\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
426            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
426-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\2045263076c857ef789ac09dabde30af\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
427            android:exported="false"
427-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\2045263076c857ef789ac09dabde30af\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
428            android:permission="android.permission.BIND_JOB_SERVICE" >
428-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\2045263076c857ef789ac09dabde30af\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
429        </service>
430
431        <receiver
431-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\2045263076c857ef789ac09dabde30af\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
432            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
432-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\2045263076c857ef789ac09dabde30af\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
433            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
433-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\projetos\MileageTracker\caches\8.14\transforms\2045263076c857ef789ac09dabde30af\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
434        <activity
434-->[com.google.android.play:core-common:2.0.3] C:\projetos\MileageTracker\caches\8.14\transforms\3990bfda9ffd8f92176a2bef9f4c6552\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
435            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
435-->[com.google.android.play:core-common:2.0.3] C:\projetos\MileageTracker\caches\8.14\transforms\3990bfda9ffd8f92176a2bef9f4c6552\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
436            android:exported="false"
436-->[com.google.android.play:core-common:2.0.3] C:\projetos\MileageTracker\caches\8.14\transforms\3990bfda9ffd8f92176a2bef9f4c6552\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
437            android:stateNotNeeded="true"
437-->[com.google.android.play:core-common:2.0.3] C:\projetos\MileageTracker\caches\8.14\transforms\3990bfda9ffd8f92176a2bef9f4c6552\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
438            android:theme="@style/Theme.PlayCore.Transparent" />
438-->[com.google.android.play:core-common:2.0.3] C:\projetos\MileageTracker\caches\8.14\transforms\3990bfda9ffd8f92176a2bef9f4c6552\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
439    </application>
440
441</manifest>
