# 🚀 Mileage Tracker - <PERSON><PERSON><PERSON> de Deploy para Produção

## 📋 Pré-requisitos

### ✅ Verificações Iniciais
- [ ] Android Studio instalado (versão mais recente)
- [ ] JDK 11 ou superior configurado
- [ ] Conta Google Play Console criada
- [ ] Conta Firebase configurada
- [ ] Conta AdMob configurada

## 🎨 Personalização do App

### 1. Logo e Ícone do App

#### ✅ Ícone Já Configurado
O app já possui um ícone personalizado com:
- **Tema:** Avião (representando viagens/milhas)
- **Cores:** Azul (#2196F3) com detalhes dourados (#FFC107)
- **Formato:** Adaptive Icon (funciona em todos os launchers)

#### 🔄 Para Personalizar o Ícone:

1. **Usando Android Studio:**
   ```
   Clique direito em app/src/main/res
   → New → Image Asset
   → Icon Type: Launcher Icons (Adaptive and Legacy)
   → Foreground Layer: Escolha sua imagem
   → Background Layer: Escolha cor ou imagem
   → Generate
   ```

2. **Manualmente:**
   - <PERSON>e `app/src/main/res/drawable/ic_launcher_foreground.xml`
   - Edite `app/src/main/res/drawable/ic_launcher_background.xml`

### 2. Nome do App

#### Atual: "Mileage Tracker"

Para alterar:
```xml
<!-- app/src/main/res/values/strings.xml -->
<string name="app_name">Seu Nome Aqui</string>
```

### 3. Cores do Tema

#### Cores Atuais:
- **Primary:** #2196F3 (Azul)
- **Secondary:** #FFC107 (Dourado)

Para alterar:
```xml
<!-- app/src/main/res/values/themes.xml -->
<color name="md_theme_light_primary">#SuaCor</color>
<color name="md_theme_light_secondary">#SuaCor</color>
```

## 🔧 Configurações de Produção

### ✅ Correções Aplicadas para Produção

#### 1. Dados de Teste Removidos:
- ✅ **Compras de teste** não são mais inseridas automaticamente
- ✅ **Apenas programas padrão** são criados na primeira execução
- ✅ **Usuários adicionam dados reais**

#### 2. Anúncios de Produção:
- ✅ **BuildConfig.DEBUG** detecta automaticamente o tipo de build
- ✅ **Debug build** = anúncios de teste
- ✅ **Release build** = anúncios de produção

#### 3. Login Limpo:
- ✅ **Botão de teste removido** da tela de login
- ✅ **Apenas Google Sign-In** disponível
- ✅ **Interface profissional**

### 1. Versioning

#### Atualizar Versão:
```kotlin
// app/build.gradle.kts
defaultConfig {
    versionCode = 2        // Incrementar para cada release
    versionName = "1.1"    // Versão visível ao usuário
}
```

### 2. Firebase Configuration

#### ✅ Já Configurado:
- `google-services.json` presente
- Firebase Auth habilitado
- Firebase Analytics habilitado

#### Para Verificar:
1. Acesse [Firebase Console](https://console.firebase.google.com)
2. Selecione seu projeto
3. Verifique se o package name está correto: `com.sirch.mileagetracker`

### 3. AdMob Configuration

#### ✅ Já Configurado:
- AdMob ID: `ca-app-pub-8316648275077982/2235862620`
- Configuração em `app/src/main/res/xml/gma_ad_services_config.xml`

#### Para Alterar:
```xml
<!-- app/src/main/res/xml/gma_ad_services_config.xml -->
<gma:adServiceConfig xmlns:gma="https://www.google.com/schemas/gma-ads-config">
    <gma:adUnitId gma:adUnitId="ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX"/>
</gma:adServiceConfig>
```

## 🔐 Assinatura do App

### 1. Criar Keystore

```bash
# No terminal do Android Studio
keytool -genkey -v -keystore mileage-tracker-release.keystore -alias mileage-tracker -keyalg RSA -keysize 2048 -validity 10000
```

### 2. Configurar Signing

#### Criar arquivo `keystore.properties` na raiz do projeto:
```properties
storeFile=mileage-tracker-release.keystore
storePassword=SUA_SENHA_STORE
keyAlias=mileage-tracker
keyPassword=SUA_SENHA_KEY
```

#### Adicionar ao `app/build.gradle.kts`:
```kotlin
// No topo do arquivo
val keystorePropertiesFile = rootProject.file("keystore.properties")
val keystoreProperties = Properties()
keystoreProperties.load(FileInputStream(keystorePropertiesFile))

android {
    signingConfigs {
        create("release") {
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
            storeFile = file(keystoreProperties["storeFile"] as String)
            storePassword = keystoreProperties["storePassword"] as String
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("release")
            // ... resto da configuração
        }
    }
}
```

## 📦 Build de Produção

### 1. Clean Build

```bash
# No terminal do Android Studio
./gradlew clean
./gradlew assembleRelease
```

### 2. Gerar AAB (Recomendado)

```bash
./gradlew bundleRelease
```

**Arquivo gerado:** `app/build/outputs/bundle/release/app-release.aab`

### 3. Gerar APK

```bash
./gradlew assembleRelease
```

**Arquivo gerado:** `app/build/outputs/apk/release/app-release.apk`

## 🧪 Testes Finais

### ✅ Checklist de Testes

- [ ] **Funcionalidade básica:** Criar programa, adicionar compras
- [ ] **Performance:** Scroll fluido, sem travamentos
- [ ] **Orientação:** Funciona em portrait e landscape
- [ ] **Dark mode:** Temas claro e escuro funcionam
- [ ] **Navegação:** Todas as telas acessíveis
- [ ] **Dados:** Persistência funcionando
- [ ] **Ads:** AdMob carregando (se configurado)
- [ ] **Auth:** Login/logout funcionando (se configurado)

### 🔍 Testes de Release

1. **Instalar APK/AAB em dispositivo físico**
2. **Testar sem debugger conectado**
3. **Verificar performance em dispositivos diferentes**
4. **Testar com dados reais**

## 📱 Deploy na Play Store

### 1. Preparar Assets

#### Screenshots Necessários:
- **Phone:** 2-8 screenshots (16:9 ou 9:16)
- **Tablet:** 1-8 screenshots (opcional)

#### Tamanhos:
- **Phone:** 1080x1920 ou 1080x2340
- **Tablet:** 1200x1920 ou 1920x1200

### 2. Informações da Store

#### Título: "Mileage Tracker"
#### Descrição Curta (80 chars):
```
Rastreie suas milhas e maximize seus benefícios de programas de fidelidade
```

#### Descrição Longa:
```
🛫 Mileage Tracker - Seu Companheiro de Milhas

Maximize seus benefícios de programas de fidelidade com o Mileage Tracker!
Acompanhe suas compras, acumule milhas e nunca perca uma oportunidade de viajar.

✨ RECURSOS PRINCIPAIS:
• 📊 Acompanhe múltiplos programas de milhas
• 💳 Registre compras e ganhos automáticos
• 📈 Estatísticas detalhadas de acúmulo
• 🌙 Modo escuro elegante
• 📱 Interface moderna e intuitiva

🎯 PERFEITO PARA:
• Viajantes frequentes
• Entusiastas de milhas
• Quem quer maximizar benefícios
• Organização financeira

Baixe agora e transforme cada compra em uma oportunidade de viagem! ✈️
```

### 3. Upload na Play Console

1. **Acesse:** [Google Play Console](https://play.google.com/console)
2. **Criar novo app** ou **selecionar existente**
3. **Upload do AAB:** Production → Create new release
4. **Preencher informações** da store
5. **Adicionar screenshots**
6. **Configurar preço** (gratuito/pago)
7. **Review e publicar**

## 🔄 Atualizações Futuras

### Versionamento Semântico:
- **Major (X.0.0):** Mudanças incompatíveis
- **Minor (1.X.0):** Novas funcionalidades
- **Patch (1.0.X):** Correções de bugs

### Processo de Update:
1. **Incrementar versionCode e versionName**
2. **Testar thoroughly**
3. **Gerar novo AAB**
4. **Upload na Play Console**
5. **Staged rollout** (recomendado)

## 📊 Monitoramento

### Firebase Analytics:
- **Eventos customizados** já configurados
- **Crash reporting** automático
- **Performance monitoring** ativo

### Play Console:
- **Crash reports**
- **ANR reports**
- **User feedback**
- **Performance metrics**

## 🆘 Troubleshooting

### Problemas Comuns:

#### Build Falha:
```bash
./gradlew clean
./gradlew --refresh-dependencies assembleRelease
```

#### Keystore Issues:
- Verificar paths no `keystore.properties`
- Confirmar senhas corretas
- Keystore deve estar na raiz do projeto

#### Upload Rejeitado:
- Verificar versionCode incrementado
- Confirmar assinatura correta
- Checar target SDK atualizado

---

## 🎉 Pronto para Produção!

Seu Mileage Tracker está otimizado e pronto para o mundo!

**Próximos passos:**
1. ✅ Personalizar conforme necessário
2. ✅ Configurar assinatura
3. ✅ Gerar build de release
4. ✅ Testar thoroughly
5. ✅ Publicar na Play Store

**Boa sorte com seu lançamento! 🚀**
