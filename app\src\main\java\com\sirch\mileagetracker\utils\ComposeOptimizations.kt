package com.sirch.mileagetracker.utils

import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Performance optimizations for Compose
 */

/**
 * Stable wrapper for unstable types to prevent unnecessary recompositions
 */
@Stable
data class StableHolder<T>(val item: T)

/**
 * Remember a stable holder for unstable types
 */
@Composable
fun <T> rememberStableHolder(item: T): StableHolder<T> {
    return remember(item) { StableHolder(item) }
}

/**
 * Optimized derivedStateOf for expensive calculations
 */
@Composable
fun <T> rememberDerivedState(
    vararg keys: Any?,
    calculation: () -> T
): State<T> {
    return remember(*keys) {
        derivedStateOf(calculation)
    }
}

/**
 * Optimized remember for expensive object creation
 */
@Composable
inline fun <T> rememberCached(
    vararg keys: Any?,
    crossinline factory: () -> T
): T {
    return remember(*keys) { factory() }
}

/**
 * Convert Dp to Px with caching
 */
@Composable
fun Dp.toPxCached(): Float {
    val density = LocalDensity.current
    return remember(this, density) {
        with(density) { <EMAIL>() }
    }
}

/**
 * Stable wrapper for lambda functions to prevent recomposition
 */
@Stable
class StableLambda<T>(val lambda: T)

/**
 * Remember a stable lambda
 */
@Composable
fun <T> rememberStableLambda(lambda: T): StableLambda<T> {
    return remember { StableLambda(lambda) }
}

/**
 * Optimized state for lists to prevent unnecessary recompositions
 */
@Composable
fun <T> rememberStableList(list: List<T>): List<T> {
    return remember(list.size, list.hashCode()) { list }
}

/**
 * Debounced state for text input optimization
 */
@Composable
fun rememberDebouncedState(
    initialValue: String,
    delayMillis: Long = 300L
): MutableState<String> {
    val state = remember { mutableStateOf(initialValue) }
    val debouncedState = remember { mutableStateOf(initialValue) }
    
    LaunchedEffect(state.value) {
        kotlinx.coroutines.delay(delayMillis)
        debouncedState.value = state.value
    }
    
    return state
}

/**
 * Optimized key for LazyColumn items
 */
fun <T> generateStableKey(item: T, index: Int): String {
    return "${item.hashCode()}_$index"
}

/**
 * Memory-efficient image loading state
 */
@Stable
data class ImageLoadingState(
    val isLoading: Boolean = false,
    val hasError: Boolean = false,
    val isSuccess: Boolean = false
)

/**
 * Optimized content padding calculation
 */
@Composable
fun rememberContentPadding(
    top: Dp = 0.dp,
    bottom: Dp = 0.dp,
    start: Dp = 0.dp,
    end: Dp = 0.dp
): androidx.compose.foundation.layout.PaddingValues {
    return remember(top, bottom, start, end) {
        androidx.compose.foundation.layout.PaddingValues(
            top = top,
            bottom = bottom,
            start = start,
            end = end
        )
    }
}
