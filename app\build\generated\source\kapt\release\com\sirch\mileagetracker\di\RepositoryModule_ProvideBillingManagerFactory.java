package com.sirch.mileagetracker.di;

import android.content.Context;
import com.sirch.mileagetracker.data.billing.BillingManager;
import com.sirch.mileagetracker.data.repository.AdsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideBillingManagerFactory implements Factory<BillingManager> {
  private final Provider<Context> contextProvider;

  private final Provider<AdsRepository> adsRepositoryProvider;

  public RepositoryModule_ProvideBillingManagerFactory(Provider<Context> contextProvider,
      Provider<AdsRepository> adsRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.adsRepositoryProvider = adsRepositoryProvider;
  }

  @Override
  public BillingManager get() {
    return provideBillingManager(contextProvider.get(), adsRepositoryProvider.get());
  }

  public static RepositoryModule_ProvideBillingManagerFactory create(
      Provider<Context> contextProvider, Provider<AdsRepository> adsRepositoryProvider) {
    return new RepositoryModule_ProvideBillingManagerFactory(contextProvider, adsRepositoryProvider);
  }

  public static BillingManager provideBillingManager(Context context, AdsRepository adsRepository) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideBillingManager(context, adsRepository));
  }
}
