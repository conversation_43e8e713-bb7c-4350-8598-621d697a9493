package com.sirch.mileagetracker.presentation.premium.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import com.sirch.mileagetracker.domain.premium.*

/**
 * Card displaying subscription product with pricing and purchase button
 */
@Composable
fun SubscriptionProductCard(
    product: SubscriptionProduct,
    isLoading: Boolean,
    onPurchase: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isRecommended = product.billingPeriod == BillingPeriod.YEARLY

    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isRecommended)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surface
        ),
        border = if (isRecommended)
            CardDefaults.outlinedCardBorder().copy(width = 2.dp)
        else null
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // Recommended badge
            if (isRecommended) {
                Surface(
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    shape = RoundedCornerShape(12.dp),
                    color = MaterialTheme.colorScheme.primary
                ) {
                    Text(
                        text = "🏆 RECOMMENDED",
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                }
                Spacer(modifier = Modifier.height(12.dp))
            }

            // Plan name and billing period
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = product.tier.displayName,
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )

                    Text(
                        text = product.billingPeriod.displayName,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // Discount badge for yearly
                if (product.discountPercentage != null) {
                    Surface(
                        shape = RoundedCornerShape(8.dp),
                        color = MaterialTheme.colorScheme.secondary
                    ) {
                        Text(
                            text = "Save ${product.discountPercentage}%",
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                            style = MaterialTheme.typography.labelSmall,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onSecondary
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Pricing
            Row(
                verticalAlignment = Alignment.Bottom
            ) {
                Text(
                    text = product.formattedPrice,
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )

                if (product.billingPeriod != BillingPeriod.LIFETIME) {
                    Text(
                        text = "/${product.billingPeriod.displayName.lowercase()}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }
            }

            // Monthly equivalent for yearly plans
            if (product.billingPeriod == BillingPeriod.YEARLY) {
                val monthlyEquivalent = String.format("R$ %.2f", product.getMonthlyEquivalentPrice() / 1_000_000.0)
                Text(
                    text = "Equivalent to $monthlyEquivalent/month",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // Introductory pricing if available
            product.introductoryPrice?.let { introPrice ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "First month: $introPrice",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.secondary,
                    fontWeight = FontWeight.Medium
                )
            }

            // Free trial if available
            product.freeTrialPeriod?.let { trialPeriod ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "🎁 Free trial: $trialPeriod",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.secondary,
                    fontWeight = FontWeight.Medium
                )
            }

            Spacer(modifier = Modifier.height(20.dp))

            // Purchase button
            Button(
                onClick = onPurchase,
                modifier = Modifier.fillMaxWidth(),
                enabled = !isLoading,
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isRecommended)
                        MaterialTheme.colorScheme.primary
                    else
                        MaterialTheme.colorScheme.secondary
                )
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                } else {
                    Text(
                        text = when (product.billingPeriod) {
                            BillingPeriod.LIFETIME -> "Buy Once"
                            else -> "Subscribe"
                        },
                        fontWeight = FontWeight.Bold
                    )
                }
            }

            // Additional info for lifetime
            if (product.billingPeriod == BillingPeriod.LIFETIME) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "✨ One-time payment • No recurring charges",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * Compact subscription product card for inline display
 */
@Composable
fun CompactSubscriptionProductCard(
    product: SubscriptionProduct,
    isSelected: Boolean,
    onSelect: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surface
        ),
        border = if (isSelected)
            CardDefaults.outlinedCardBorder().copy(width = 2.dp)
        else
            CardDefaults.outlinedCardBorder(),
        onClick = onSelect
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = product.billingPeriod.displayName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = product.formattedPrice,
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Medium
                )

                if (product.billingPeriod == BillingPeriod.YEARLY) {
                    val monthlyEquivalent = String.format("R$ %.2f", product.getMonthlyEquivalentPrice() / 1_000_000.0)
                    Text(
                        text = "$monthlyEquivalent/month",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            if (product.discountPercentage != null) {
                Surface(
                    shape = RoundedCornerShape(8.dp),
                    color = MaterialTheme.colorScheme.secondary
                ) {
                    Text(
                        text = "-${product.discountPercentage}%",
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSecondary
                    )
                }
            }
        }
    }
}
