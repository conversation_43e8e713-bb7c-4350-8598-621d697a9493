# Configuração do Firebase

## Passos para configurar o Firebase no projeto:

### 1. Criar projeto no Firebase Console
1. Acesse [Firebase Console](https://console.firebase.google.com/)
2. Clique em "Adicionar projeto"
3. <PERSON>gite o nome do projeto: "Mileage Tracker"
4. Configure o Google Analytics (opcional)

### 2. Adicionar app Android
1. No console do Firebase, clique em "Adicionar app" > Android
2. Package name: `com.sirch.mileagetracker`
3. App nickname: "Mileage Tracker"
4. SHA-1: Obtenha executando: `./gradlew signingReport`

### 3. Baixar google-services.json
1. Baixe o arquivo `google-services.json`
2. Coloque na pasta `app/` do projeto
3. **IMPORTANTE**: Não commite este arquivo no Git (já está no .gitignore)

### 4. Configurar Authentication
1. No Firebase Console, vá em "Authentication" > "Sign-in method"
2. Habilite "Google"
3. Configure o email de suporte do projeto
4. Copie o "Web client ID"

### 5. Atualizar strings.xml
1. Abra `app/src/main/res/values/strings.xml`
2. Substitua `YOUR_WEB_CLIENT_ID_HERE` pelo Web client ID copiado do Firebase

### 6. Configurar AdMob (Opcional)
1. No Firebase Console, vá em "AdMob"
2. Vincule ou crie uma conta AdMob
3. Crie IDs de anúncios para banner e intersticial
4. Atualize o `APPLICATION_ID` no AndroidManifest.xml

### 7. Testar
1. Execute o app
2. Teste o login com Google
3. Verifique se o usuário aparece no Firebase Console > Authentication > Users

## Arquivos importantes:
- `app/google-services.json` - Configuração do Firebase (não versionar)
- `app/src/main/res/values/strings.xml` - Web client ID
- `app/src/main/AndroidManifest.xml` - AdMob App ID

## Troubleshooting:
- Se o login falhar, verifique se o SHA-1 está correto
- Certifique-se de que o package name está correto
- Verifique se o google-services.json está na pasta correta
