package com.sirch.mileagetracker.presentation.splash;

import com.sirch.mileagetracker.domain.usecase.InitializeAppUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SplashViewModel_Factory implements Factory<SplashViewModel> {
  private final Provider<InitializeAppUseCase> initializeAppUseCaseProvider;

  public SplashViewModel_Factory(Provider<InitializeAppUseCase> initializeAppUseCaseProvider) {
    this.initializeAppUseCaseProvider = initializeAppUseCaseProvider;
  }

  @Override
  public SplashViewModel get() {
    return newInstance(initializeAppUseCaseProvider.get());
  }

  public static SplashViewModel_Factory create(
      Provider<InitializeAppUseCase> initializeAppUseCaseProvider) {
    return new SplashViewModel_Factory(initializeAppUseCaseProvider);
  }

  public static SplashViewModel newInstance(InitializeAppUseCase initializeAppUseCase) {
    return new SplashViewModel(initializeAppUseCase);
  }
}
