package com.sirch.mileagetracker.data.local

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.sirch.mileagetracker.data.local.converters.DateConverter
import com.sirch.mileagetracker.data.local.dao.ProgramDao
import com.sirch.mileagetracker.data.local.dao.PurchaseDao
import com.sirch.mileagetracker.data.local.dao.SettingsDao
import com.sirch.mileagetracker.data.local.entities.Program
import com.sirch.mileagetracker.data.local.entities.Purchase
import com.sirch.mileagetracker.data.local.entities.Settings

@Database(
    entities = [
        Program::class,
        Purchase::class,
        Settings::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(DateConverter::class)
abstract class MileageTrackerDatabase : RoomDatabase() {

    abstract fun programDao(): ProgramDao
    abstract fun purchaseDao(): PurchaseDao
    abstract fun settingsDao(): SettingsDao

    companion object {
        const val DATABASE_NAME = "mileage_tracker_database_v2" // Novo nome para forçar recriação

        @Volatile
        private var INSTANCE: MileageTrackerDatabase? = null

        fun getDatabase(context: Context): MileageTrackerDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    MileageTrackerDatabase::class.java,
                    DATABASE_NAME
                )
                .fallbackToDestructiveMigration() // Permite recriar o banco se houver problemas
                .build()
                INSTANCE = instance
                instance
            }
        }

        // Método para limpar a instância (útil para testes)
        fun clearInstance() {
            INSTANCE = null
        }
    }
}
