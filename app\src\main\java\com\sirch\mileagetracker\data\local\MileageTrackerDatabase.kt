package com.sirch.mileagetracker.data.local

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.sirch.mileagetracker.data.local.converters.DateConverter
import com.sirch.mileagetracker.data.local.converters.DateTimeConverter
import com.sirch.mileagetracker.data.local.converters.EnumConverters
import com.sirch.mileagetracker.data.local.dao.*
import com.sirch.mileagetracker.data.local.entities.*

@Database(
    entities = [
        Program::class,
        Purchase::class,
        Settings::class,
        EfficiencyScore::class,
        AggregatedData::class,
        Goal::class,
        NotificationConfig::class,
        BackupHistory::class,
        ReportTemplate::class
    ],
    version = 2,
    exportSchema = false
)
@TypeConverters(DateConverter::class, DateTimeConverter::class, EnumConverters::class)
abstract class MileageTrackerDatabase : RoomDatabase() {

    abstract fun programDao(): ProgramDao
    abstract fun purchaseDao(): PurchaseDao
    abstract fun settingsDao(): SettingsDao
    abstract fun efficiencyScoreDao(): EfficiencyScoreDao
    abstract fun aggregatedDataDao(): AggregatedDataDao
    abstract fun goalDao(): GoalDao
    abstract fun notificationConfigDao(): NotificationConfigDao
    abstract fun backupHistoryDao(): BackupHistoryDao
    abstract fun reportTemplateDao(): ReportTemplateDao

    companion object {
        const val DATABASE_NAME = "mileage_tracker_database"

        @Volatile
        private var INSTANCE: MileageTrackerDatabase? = null

        /**
         * Migration from version 1 to 2 - Add new entities for v0.2.0 features
         */
        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Create efficiency_scores table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS `efficiency_scores` (
                        `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        `userId` TEXT NOT NULL,
                        `programId` INTEGER NOT NULL,
                        `personalAvgCostPerMile` REAL NOT NULL,
                        `aggregateAvgCostPerMile` REAL NOT NULL,
                        `score` REAL NOT NULL,
                        `percentile` REAL NOT NULL,
                        `lastUpdated` INTEGER NOT NULL,
                        `sampleSize` INTEGER NOT NULL,
                        `isDataSharingEnabled` INTEGER NOT NULL DEFAULT 1,
                        FOREIGN KEY(`programId`) REFERENCES `programs`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE
                    )
                """)

                // Create indexes for efficiency_scores
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_efficiency_scores_userId` ON `efficiency_scores` (`userId`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_efficiency_scores_programId` ON `efficiency_scores` (`programId`)")
                database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_efficiency_scores_userId_programId` ON `efficiency_scores` (`userId`, `programId`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_efficiency_scores_lastUpdated` ON `efficiency_scores` (`lastUpdated`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_efficiency_scores_score` ON `efficiency_scores` (`score`)")

                // Create aggregated_data table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS `aggregated_data` (
                        `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        `programId` INTEGER NOT NULL,
                        `anonymizedAvgCostPerMile` REAL NOT NULL,
                        `sampleSize` INTEGER NOT NULL,
                        `lastUpdated` INTEGER NOT NULL,
                        `region` TEXT,
                        `dataVersion` INTEGER NOT NULL DEFAULT 1,
                        `confidenceLevel` REAL NOT NULL,
                        `minCostPerMile` REAL NOT NULL,
                        `maxCostPerMile` REAL NOT NULL,
                        `standardDeviation` REAL NOT NULL,
                        FOREIGN KEY(`programId`) REFERENCES `programs`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE
                    )
                """)

                // Create indexes for aggregated_data
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_aggregated_data_programId` ON `aggregated_data` (`programId`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_aggregated_data_lastUpdated` ON `aggregated_data` (`lastUpdated`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_aggregated_data_region` ON `aggregated_data` (`region`)")
                database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_aggregated_data_programId_region` ON `aggregated_data` (`programId`, `region`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_aggregated_data_sampleSize` ON `aggregated_data` (`sampleSize`)")

                // Create goals table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS `goals` (
                        `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        `userId` TEXT NOT NULL,
                        `programId` INTEGER,
                        `title` TEXT NOT NULL,
                        `description` TEXT,
                        `targetMiles` INTEGER NOT NULL,
                        `currentMiles` INTEGER NOT NULL DEFAULT 0,
                        `targetDate` INTEGER NOT NULL,
                        `isActive` INTEGER NOT NULL DEFAULT 1,
                        `createdAt` INTEGER NOT NULL,
                        `completedAt` INTEGER,
                        `goalType` TEXT NOT NULL DEFAULT 'MILES',
                        `targetValue` REAL,
                        `currentValue` REAL,
                        `isPremium` INTEGER NOT NULL DEFAULT 0,
                        FOREIGN KEY(`programId`) REFERENCES `programs`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE
                    )
                """)

                // Create indexes for goals
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_goals_userId` ON `goals` (`userId`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_goals_programId` ON `goals` (`programId`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_goals_userId_isActive` ON `goals` (`userId`, `isActive`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_goals_targetDate` ON `goals` (`targetDate`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_goals_goalType` ON `goals` (`goalType`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_goals_createdAt` ON `goals` (`createdAt`)")

                // Create notification_configs table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS `notification_configs` (
                        `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        `userId` TEXT NOT NULL,
                        `type` TEXT NOT NULL,
                        `enabled` INTEGER NOT NULL DEFAULT 1,
                        `frequency` TEXT NOT NULL DEFAULT 'WEEKLY',
                        `lastSent` INTEGER,
                        `nextScheduled` INTEGER,
                        `timeOfDay` INTEGER NOT NULL DEFAULT 18,
                        `daysOfWeek` INTEGER NOT NULL DEFAULT 64,
                        `isPremium` INTEGER NOT NULL DEFAULT 0,
                        `customMessage` TEXT,
                        `isQuietHoursEnabled` INTEGER NOT NULL DEFAULT 1,
                        `quietHoursStart` INTEGER NOT NULL DEFAULT 22,
                        `quietHoursEnd` INTEGER NOT NULL DEFAULT 8
                    )
                """)

                // Create indexes for notification_configs
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_notification_configs_userId` ON `notification_configs` (`userId`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_notification_configs_type` ON `notification_configs` (`type`)")
                database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_notification_configs_userId_type` ON `notification_configs` (`userId`, `type`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_notification_configs_enabled` ON `notification_configs` (`enabled`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_notification_configs_nextScheduled` ON `notification_configs` (`nextScheduled`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_notification_configs_lastSent` ON `notification_configs` (`lastSent`)")

                // Create backup_history table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS `backup_history` (
                        `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        `userId` TEXT NOT NULL,
                        `backupTimestamp` INTEGER NOT NULL,
                        `backupSize` INTEGER NOT NULL,
                        `isPremium` INTEGER NOT NULL,
                        `status` TEXT NOT NULL,
                        `backupType` TEXT NOT NULL DEFAULT 'AUTOMATIC',
                        `cloudPath` TEXT,
                        `checksum` TEXT,
                        `deviceInfo` TEXT,
                        `appVersion` TEXT,
                        `dataVersion` INTEGER NOT NULL DEFAULT 1,
                        `compressionRatio` REAL NOT NULL DEFAULT 0.0,
                        `uploadDuration` INTEGER,
                        `errorMessage` TEXT,
                        `isEncrypted` INTEGER NOT NULL DEFAULT 1,
                        `retentionDate` INTEGER
                    )
                """)

                // Create indexes for backup_history
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_backup_history_userId` ON `backup_history` (`userId`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_backup_history_backupTimestamp` ON `backup_history` (`backupTimestamp`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_backup_history_status` ON `backup_history` (`status`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_backup_history_isPremium` ON `backup_history` (`isPremium`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_backup_history_backupType` ON `backup_history` (`backupType`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_backup_history_retentionDate` ON `backup_history` (`retentionDate`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_backup_history_userId_backupTimestamp` ON `backup_history` (`userId`, `backupTimestamp`)")

                // Create report_templates table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS `report_templates` (
                        `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        `userId` TEXT NOT NULL,
                        `name` TEXT NOT NULL,
                        `description` TEXT,
                        `filters` TEXT NOT NULL,
                        `isPremium` INTEGER NOT NULL DEFAULT 0,
                        `createdAt` INTEGER NOT NULL,
                        `lastUsed` INTEGER,
                        `useCount` INTEGER NOT NULL DEFAULT 0,
                        `reportType` TEXT NOT NULL DEFAULT 'SUMMARY',
                        `outputFormat` TEXT NOT NULL DEFAULT 'PDF',
                        `dateRange` TEXT NOT NULL DEFAULT 'LAST_MONTH',
                        `programIds` TEXT,
                        `includeCharts` INTEGER NOT NULL DEFAULT 1,
                        `includeEfficiencyScore` INTEGER NOT NULL DEFAULT 0,
                        `customFields` TEXT,
                        `isShared` INTEGER NOT NULL DEFAULT 0,
                        `shareCode` TEXT
                    )
                """)

                // Create indexes for report_templates
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_report_templates_userId` ON `report_templates` (`userId`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_report_templates_name` ON `report_templates` (`name`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_report_templates_reportType` ON `report_templates` (`reportType`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_report_templates_isPremium` ON `report_templates` (`isPremium`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_report_templates_createdAt` ON `report_templates` (`createdAt`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_report_templates_lastUsed` ON `report_templates` (`lastUsed`)")
                database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_report_templates_shareCode` ON `report_templates` (`shareCode`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_report_templates_userId_name` ON `report_templates` (`userId`, `name`)")
            }
        }

        fun getDatabase(context: Context): MileageTrackerDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    MileageTrackerDatabase::class.java,
                    DATABASE_NAME
                )
                .addMigrations(MIGRATION_1_2)
                .fallbackToDestructiveMigration() // Only as last resort
                .build()
                INSTANCE = instance
                instance
            }
        }

        // Método para limpar a instância (útil para testes)
        fun clearInstance() {
            INSTANCE = null
        }
    }
}
