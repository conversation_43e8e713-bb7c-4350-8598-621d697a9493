int anim fragment_fast_out_extra_slow_in 0x7f010000
int animator fragment_close_enter 0x7f020000
int animator fragment_close_exit 0x7f020001
int animator fragment_fade_enter 0x7f020002
int animator fragment_fade_exit 0x7f020003
int animator fragment_open_enter 0x7f020004
int animator fragment_open_exit 0x7f020005
int attr action 0x7f030000
int attr adSize 0x7f030001
int attr adSizes 0x7f030002
int attr adUnitId 0x7f030003
int attr alpha 0x7f030004
int attr argType 0x7f030005
int attr buttonSize 0x7f030006
int attr circleCrop 0x7f030007
int attr colorScheme 0x7f030008
int attr data 0x7f030009
int attr dataPattern 0x7f03000a
int attr destination 0x7f03000b
int attr enterAnim 0x7f03000c
int attr exitAnim 0x7f03000d
int attr fastScrollEnabled 0x7f03000e
int attr fastScrollHorizontalThumbDrawable 0x7f03000f
int attr fastScrollHorizontalTrackDrawable 0x7f030010
int attr fastScrollVerticalThumbDrawable 0x7f030011
int attr fastScrollVerticalTrackDrawable 0x7f030012
int attr font 0x7f030013
int attr fontProviderAuthority 0x7f030014
int attr fontProviderCerts 0x7f030015
int attr fontProviderFallbackQuery 0x7f030016
int attr fontProviderFetchStrategy 0x7f030017
int attr fontProviderFetchTimeout 0x7f030018
int attr fontProviderPackage 0x7f030019
int attr fontProviderQuery 0x7f03001a
int attr fontProviderSystemFontFamily 0x7f03001b
int attr fontStyle 0x7f03001c
int attr fontVariationSettings 0x7f03001d
int attr fontWeight 0x7f03001e
int attr graph 0x7f03001f
int attr imageAspectRatio 0x7f030020
int attr imageAspectRatioAdjust 0x7f030021
int attr lStar 0x7f030022
int attr launchSingleTop 0x7f030023
int attr layoutManager 0x7f030024
int attr mimeType 0x7f030025
int attr navGraph 0x7f030026
int attr nestedScrollViewStyle 0x7f030027
int attr nullable 0x7f030028
int attr popEnterAnim 0x7f030029
int attr popExitAnim 0x7f03002a
int attr popUpTo 0x7f03002b
int attr popUpToInclusive 0x7f03002c
int attr popUpToSaveState 0x7f03002d
int attr queryPatterns 0x7f03002e
int attr recyclerViewStyle 0x7f03002f
int attr restoreState 0x7f030030
int attr reverseLayout 0x7f030031
int attr route 0x7f030032
int attr scopeUris 0x7f030033
int attr shortcutMatchRequired 0x7f030034
int attr spanCount 0x7f030035
int attr stackFromEnd 0x7f030036
int attr startDestination 0x7f030037
int attr targetPackage 0x7f030038
int attr ttcIndex 0x7f030039
int attr uri 0x7f03003a
int bool enable_system_alarm_service_default 0x7f040000
int bool enable_system_foreground_service_default 0x7f040001
int bool enable_system_job_service_default 0x7f040002
int bool workmanager_test_configuration 0x7f040003
int color androidx_core_ripple_material_light 0x7f050000
int color androidx_core_secondary_text_default_material_light 0x7f050001
int color browser_actions_bg_grey 0x7f050002
int color browser_actions_divider_color 0x7f050003
int color browser_actions_text_color 0x7f050004
int color browser_actions_title_color 0x7f050005
int color call_notification_answer_color 0x7f050006
int color call_notification_decline_color 0x7f050007
int color common_google_signin_btn_text_dark 0x7f050008
int color common_google_signin_btn_text_dark_default 0x7f050009
int color common_google_signin_btn_text_dark_disabled 0x7f05000a
int color common_google_signin_btn_text_dark_focused 0x7f05000b
int color common_google_signin_btn_text_dark_pressed 0x7f05000c
int color common_google_signin_btn_text_light 0x7f05000d
int color common_google_signin_btn_text_light_default 0x7f05000e
int color common_google_signin_btn_text_light_disabled 0x7f05000f
int color common_google_signin_btn_text_light_focused 0x7f050010
int color common_google_signin_btn_text_light_pressed 0x7f050011
int color common_google_signin_btn_tint 0x7f050012
int color notification_action_color_filter 0x7f050013
int color notification_icon_bg_color 0x7f050014
int color vector_tint_color 0x7f050015
int color vector_tint_theme_color 0x7f050016
int dimen browser_actions_context_menu_max_width 0x7f060000
int dimen browser_actions_context_menu_min_padding 0x7f060001
int dimen compat_button_inset_horizontal_material 0x7f060002
int dimen compat_button_inset_vertical_material 0x7f060003
int dimen compat_button_padding_horizontal_material 0x7f060004
int dimen compat_button_padding_vertical_material 0x7f060005
int dimen compat_control_corner_material 0x7f060006
int dimen compat_notification_large_icon_max_height 0x7f060007
int dimen compat_notification_large_icon_max_width 0x7f060008
int dimen fastscroll_default_thickness 0x7f060009
int dimen fastscroll_margin 0x7f06000a
int dimen fastscroll_minimum_range 0x7f06000b
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f06000c
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f06000d
int dimen item_touch_helper_swipe_escape_velocity 0x7f06000e
int dimen notification_action_icon_size 0x7f06000f
int dimen notification_action_text_size 0x7f060010
int dimen notification_big_circle_margin 0x7f060011
int dimen notification_content_margin_start 0x7f060012
int dimen notification_large_icon_height 0x7f060013
int dimen notification_large_icon_width 0x7f060014
int dimen notification_main_column_padding_top 0x7f060015
int dimen notification_media_narrow_margin 0x7f060016
int dimen notification_right_icon_size 0x7f060017
int dimen notification_right_side_padding_top 0x7f060018
int dimen notification_small_icon_background_padding 0x7f060019
int dimen notification_small_icon_size_as_large 0x7f06001a
int dimen notification_subtext_size 0x7f06001b
int dimen notification_top_pad 0x7f06001c
int dimen notification_top_pad_large_text 0x7f06001d
int drawable admob_close_button_black_circle_white_cross 0x7f070000
int drawable admob_close_button_white_circle_black_cross 0x7f070001
int drawable admob_close_button_white_cross 0x7f070002
int drawable common_full_open_on_phone 0x7f070003
int drawable common_google_signin_btn_icon_dark 0x7f070004
int drawable common_google_signin_btn_icon_dark_focused 0x7f070005
int drawable common_google_signin_btn_icon_dark_normal 0x7f070006
int drawable common_google_signin_btn_icon_dark_normal_background 0x7f070007
int drawable common_google_signin_btn_icon_disabled 0x7f070008
int drawable common_google_signin_btn_icon_light 0x7f070009
int drawable common_google_signin_btn_icon_light_focused 0x7f07000a
int drawable common_google_signin_btn_icon_light_normal 0x7f07000b
int drawable common_google_signin_btn_icon_light_normal_background 0x7f07000c
int drawable common_google_signin_btn_text_dark 0x7f07000d
int drawable common_google_signin_btn_text_dark_focused 0x7f07000e
int drawable common_google_signin_btn_text_dark_normal 0x7f07000f
int drawable common_google_signin_btn_text_dark_normal_background 0x7f070010
int drawable common_google_signin_btn_text_disabled 0x7f070011
int drawable common_google_signin_btn_text_light 0x7f070012
int drawable common_google_signin_btn_text_light_focused 0x7f070013
int drawable common_google_signin_btn_text_light_normal 0x7f070014
int drawable common_google_signin_btn_text_light_normal_background 0x7f070015
int drawable googleg_disabled_color_18 0x7f070016
int drawable googleg_standard_color_18 0x7f070017
int drawable ic_call_answer 0x7f070018
int drawable ic_call_answer_low 0x7f070019
int drawable ic_call_answer_video 0x7f07001a
int drawable ic_call_answer_video_low 0x7f07001b
int drawable ic_call_decline 0x7f07001c
int drawable ic_call_decline_low 0x7f07001d
int drawable ic_launcher_background 0x7f07001e
int drawable ic_launcher_foreground 0x7f07001f
int drawable ic_launcher_legacy 0x7f070020
int drawable ic_other_sign_in 0x7f070021
int drawable ic_passkey 0x7f070022
int drawable ic_password 0x7f070023
int drawable notification_action_background 0x7f070024
int drawable notification_bg 0x7f070025
int drawable notification_bg_low 0x7f070026
int drawable notification_bg_low_normal 0x7f070027
int drawable notification_bg_low_pressed 0x7f070028
int drawable notification_bg_normal 0x7f070029
int drawable notification_bg_normal_pressed 0x7f07002a
int drawable notification_icon_background 0x7f07002b
int drawable notification_oversize_large_icon_bg 0x7f07002c
int drawable notification_template_icon_bg 0x7f07002d
int drawable notification_template_icon_low_bg 0x7f07002e
int drawable notification_tile_bg 0x7f07002f
int drawable notify_panel_notification_icon_bg 0x7f070030
int drawable offline_dialog_background 0x7f070031
int drawable offline_dialog_default_icon_42dp 0x7f070032
int id accessibility_action_clickable_span 0x7f080000
int id accessibility_custom_action_0 0x7f080001
int id accessibility_custom_action_1 0x7f080002
int id accessibility_custom_action_10 0x7f080003
int id accessibility_custom_action_11 0x7f080004
int id accessibility_custom_action_12 0x7f080005
int id accessibility_custom_action_13 0x7f080006
int id accessibility_custom_action_14 0x7f080007
int id accessibility_custom_action_15 0x7f080008
int id accessibility_custom_action_16 0x7f080009
int id accessibility_custom_action_17 0x7f08000a
int id accessibility_custom_action_18 0x7f08000b
int id accessibility_custom_action_19 0x7f08000c
int id accessibility_custom_action_2 0x7f08000d
int id accessibility_custom_action_20 0x7f08000e
int id accessibility_custom_action_21 0x7f08000f
int id accessibility_custom_action_22 0x7f080010
int id accessibility_custom_action_23 0x7f080011
int id accessibility_custom_action_24 0x7f080012
int id accessibility_custom_action_25 0x7f080013
int id accessibility_custom_action_26 0x7f080014
int id accessibility_custom_action_27 0x7f080015
int id accessibility_custom_action_28 0x7f080016
int id accessibility_custom_action_29 0x7f080017
int id accessibility_custom_action_3 0x7f080018
int id accessibility_custom_action_30 0x7f080019
int id accessibility_custom_action_31 0x7f08001a
int id accessibility_custom_action_4 0x7f08001b
int id accessibility_custom_action_5 0x7f08001c
int id accessibility_custom_action_6 0x7f08001d
int id accessibility_custom_action_7 0x7f08001e
int id accessibility_custom_action_8 0x7f08001f
int id accessibility_custom_action_9 0x7f080020
int id action_container 0x7f080021
int id action_divider 0x7f080022
int id action_image 0x7f080023
int id action_text 0x7f080024
int id actions 0x7f080025
int id adjust_height 0x7f080026
int id adjust_width 0x7f080027
int id androidx_compose_ui_view_composition_context 0x7f080028
int id async 0x7f080029
int id auto 0x7f08002a
int id blocking 0x7f08002b
int id browser_actions_header_text 0x7f08002c
int id browser_actions_menu_item_icon 0x7f08002d
int id browser_actions_menu_item_text 0x7f08002e
int id browser_actions_menu_items 0x7f08002f
int id browser_actions_menu_view 0x7f080030
int id chronometer 0x7f080031
int id compose_view_saveable_id_tag 0x7f080032
int id consume_window_insets_tag 0x7f080033
int id dark 0x7f080034
int id dialog_button 0x7f080035
int id edit_text_id 0x7f080036
int id forever 0x7f080037
int id fragment_container_view_tag 0x7f080038
int id hide_ime_id 0x7f080039
int id hide_in_inspector_tag 0x7f08003a
int id icon 0x7f08003b
int id icon_group 0x7f08003c
int id icon_only 0x7f08003d
int id info 0x7f08003e
int id inspection_slot_table_set 0x7f08003f
int id is_pooling_container_tag 0x7f080040
int id italic 0x7f080041
int id item_touch_helper_previous_elevation 0x7f080042
int id layout 0x7f080043
int id light 0x7f080044
int id line1 0x7f080045
int id line3 0x7f080046
int id nav_controller_view_tag 0x7f080047
int id none 0x7f080048
int id normal 0x7f080049
int id notification_background 0x7f08004a
int id notification_main_column 0x7f08004b
int id notification_main_column_container 0x7f08004c
int id offline_dialog_advertiser_name 0x7f08004d
int id offline_dialog_image 0x7f08004e
int id offline_dialog_text 0x7f08004f
int id pooling_container_listener_holder_tag 0x7f080050
int id report_drawn 0x7f080051
int id right_icon 0x7f080052
int id right_side 0x7f080053
int id special_effects_controller_view_tag 0x7f080054
int id standard 0x7f080055
int id tag_accessibility_actions 0x7f080056
int id tag_accessibility_clickable_spans 0x7f080057
int id tag_accessibility_heading 0x7f080058
int id tag_accessibility_pane_title 0x7f080059
int id tag_on_apply_window_listener 0x7f08005a
int id tag_on_receive_content_listener 0x7f08005b
int id tag_on_receive_content_mime_types 0x7f08005c
int id tag_screen_reader_focusable 0x7f08005d
int id tag_state_description 0x7f08005e
int id tag_transition_group 0x7f08005f
int id tag_unhandled_key_event_manager 0x7f080060
int id tag_unhandled_key_listeners 0x7f080061
int id tag_window_insets_animation_callback 0x7f080062
int id text 0x7f080063
int id text2 0x7f080064
int id time 0x7f080065
int id title 0x7f080066
int id view_tree_lifecycle_owner 0x7f080067
int id view_tree_on_back_pressed_dispatcher_owner 0x7f080068
int id view_tree_saved_state_registry_owner 0x7f080069
int id view_tree_view_model_store_owner 0x7f08006a
int id visible_removing_fragment_view_tag 0x7f08006b
int id wide 0x7f08006c
int id wrapped_composition_tag 0x7f08006d
int integer google_play_services_version 0x7f090000
int integer status_bar_notification_info_maxnum 0x7f090001
int layout admob_empty_layout 0x7f0a0000
int layout browser_actions_context_menu_page 0x7f0a0001
int layout browser_actions_context_menu_row 0x7f0a0002
int layout custom_dialog 0x7f0a0003
int layout ime_base_split_test_activity 0x7f0a0004
int layout ime_secondary_split_test_activity 0x7f0a0005
int layout notification_action 0x7f0a0006
int layout notification_action_tombstone 0x7f0a0007
int layout notification_template_custom_big 0x7f0a0008
int layout notification_template_icon_group 0x7f0a0009
int layout notification_template_part_chronometer 0x7f0a000a
int layout notification_template_part_time 0x7f0a000b
int layout offline_ads_dialog 0x7f0a000c
int mipmap ic_launcher 0x7f0b0000
int mipmap ic_launcher_round 0x7f0b0001
int raw com_android_billingclient_heterodyne_info 0x7f0c0000
int raw com_android_billingclient_registration_info 0x7f0c0001
int raw firebase_common_keep 0x7f0c0002
int string about 0x7f0d0000
int string add_purchase 0x7f0d0001
int string android_credentials_TYPE_PASSWORD_CREDENTIAL 0x7f0d0002
int string androidx_credentials_TYPE_PUBLIC_KEY_CREDENTIAL 0x7f0d0003
int string androidx_startup 0x7f0d0004
int string app_name 0x7f0d0005
int string average_cost_per_mile 0x7f0d0006
int string call_notification_answer_action 0x7f0d0007
int string call_notification_answer_video_action 0x7f0d0008
int string call_notification_decline_action 0x7f0d0009
int string call_notification_hang_up_action 0x7f0d000a
int string call_notification_incoming_text 0x7f0d000b
int string call_notification_ongoing_text 0x7f0d000c
int string call_notification_screening_text 0x7f0d000d
int string cancel 0x7f0d000e
int string close_drawer 0x7f0d000f
int string close_sheet 0x7f0d0010
int string common_google_play_services_enable_button 0x7f0d0011
int string common_google_play_services_enable_text 0x7f0d0012
int string common_google_play_services_enable_title 0x7f0d0013
int string common_google_play_services_install_button 0x7f0d0014
int string common_google_play_services_install_text 0x7f0d0015
int string common_google_play_services_install_title 0x7f0d0016
int string common_google_play_services_notification_channel_name 0x7f0d0017
int string common_google_play_services_notification_ticker 0x7f0d0018
int string common_google_play_services_unknown_issue 0x7f0d0019
int string common_google_play_services_unsupported_text 0x7f0d001a
int string common_google_play_services_update_button 0x7f0d001b
int string common_google_play_services_update_text 0x7f0d001c
int string common_google_play_services_update_title 0x7f0d001d
int string common_google_play_services_updating_text 0x7f0d001e
int string common_google_play_services_wear_update_text 0x7f0d001f
int string common_open_on_phone 0x7f0d0020
int string common_signin_button_text 0x7f0d0021
int string common_signin_button_text_long 0x7f0d0022
int string copy_toast_msg 0x7f0d0023
int string currency_symbol 0x7f0d0024
int string default_error_message 0x7f0d0025
int string default_popup_window_title 0x7f0d0026
int string default_web_client_id 0x7f0d0027
int string delete_purchase 0x7f0d0028
int string dropdown_menu 0x7f0d0029
int string edit_purchase 0x7f0d002a
int string error 0x7f0d002b
int string fallback_menu_item_copy_link 0x7f0d002c
int string fallback_menu_item_open_in_browser 0x7f0d002d
int string fallback_menu_item_share_link 0x7f0d002e
int string gcm_defaultSenderId 0x7f0d002f
int string google_api_key 0x7f0d0030
int string google_app_id 0x7f0d0031
int string google_crash_reporting_api_key 0x7f0d0032
int string google_storage_bucket 0x7f0d0033
int string in_progress 0x7f0d0034
int string indeterminate 0x7f0d0035
int string loading 0x7f0d0036
int string login_benefit_backup 0x7f0d0037
int string login_benefit_premium 0x7f0d0038
int string login_benefit_sync 0x7f0d0039
int string login_benefits_title 0x7f0d003a
int string login_description 0x7f0d003b
int string login_error_cancelled 0x7f0d003c
int string login_error_generic 0x7f0d003d
int string login_error_network 0x7f0d003e
int string login_error_title 0x7f0d003f
int string m3c_bottom_sheet_collapse_description 0x7f0d0040
int string m3c_bottom_sheet_dismiss_description 0x7f0d0041
int string m3c_bottom_sheet_drag_handle_description 0x7f0d0042
int string m3c_bottom_sheet_expand_description 0x7f0d0043
int string m3c_bottom_sheet_pane_title 0x7f0d0044
int string m3c_date_input_headline 0x7f0d0045
int string m3c_date_input_headline_description 0x7f0d0046
int string m3c_date_input_invalid_for_pattern 0x7f0d0047
int string m3c_date_input_invalid_not_allowed 0x7f0d0048
int string m3c_date_input_invalid_year_range 0x7f0d0049
int string m3c_date_input_label 0x7f0d004a
int string m3c_date_input_no_input_description 0x7f0d004b
int string m3c_date_input_title 0x7f0d004c
int string m3c_date_picker_headline 0x7f0d004d
int string m3c_date_picker_headline_description 0x7f0d004e
int string m3c_date_picker_navigate_to_year_description 0x7f0d004f
int string m3c_date_picker_no_selection_description 0x7f0d0050
int string m3c_date_picker_scroll_to_earlier_years 0x7f0d0051
int string m3c_date_picker_scroll_to_later_years 0x7f0d0052
int string m3c_date_picker_switch_to_calendar_mode 0x7f0d0053
int string m3c_date_picker_switch_to_day_selection 0x7f0d0054
int string m3c_date_picker_switch_to_input_mode 0x7f0d0055
int string m3c_date_picker_switch_to_next_month 0x7f0d0056
int string m3c_date_picker_switch_to_previous_month 0x7f0d0057
int string m3c_date_picker_switch_to_year_selection 0x7f0d0058
int string m3c_date_picker_title 0x7f0d0059
int string m3c_date_picker_today_description 0x7f0d005a
int string m3c_date_picker_year_picker_pane_title 0x7f0d005b
int string m3c_date_range_input_invalid_range_input 0x7f0d005c
int string m3c_date_range_input_title 0x7f0d005d
int string m3c_date_range_picker_day_in_range 0x7f0d005e
int string m3c_date_range_picker_end_headline 0x7f0d005f
int string m3c_date_range_picker_scroll_to_next_month 0x7f0d0060
int string m3c_date_range_picker_scroll_to_previous_month 0x7f0d0061
int string m3c_date_range_picker_start_headline 0x7f0d0062
int string m3c_date_range_picker_title 0x7f0d0063
int string m3c_dialog 0x7f0d0064
int string m3c_dropdown_menu_collapsed 0x7f0d0065
int string m3c_dropdown_menu_expanded 0x7f0d0066
int string m3c_search_bar_search 0x7f0d0067
int string m3c_snackbar_dismiss 0x7f0d0068
int string m3c_suggestions_available 0x7f0d0069
int string m3c_time_picker_am 0x7f0d006a
int string m3c_time_picker_hour 0x7f0d006b
int string m3c_time_picker_hour_24h_suffix 0x7f0d006c
int string m3c_time_picker_hour_selection 0x7f0d006d
int string m3c_time_picker_hour_suffix 0x7f0d006e
int string m3c_time_picker_hour_text_field 0x7f0d006f
int string m3c_time_picker_minute 0x7f0d0070
int string m3c_time_picker_minute_selection 0x7f0d0071
int string m3c_time_picker_minute_suffix 0x7f0d0072
int string m3c_time_picker_minute_text_field 0x7f0d0073
int string m3c_time_picker_period_toggle_description 0x7f0d0074
int string m3c_time_picker_pm 0x7f0d0075
int string m3c_tooltip_long_press_label 0x7f0d0076
int string m3c_tooltip_pane_description 0x7f0d0077
int string native_body 0x7f0d0078
int string native_headline 0x7f0d0079
int string native_media_view 0x7f0d007a
int string nav_home 0x7f0d007b
int string nav_programs 0x7f0d007c
int string nav_purchases 0x7f0d007d
int string nav_settings 0x7f0d007e
int string navigation_menu 0x7f0d007f
int string not_selected 0x7f0d0080
int string notifications_permission_confirm 0x7f0d0081
int string notifications_permission_decline 0x7f0d0082
int string notifications_permission_title 0x7f0d0083
int string off 0x7f0d0084
int string offline_dialog_image_description 0x7f0d0085
int string offline_dialog_text 0x7f0d0086
int string offline_notification_title 0x7f0d0087
int string offline_notification_title_with_advertiser 0x7f0d0088
int string offline_opt_in_confirm 0x7f0d0089
int string offline_opt_in_decline 0x7f0d008a
int string offline_opt_in_message 0x7f0d008b
int string offline_opt_in_title 0x7f0d008c
int string ok 0x7f0d008d
int string on 0x7f0d008e
int string premium_active 0x7f0d008f
int string program_details 0x7f0d0090
int string programs_title 0x7f0d0091
int string project_id 0x7f0d0092
int string purchase_cost 0x7f0d0093
int string purchase_date 0x7f0d0094
int string purchase_miles 0x7f0d0095
int string purchase_program 0x7f0d0096
int string purchases_title 0x7f0d0097
int string range_end 0x7f0d0098
int string range_start 0x7f0d0099
int string remove_ads 0x7f0d009a
int string retry 0x7f0d009b
int string s1 0x7f0d009c
int string s2 0x7f0d009d
int string s3 0x7f0d009e
int string s4 0x7f0d009f
int string s5 0x7f0d00a0
int string s6 0x7f0d00a1
int string s7 0x7f0d00a2
int string save 0x7f0d00a3
int string selected 0x7f0d00a4
int string settings_title 0x7f0d00a5
int string sign_in_with_google 0x7f0d00a6
int string sign_out 0x7f0d00a7
int string status_bar_notification_info_overflow 0x7f0d00a8
int string switch_role 0x7f0d00a9
int string tab 0x7f0d00aa
int string template_percent 0x7f0d00ab
int string tooltip_description 0x7f0d00ac
int string tooltip_label 0x7f0d00ad
int string total_miles 0x7f0d00ae
int string total_spent 0x7f0d00af
int string watermark_label_prefix 0x7f0d00b0
int string welcome 0x7f0d00b1
int string welcome_subtitle 0x7f0d00b2
int style DialogWindowTheme 0x7f0e0000
int style FloatingDialogTheme 0x7f0e0001
int style FloatingDialogWindowTheme 0x7f0e0002
int style TextAppearance_Compat_Notification 0x7f0e0003
int style TextAppearance_Compat_Notification_Info 0x7f0e0004
int style TextAppearance_Compat_Notification_Line2 0x7f0e0005
int style TextAppearance_Compat_Notification_Time 0x7f0e0006
int style TextAppearance_Compat_Notification_Title 0x7f0e0007
int style Theme_Hidden 0x7f0e0008
int style Theme_IAPTheme 0x7f0e0009
int style Theme_MileageTracker 0x7f0e000a
int style Theme_PlayCore_Transparent 0x7f0e000b
int style Widget_Compat_NotificationActionContainer 0x7f0e000c
int style Widget_Compat_NotificationActionText 0x7f0e000d
int[] styleable ActivityNavigator { 0x01010003, 0x7f030000, 0x7f030009, 0x7f03000a, 0x7f030038 }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable AdsAttrs { 0x7f030001, 0x7f030002, 0x7f030003 }
int styleable AdsAttrs_adSize 0
int styleable AdsAttrs_adSizes 1
int styleable AdsAttrs_adUnitId 2
int[] styleable Capability { 0x7f03002e, 0x7f030034 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f030004, 0x7f030022 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable FontFamily { 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFallbackQuery 2
int styleable FontFamily_fontProviderFetchStrategy 3
int styleable FontFamily_fontProviderFetchTimeout 4
int styleable FontFamily_fontProviderPackage 5
int styleable FontFamily_fontProviderQuery 6
int styleable FontFamily_fontProviderSystemFontFamily 7
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f030013, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f030039 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LoadingImageView { 0x7f030007, 0x7f030020, 0x7f030021 }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable NavAction { 0x010100d0, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f030023, 0x7f030029, 0x7f03002a, 0x7f03002b, 0x7f03002c, 0x7f03002d, 0x7f030030 }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f030005, 0x7f030028 }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f030000, 0x7f030025, 0x7f03003a }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f030037 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f030026 }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f03001f }
int styleable NavInclude_graph 0
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f030032 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f03000e, 0x7f03000f, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030024, 0x7f030031, 0x7f030035, 0x7f030036 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable SignInButton { 0x7f030006, 0x7f030008, 0x7f030033 }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int xml backup_rules 0x7f100000
int xml com_android_billingclient_phenotype 0x7f100001
int xml data_extraction_rules 0x7f100002
int xml ga_ad_services_config 0x7f100003
int xml gma_ad_services_config 0x7f100004
int xml image_share_filepaths 0x7f100005
