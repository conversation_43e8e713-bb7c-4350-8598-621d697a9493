package com.sirch.mileagetracker.presentation.purchase;

import androidx.lifecycle.ViewModel;
import com.sirch.mileagetracker.data.local.entities.Program;
import com.sirch.mileagetracker.data.local.entities.Purchase;
import com.sirch.mileagetracker.data.repository.ProgramRepository;
import com.sirch.mileagetracker.data.repository.PurchaseRepository;
import dagger.hilt.android.lifecycle.HiltViewModel;
import kotlinx.coroutines.flow.StateFlow;
import kotlinx.datetime.Clock;
import kotlinx.datetime.LocalDate;
import kotlinx.datetime.TimeZone;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u0011\u001a\u00020\u0012J\u0006\u0010\u0013\u001a\u00020\u0012J\u000e\u0010\u0014\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u000bJ\u0006\u0010\u0016\u001a\u00020\u0012J\u000e\u0010\u0017\u001a\u00020\u00122\u0006\u0010\u0018\u001a\u00020\u000bJ\u0006\u0010\u0019\u001a\u00020\u0012J\u000e\u0010\u001a\u001a\u00020\u00122\u0006\u0010\u001b\u001a\u00020\u001cJ\u000e\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u001fJ\u0006\u0010 \u001a\u00020\u0012J\u000e\u0010!\u001a\u00020\u00122\u0006\u0010\"\u001a\u00020#J\u000e\u0010$\u001a\u00020\u00122\u0006\u0010%\u001a\u00020#J\u000e\u0010&\u001a\u00020\u00122\u0006\u0010\'\u001a\u00020#J\b\u0010(\u001a\u00020)H\u0002R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0012\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006*"}, d2 = {"Lcom/sirch/mileagetracker/presentation/purchase/PurchaseFormViewModel;", "Landroidx/lifecycle/ViewModel;", "purchaseRepository", "Lcom/sirch/mileagetracker/data/repository/PurchaseRepository;", "programRepository", "Lcom/sirch/mileagetracker/data/repository/ProgramRepository;", "(Lcom/sirch/mileagetracker/data/repository/PurchaseRepository;Lcom/sirch/mileagetracker/data/repository/ProgramRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/sirch/mileagetracker/presentation/purchase/PurchaseFormUiState;", "currentPurchaseId", "", "Ljava/lang/Long;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "deletePurchase", "", "hideDeleteConfirmation", "initializeForProgram", "programId", "loadPrograms", "loadPurchase", "purchaseId", "savePurchase", "selectDate", "date", "Lkotlinx/datetime/LocalDate;", "selectProgram", "program", "Lcom/sirch/mileagetracker/data/local/entities/Program;", "showDeleteConfirmation", "updateCost", "cost", "", "updateDescription", "description", "updateMiles", "miles", "validateForm", "", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class PurchaseFormViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.PurchaseRepository purchaseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.ProgramRepository programRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState> uiState = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Long currentPurchaseId;
    
    @javax.inject.Inject()
    public PurchaseFormViewModel(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.PurchaseRepository purchaseRepository, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.ProgramRepository programRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState> getUiState() {
        return null;
    }
    
    public final void loadPrograms() {
    }
    
    public final void initializeForProgram(long programId) {
    }
    
    public final void loadPurchase(long purchaseId) {
    }
    
    public final void selectProgram(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Program program) {
    }
    
    public final void selectDate(@org.jetbrains.annotations.NotNull()
    kotlinx.datetime.LocalDate date) {
    }
    
    public final void updateMiles(@org.jetbrains.annotations.NotNull()
    java.lang.String miles) {
    }
    
    public final void updateCost(@org.jetbrains.annotations.NotNull()
    java.lang.String cost) {
    }
    
    public final void updateDescription(@org.jetbrains.annotations.NotNull()
    java.lang.String description) {
    }
    
    public final void savePurchase() {
    }
    
    public final void showDeleteConfirmation() {
    }
    
    public final void hideDeleteConfirmation() {
    }
    
    public final void deletePurchase() {
    }
    
    private final boolean validateForm() {
        return false;
    }
}