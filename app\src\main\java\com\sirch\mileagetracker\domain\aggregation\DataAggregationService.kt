package com.sirch.mileagetracker.domain.aggregation

import com.sirch.mileagetracker.data.local.dao.AggregatedDataDao
import com.sirch.mileagetracker.data.local.dao.EfficiencyScoreDao
import com.sirch.mileagetracker.data.local.entities.AggregatedData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.sqrt

/**
 * Service for aggregating anonymized efficiency data across all users.
 * Ensures LGPD/GDPR compliance by completely anonymizing data before aggregation.
 * 
 * Privacy measures:
 * - No individual user data stored in aggregates
 * - Minimum anonymity set of 10 users per program
 * - Outliers removed to prevent identification
 * - Data hashed and anonymized before processing
 */
@Singleton
class DataAggregationService @Inject constructor(
    private val aggregatedDataDao: AggregatedDataDao,
    private val efficiencyScoreDao: EfficiencyScoreDao
) {

    companion object {
        private const val MIN_USERS_FOR_AGGREGATION = 10
        private const val OUTLIER_THRESHOLD_MULTIPLIER = 2.0 // Remove values > 2 standard deviations
        private const val MAX_DATA_AGE_HOURS = 24 // Refresh aggregate data daily
        private const val DEFAULT_REGION = "BR" // Brazil default
    }

    /**
     * Get aggregate data for a specific program
     * Returns cached data if recent, otherwise triggers recalculation
     */
    suspend fun getAggregateData(programId: Long, region: String = DEFAULT_REGION): AggregatedData? {
        return withContext(Dispatchers.IO) {
            try {
                // Check for existing recent data
                val existingData = aggregatedDataDao.getAggregatedDataByProgramAndRegion(programId, region)
                
                if (existingData?.isDataValid() == true) {
                    return@withContext existingData
                }
                
                // Calculate new aggregate data
                calculateAggregateData(programId, region)
                
            } catch (e: Exception) {
                println("Error getting aggregate data: ${e.message}")
                null
            }
        }
    }

    /**
     * Update aggregate data for a specific program (called after new purchases)
     */
    suspend fun updateAggregateDataForProgram(programId: Long, region: String = DEFAULT_REGION) {
        withContext(Dispatchers.IO) {
            try {
                calculateAggregateData(programId, region)
            } catch (e: Exception) {
                println("Error updating aggregate data: ${e.message}")
            }
        }
    }

    /**
     * Calculate aggregate statistics for a program with privacy protection
     */
    private suspend fun calculateAggregateData(programId: Long, region: String): AggregatedData? {
        try {
            // Get all efficiency scores for this program (anonymized data)
            val allScores = efficiencyScoreDao.getAllScoresForProgram(programId)
            
            // Check minimum anonymity set requirement
            if (allScores.size < MIN_USERS_FOR_AGGREGATION) {
                println("Insufficient data for aggregation: ${allScores.size} < $MIN_USERS_FOR_AGGREGATION users")
                return null
            }
            
            // Extract cost per mile values for analysis
            val costPerMileValues = allScores
                .filter { it.isDataSharingEnabled } // Only include users who consented
                .map { it.personalAvgCostPerMile }
                .filter { it > 0 } // Remove invalid values
            
            if (costPerMileValues.size < MIN_USERS_FOR_AGGREGATION) {
                println("Insufficient consented data for aggregation")
                return null
            }
            
            // Remove outliers to prevent identification and improve data quality
            val cleanedValues = removeOutliers(costPerMileValues)
            
            if (cleanedValues.size < MIN_USERS_FOR_AGGREGATION) {
                println("Insufficient data after outlier removal")
                return null
            }
            
            // Calculate anonymized statistics
            val anonymizedStats = calculateAnonymizedStatistics(cleanedValues)
            
            // Create aggregate data record
            val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
            val aggregatedData = AggregatedData(
                programId = programId,
                anonymizedAvgCostPerMile = anonymizedStats.average,
                sampleSize = cleanedValues.size,
                lastUpdated = now,
                region = region,
                dataVersion = 1,
                confidenceLevel = calculateConfidenceLevel(cleanedValues.size),
                minCostPerMile = anonymizedStats.min,
                maxCostPerMile = anonymizedStats.max,
                standardDeviation = anonymizedStats.standardDeviation
            )
            
            // Save to database
            aggregatedDataDao.insertAggregatedData(aggregatedData)
            
            return aggregatedData
            
        } catch (e: Exception) {
            println("Error calculating aggregate data: ${e.message}")
            return null
        }
    }

    /**
     * Remove statistical outliers to prevent identification and improve data quality
     */
    private fun removeOutliers(values: List<Double>): List<Double> {
        if (values.size < 3) return values
        
        val mean = values.average()
        val variance = values.map { (it - mean) * (it - mean) }.average()
        val standardDeviation = sqrt(variance)
        
        val threshold = standardDeviation * OUTLIER_THRESHOLD_MULTIPLIER
        
        return values.filter { value ->
            val deviation = kotlin.math.abs(value - mean)
            deviation <= threshold
        }
    }

    /**
     * Calculate anonymized statistics ensuring no individual identification
     */
    private fun calculateAnonymizedStatistics(values: List<Double>): AnonymizedStatistics {
        val sortedValues = values.sorted()
        val average = values.average()
        val min = sortedValues.first()
        val max = sortedValues.last()
        
        // Calculate standard deviation
        val variance = values.map { (it - average) * (it - average) }.average()
        val standardDeviation = sqrt(variance)
        
        return AnonymizedStatistics(
            average = average,
            min = min,
            max = max,
            standardDeviation = standardDeviation
        )
    }

    /**
     * Calculate confidence level based on sample size
     */
    private fun calculateConfidenceLevel(sampleSize: Int): Double {
        return when {
            sampleSize >= 100 -> 95.0
            sampleSize >= 50 -> 90.0
            sampleSize >= 30 -> 85.0
            sampleSize >= 20 -> 80.0
            sampleSize >= 10 -> 75.0
            else -> 70.0
        }
    }

    /**
     * Clean up old aggregate data to maintain performance
     */
    suspend fun cleanupOldAggregateData() {
        withContext(Dispatchers.IO) {
            try {
                val cutoffTime = Clock.System.now()
                    .minus(kotlin.time.Duration.parse("${MAX_DATA_AGE_HOURS * 7}h")) // Keep 7 days of history
                    .toLocalDateTime(TimeZone.currentSystemDefault())
                
                val deletedCount = aggregatedDataDao.deleteExpiredAggregatedData(cutoffTime)
                println("Cleaned up $deletedCount old aggregate data records")
                
            } catch (e: Exception) {
                println("Error cleaning up aggregate data: ${e.message}")
            }
        }
    }

    /**
     * Get aggregate data statistics for monitoring
     */
    suspend fun getAggregateDataStatistics(): AggregateDataStatistics? {
        return try {
            val stats = aggregatedDataDao.getAggregatedDataStatistics()
            val programsWithData = aggregatedDataDao.getProgramsWithValidData(
                cutoffTime = Clock.System.now()
                    .minus(kotlin.time.Duration.parse("${MAX_DATA_AGE_HOURS}h"))
                    .toLocalDateTime(TimeZone.currentSystemDefault())
            )
            
            AggregateDataStatistics(
                totalPrograms = programsWithData.size,
                totalRecords = stats?.totalRecords ?: 0,
                averageSampleSize = stats?.avgSampleSize ?: 0.0,
                oldestUpdate = stats?.oldestUpdate,
                newestUpdate = stats?.newestUpdate
            )
            
        } catch (e: Exception) {
            println("Error getting aggregate statistics: ${e.message}")
            null
        }
    }

    /**
     * Check if aggregate data is available for a program
     */
    suspend fun hasAggregateData(programId: Long, region: String = DEFAULT_REGION): Boolean {
        return try {
            val data = aggregatedDataDao.getAggregatedDataByProgramAndRegion(programId, region)
            data?.isDataValid() == true && data.sampleSize >= MIN_USERS_FOR_AGGREGATION
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Force refresh of all aggregate data (for background sync)
     */
    suspend fun refreshAllAggregateData() {
        withContext(Dispatchers.IO) {
            try {
                // Get all programs that have efficiency scores
                val programIds = efficiencyScoreDao.getProgramsWithScores()
                
                programIds.forEach { programId ->
                    updateAggregateDataForProgram(programId)
                }
                
                // Cleanup old data
                cleanupOldAggregateData()
                
            } catch (e: Exception) {
                println("Error refreshing aggregate data: ${e.message}")
            }
        }
    }
}

/**
 * Anonymized statistics for a dataset
 */
private data class AnonymizedStatistics(
    val average: Double,
    val min: Double,
    val max: Double,
    val standardDeviation: Double
)

/**
 * Statistics about aggregate data for monitoring
 */
data class AggregateDataStatistics(
    val totalPrograms: Int,
    val totalRecords: Int,
    val averageSampleSize: Double,
    val oldestUpdate: kotlinx.datetime.LocalDateTime?,
    val newestUpdate: kotlinx.datetime.LocalDateTime?
)
