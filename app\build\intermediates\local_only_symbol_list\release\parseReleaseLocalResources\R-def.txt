R_DEF: Internal format may change without notice
local
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_launcher_legacy
mipmap ic_launcher
mipmap ic_launcher_round
string about
string add_purchase
string app_name
string average_cost_per_mile
string cancel
string currency_symbol
string default_web_client_id
string delete_purchase
string edit_purchase
string error
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string loading
string login_benefit_backup
string login_benefit_premium
string login_benefit_sync
string login_benefits_title
string login_description
string login_error_cancelled
string login_error_generic
string login_error_network
string login_error_title
string nav_home
string nav_programs
string nav_purchases
string nav_settings
string ok
string premium_active
string program_details
string programs_title
string project_id
string purchase_cost
string purchase_date
string purchase_miles
string purchase_program
string purchases_title
string remove_ads
string retry
string save
string settings_title
string sign_in_with_google
string sign_out
string total_miles
string total_spent
string welcome
string welcome_subtitle
style Theme.MileageTracker
xml backup_rules
xml data_extraction_rules
xml gma_ad_services_config
