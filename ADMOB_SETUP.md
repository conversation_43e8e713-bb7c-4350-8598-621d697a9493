# 📱 Configuração do AdMob - Mileage Tracker

## 🎯 **Configuração Atual:**

- **AdMob App ID:** `ca-app-pub-8316648275077982/2235862620`
- **Play Services Ads:** `23.5.0` (versão estável)
- **Conflito de Manifest:** ✅ Resolvido

## 🔧 **Problema Resolvido:**

### **Erro Original:**
```
Manifest merger failed : Attribute property#android.adservices.AD_SERVICES_CONFIG@resource
```

### **Causa:**
Conflito entre duas bibliotecas do Google:
- `play-services-measurement-api` (Firebase Analytics)
- `play-services-ads-lite` (AdMob)

### **Solução Aplicada:**

#### 1. **AndroidManifest.xml:**
```xml
<!-- Fix for AdMob manifest merger conflict -->
<property
    android:name="android.adservices.AD_SERVICES_CONFIG"
    android:resource="@xml/gma_ad_services_config"
    tools:replace="android:resource" />
```

#### 2. **Arquivo de Configuração:**
- Criado: `app/src/main/res/xml/gma_ad_services_config.xml`
- Configura os serviços de anúncios do AdMob

#### 3. **Exclusão de Dependência:**
```kotlin
// build.gradle.kts
implementation(libs.play.services.ads) {
    exclude(group = "com.google.android.gms", module = "play-services-measurement-api")
}
```

## 📋 **Configuração Completa do AdMob:**

### **1. AndroidManifest.xml:**
```xml
<!-- AdMob App ID -->
<meta-data
    android:name="com.google.android.gms.ads.APPLICATION_ID"
    android:value="ca-app-pub-8316648275077982/2235862620" />

<!-- Fix for manifest conflict -->
<property
    android:name="android.adservices.AD_SERVICES_CONFIG"
    android:resource="@xml/gma_ad_services_config"
    tools:replace="android:resource" />
```

### **2. Dependências:**
```kotlin
// build.gradle.kts
implementation("com.google.android.gms:play-services-ads:23.5.0") {
    exclude(group = "com.google.android.gms", module = "play-services-measurement-api")
}
```

### **3. Arquivo de Configuração:**
```xml
<!-- res/xml/gma_ad_services_config.xml -->
<?xml version="1.0" encoding="utf-8"?>
<ad-services-config>
    <include-data-collection-api value="true" />
</ad-services-config>
```

## 🚀 **Próximos Passos para AdMob:**

### **1. Implementar Anúncios Banner:**
```kotlin
// HomeScreen.kt
@Composable
fun AdBanner() {
    AndroidView(
        factory = { context ->
            AdView(context).apply {
                setAdSize(AdSize.BANNER)
                adUnitId = "ca-app-pub-3940256099942544/6300978111" // Test ID
                loadAd(AdRequest.Builder().build())
            }
        }
    )
}
```

### **2. Implementar Anúncios Intersticiais:**
```kotlin
// AdMob Manager
class AdMobManager @Inject constructor(
    private val context: Context
) {
    private var interstitialAd: InterstitialAd? = null
    
    fun loadInterstitialAd() {
        val adRequest = AdRequest.Builder().build()
        InterstitialAd.load(
            context,
            "ca-app-pub-3940256099942544/1033173712", // Test ID
            adRequest,
            object : InterstitialAdLoadCallback() {
                override fun onAdLoaded(ad: InterstitialAd) {
                    interstitialAd = ad
                }
            }
        )
    }
}
```

### **3. IDs de Anúncios de Teste:**
```
Banner: ca-app-pub-3940256099942544/6300978111
Intersticial: ca-app-pub-3940256099942544/1033173712
```

### **4. IDs de Produção:**
- Criar no AdMob Console: https://apps.admob.com/
- Substituir IDs de teste pelos reais antes do release

## ⚠️ **Considerações Importantes:**

1. **Teste sempre com IDs de teste** durante desenvolvimento
2. **Configure Privacy & Messaging** no AdMob Console
3. **Implemente GDPR/CCPA compliance** se necessário
4. **Teste em dispositivos reais** para verificar anúncios
5. **Monitore métricas** no AdMob Console

## 🔍 **Verificação:**

1. **Build do projeto:** Deve compilar sem erros de manifest
2. **Inicialização do AdMob:** Verificar logs no Logcat
3. **Carregamento de anúncios:** Testar com IDs de teste

**O AdMob está configurado e pronto para uso!** ✅
