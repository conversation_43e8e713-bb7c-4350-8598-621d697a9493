# 🛠️ Configuração do Android Studio - Mileage Tracker

## 📋 Requisitos do Sistema

### ☕ **JDK Recomendado:**
- **JDK 17** (LTS) - **RECOMENDADO**
- **JDK 21** (LTS) - Também compatível
- ❌ **Evitar JDK 8, 11** - Vers<PERSON>es muito antigas
- ❌ **Evitar JDK 22+** - Podem ter problemas de compatibilidade

### 🔧 **Versões do Projeto:**
```
Gradle: 8.9
Android Gradle Plugin (AGP): 8.6.0
Kotlin: 2.0.20
Compose BOM: 2024.09.02
Hilt: 2.48
Target SDK: 35 (Android 15)
Min SDK: 33 (Android 13 Tiramisu)
```

## 🚀 **Passos para Configurar:**

### 1. **Verificar/Instalar JDK 17:**

**Windows:**
```bash
# Verificar JDK atual
java -version
javac -version

# Se não tiver JDK 17, baixar de:
# https://adoptium.net/temurin/releases/?version=17
```

**macOS:**
```bash
# Verificar JDK atual
java -version

# Instalar via Homebrew
brew install openjdk@17
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-17-jdk

# Verificar instalação
java -version
```

### 2. **Configurar Android Studio:**

1. **File → Settings** (ou **Android Studio → Preferences** no macOS)
2. **Build, Execution, Deployment → Build Tools → Gradle**
3. **Gradle JVM:** Selecionar **JDK 17**
4. **Apply → OK**

### 3. **Configurar Variáveis de Ambiente:**

**Windows:**
```
JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-17.x.x.x-hotspot
```

**macOS/Linux:**
```bash
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk
# Adicionar ao ~/.bashrc ou ~/.zshrc
```

### 4. **Sincronizar Projeto:**

1. Abrir o projeto no Android Studio
2. **File → Sync Project with Gradle Files**
3. Aguardar download das dependências
4. Verificar se não há erros na aba **Build**

## 🔍 **Verificação de Compatibilidade:**

### ✅ **Checklist:**
- [ ] JDK 17 instalado e configurado
- [ ] Android Studio atualizado (versão mais recente)
- [ ] Gradle 8.7 configurado
- [ ] Projeto sincronizado sem erros
- [ ] Build bem-sucedido

### 🐛 **Problemas Comuns:**

**Erro: "Unsupported Java version"**
```
Solução: Configurar JDK 17 no Android Studio
File → Settings → Build Tools → Gradle → Gradle JVM
```

**Erro: "Gradle sync failed"**
```
Solução:
1. File → Invalidate Caches and Restart
2. Verificar conexão com internet
3. Limpar cache: ./gradlew clean
```

**Erro: "Kotlin compiler version"**
```
Solução: Projeto já configurado com Kotlin 2.1.0
Apenas sincronizar o projeto
```

## 📱 **Testar Configuração:**

1. **Build do projeto:**
   ```bash
   ./gradlew assembleDebug
   ```

2. **Executar no emulador:**
   - Criar AVD com Android 13+ (API 33+)
   - Executar o app
   - Verificar se compila e executa

3. **Verificar funcionalidades:**
   - Tela de splash carrega
   - Login com Google funciona
   - Banco de dados inicializa

## 🎯 **Configuração Ideal:**

```
Android Studio: Última versão estável
JDK: 17 (LTS)
Gradle: 8.9
AGP: 8.6.0
Kotlin: 2.0.20
Compose BOM: 2024.09.02
Hilt: 2.48
Min SDK: 33 (Android 13)
Target SDK: 35 (Android 15)
```

## 📞 **Suporte:**

Se ainda houver problemas:
1. Verificar logs do Android Studio
2. Limpar cache: **File → Invalidate Caches and Restart**
3. Reimportar projeto: **File → New → Import Project**

**O projeto está configurado para funcionar perfeitamente com essas especificações!** ✅
