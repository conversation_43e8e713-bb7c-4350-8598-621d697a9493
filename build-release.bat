@echo off
echo ========================================
echo    Mileage Tracker - Build Release
echo ========================================
echo.

echo [1/5] Limpando projeto...
call gradlew clean
if %errorlevel% neq 0 (
    echo ERRO: Falha na limpeza do projeto
    pause
    exit /b 1
)

echo.
echo [2/5] Verificando configuracoes...
if not exist "keystore.properties" (
    echo AVISO: keystore.properties nao encontrado
    echo Copie keystore.properties.example e configure suas credenciais
    pause
)

echo.
echo [3/5] Compilando versao de release...
call gradlew assembleRelease
if %errorlevel% neq 0 (
    echo ERRO: Falha na compilacao
    pause
    exit /b 1
)

echo.
echo [4/5] Gerando Android App Bundle (AAB)...
call gradlew bundleRelease
if %errorlevel% neq 0 (
    echo ERRO: Falha na geracao do AAB
    pause
    exit /b 1
)

echo.
echo [5/5] Build concluido com sucesso!
echo.
echo Arquivos gerados:
echo - APK: app\build\outputs\apk\release\app-release.apk
echo - AAB: app\build\outputs\bundle\release\app-release.aab
echo.
echo Recomendacao: Use o arquivo AAB para upload na Play Store
echo.
pause
