package com.sirch.mileagetracker.presentation.theme

import com.sirch.mileagetracker.data.repository.AdsRepository
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ThemeManager @Inject constructor(
    private val adsRepository: AdsRepository
) {

    /**
     * Get theme state flow - now only tracks premium status
     * Dark mode is handled automatically by the system theme
     */
    fun getThemeFlow() = adsRepository.areAdsRemoved().map { isPremium ->
        ThemeState(
            isPremium = isPremium
        )
    }
}

data class ThemeState(
    val isPremium: Boolean = false
)
