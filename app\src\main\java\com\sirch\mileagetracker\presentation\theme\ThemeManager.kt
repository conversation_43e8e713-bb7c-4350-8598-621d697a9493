package com.sirch.mileagetracker.presentation.theme

import com.sirch.mileagetracker.data.repository.AdsRepository
import com.sirch.mileagetracker.data.repository.SettingsRepository
import kotlinx.coroutines.flow.combine
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ThemeManager @Inject constructor(
    private val settingsRepository: SettingsRepository,
    private val adsRepository: AdsRepository
) {

    fun getThemeFlow() = combine(
        settingsRepository.getSettings(),
        adsRepository.areAdsRemoved()
    ) { settings, isPremium ->
        ThemeState(
            isDarkMode = settings?.darkModeEnabled == true && isPremium,
            isPremium = isPremium
        )
    }

    suspend fun toggleDarkMode() {
        val currentSettings = settingsRepository.getCurrentSettings()
        val isPremium = !adsRepository.getCurrentAdsStatus()

        if (isPremium) {
            settingsRepository.updateDarkMode(!currentSettings.darkModeEnabled)
        }
    }

    suspend fun setDarkMode(enabled: Boolean) {
        val isPremium = !adsRepository.getCurrentAdsStatus()

        if (isPremium) {
            settingsRepository.updateDarkMode(enabled)
        }
    }
}

data class ThemeState(
    val isDarkMode: Boolean = false,
    val isPremium: Boolean = false
)
