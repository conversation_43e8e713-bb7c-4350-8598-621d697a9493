{"logs": [{"outputFile": "com.sirch.mileagetracker.app-mergeReleaseResources-3:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\e995885063661f84453093b90e093640\\transformed\\browser-1.8.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "13,17,18,19", "startColumns": "4,4,4,4", "startOffsets": "1224,1602,1699,1808", "endColumns": "97,96,108,98", "endOffsets": "1317,1694,1803,1902"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\17af91994e13f583c0b6091f8c6fb16f\\transformed\\core-1.15.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "4,5,6,7,8,9,10,85", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "331,427,529,628,727,831,934,8622", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "422,524,623,722,826,929,1045,8718"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\831f7f0f93fd70afcef46ca0120dc927\\transformed\\foundation-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "89,90", "startColumns": "4,4", "startOffsets": "8987,9073", "endColumns": "85,84", "endOffsets": "9068,9153"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\ce7063c7507d7ba5446267220fbed412\\transformed\\credentials-1.2.0-rc01\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,217", "endColumns": "111,113", "endOffsets": "212,326"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\fcd13fdc941282eaa2071fb31e6df1d3\\transformed\\ui-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1337,1403,1524"}, "to": {"startLines": "11,12,14,15,16,20,21,78,79,80,81,82,83,84,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1050,1142,1322,1416,1515,1907,1989,8087,8176,8260,8325,8389,8467,8549,8723,8800,8866", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "1137,1219,1411,1510,1597,1984,2073,8171,8255,8320,8384,8462,8544,8617,8795,8861,8982"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\4f87b4fe7e5f13faf091c8d26d70e189\\transformed\\material3-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2466,2587,2703,2826,2951,3043,3141,3258,3382,3479,3581,3683,3813,3952,4058,4157,4235,4331,4425,4512,4599,4701,4783,4867,4968,5069,5169,5268,5356,5462,5563,5667,5787,5869,5969", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2461,2582,2698,2821,2946,3038,3136,3253,3377,3474,3576,3678,3808,3947,4053,4152,4230,4326,4420,4507,4594,4696,4778,4862,4963,5064,5164,5263,5351,5457,5558,5662,5782,5864,5964,6059"}, "to": {"startLines": "22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2078,2196,2312,2423,2537,2636,2731,2843,2979,3095,3231,3315,3414,3505,3602,3721,3846,3950,4077,4200,4328,4489,4610,4726,4849,4974,5066,5164,5281,5405,5502,5604,5706,5836,5975,6081,6180,6258,6354,6448,6535,6622,6724,6806,6890,6991,7092,7192,7291,7379,7485,7586,7690,7810,7892,7992", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "2191,2307,2418,2532,2631,2726,2838,2974,3090,3226,3310,3409,3500,3597,3716,3841,3945,4072,4195,4323,4484,4605,4721,4844,4969,5061,5159,5276,5400,5497,5599,5701,5831,5970,6076,6175,6253,6349,6443,6530,6617,6719,6801,6885,6986,7087,7187,7286,7374,7480,7581,7685,7805,7887,7987,8082"}}]}]}