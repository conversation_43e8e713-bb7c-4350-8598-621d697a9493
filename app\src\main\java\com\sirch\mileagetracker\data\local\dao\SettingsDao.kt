package com.sirch.mileagetracker.data.local.dao

import androidx.room.*
import com.sirch.mileagetracker.data.local.entities.Settings
import kotlinx.coroutines.flow.Flow

@Dao
interface SettingsDao {
    
    @Query("SELECT * FROM settings WHERE id = 1")
    fun getSettings(): Flow<Settings?>
    
    @Query("SELECT * FROM settings WHERE id = 1")
    suspend fun getSettingsOnce(): Settings?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSettings(settings: Settings)
    
    @Update
    suspend fun updateSettings(settings: Settings)
    
    @Query("UPDATE settings SET adsRemoved = :adsRemoved WHERE id = 1")
    suspend fun updateAdsRemoved(adsRemoved: Boolean)
    
    @Query("UPDATE settings SET userId = :userId WHERE id = 1")
    suspend fun updateUserId(userId: String?)
    
    @Query("UPDATE settings SET lastSyncTime = :timestamp WHERE id = 1")
    suspend fun updateLastSyncTime(timestamp: Long)
}
