package com.sirch.mileagetracker.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.paging.PagingSource;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.paging.LimitOffsetPagingSource;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.sirch.mileagetracker.data.local.converters.DateConverter;
import com.sirch.mileagetracker.data.local.entities.Purchase;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDate;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class PurchaseDao_Impl implements PurchaseDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Purchase> __insertionAdapterOfPurchase;

  private final DateConverter __dateConverter = new DateConverter();

  private final EntityDeletionOrUpdateAdapter<Purchase> __deletionAdapterOfPurchase;

  private final EntityDeletionOrUpdateAdapter<Purchase> __updateAdapterOfPurchase;

  private final SharedSQLiteStatement __preparedStmtOfDeletePurchasesByProgram;

  public PurchaseDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfPurchase = new EntityInsertionAdapter<Purchase>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `purchases` (`id`,`programId`,`date`,`miles`,`cost`,`description`) VALUES (nullif(?, 0),?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Purchase entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getProgramId());
        final String _tmp = __dateConverter.fromLocalDate(entity.getDate());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, _tmp);
        }
        statement.bindLong(4, entity.getMiles());
        statement.bindDouble(5, entity.getCost());
        if (entity.getDescription() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getDescription());
        }
      }
    };
    this.__deletionAdapterOfPurchase = new EntityDeletionOrUpdateAdapter<Purchase>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `purchases` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Purchase entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfPurchase = new EntityDeletionOrUpdateAdapter<Purchase>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `purchases` SET `id` = ?,`programId` = ?,`date` = ?,`miles` = ?,`cost` = ?,`description` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Purchase entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getProgramId());
        final String _tmp = __dateConverter.fromLocalDate(entity.getDate());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, _tmp);
        }
        statement.bindLong(4, entity.getMiles());
        statement.bindDouble(5, entity.getCost());
        if (entity.getDescription() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getDescription());
        }
        statement.bindLong(7, entity.getId());
      }
    };
    this.__preparedStmtOfDeletePurchasesByProgram = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM purchases WHERE programId = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertPurchase(final Purchase purchase,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfPurchase.insertAndReturnId(purchase);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deletePurchase(final Purchase purchase,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfPurchase.handle(purchase);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updatePurchase(final Purchase purchase,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfPurchase.handle(purchase);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deletePurchasesByProgram(final long programId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeletePurchasesByProgram.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, programId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeletePurchasesByProgram.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Purchase>> getAllPurchases() {
    final String _sql = "SELECT * FROM purchases ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"purchases"}, new Callable<List<Purchase>>() {
      @Override
      @NonNull
      public List<Purchase> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProgramId = CursorUtil.getColumnIndexOrThrow(_cursor, "programId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfMiles = CursorUtil.getColumnIndexOrThrow(_cursor, "miles");
          final int _cursorIndexOfCost = CursorUtil.getColumnIndexOrThrow(_cursor, "cost");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final List<Purchase> _result = new ArrayList<Purchase>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Purchase _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpProgramId;
            _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            final LocalDate _tmpDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDate);
            }
            _tmpDate = __dateConverter.toLocalDate(_tmp);
            final int _tmpMiles;
            _tmpMiles = _cursor.getInt(_cursorIndexOfMiles);
            final double _tmpCost;
            _tmpCost = _cursor.getDouble(_cursorIndexOfCost);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item = new Purchase(_tmpId,_tmpProgramId,_tmpDate,_tmpMiles,_tmpCost,_tmpDescription);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public PagingSource<Integer, Purchase> getAllPurchasesPaged() {
    final String _sql = "SELECT * FROM purchases ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return new LimitOffsetPagingSource<Purchase>(_statement, __db, "purchases") {
      @Override
      @NonNull
      protected List<Purchase> convertRows(@NonNull final Cursor cursor) {
        final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(cursor, "id");
        final int _cursorIndexOfProgramId = CursorUtil.getColumnIndexOrThrow(cursor, "programId");
        final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(cursor, "date");
        final int _cursorIndexOfMiles = CursorUtil.getColumnIndexOrThrow(cursor, "miles");
        final int _cursorIndexOfCost = CursorUtil.getColumnIndexOrThrow(cursor, "cost");
        final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(cursor, "description");
        final List<Purchase> _result = new ArrayList<Purchase>(cursor.getCount());
        while (cursor.moveToNext()) {
          final Purchase _item;
          final long _tmpId;
          _tmpId = cursor.getLong(_cursorIndexOfId);
          final long _tmpProgramId;
          _tmpProgramId = cursor.getLong(_cursorIndexOfProgramId);
          final LocalDate _tmpDate;
          final String _tmp;
          if (cursor.isNull(_cursorIndexOfDate)) {
            _tmp = null;
          } else {
            _tmp = cursor.getString(_cursorIndexOfDate);
          }
          _tmpDate = __dateConverter.toLocalDate(_tmp);
          final int _tmpMiles;
          _tmpMiles = cursor.getInt(_cursorIndexOfMiles);
          final double _tmpCost;
          _tmpCost = cursor.getDouble(_cursorIndexOfCost);
          final String _tmpDescription;
          if (cursor.isNull(_cursorIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = cursor.getString(_cursorIndexOfDescription);
          }
          _item = new Purchase(_tmpId,_tmpProgramId,_tmpDate,_tmpMiles,_tmpCost,_tmpDescription);
          _result.add(_item);
        }
        return _result;
      }
    };
  }

  @Override
  public Flow<List<Purchase>> getPurchasesByProgram(final long programId) {
    final String _sql = "SELECT * FROM purchases WHERE programId = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, programId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"purchases"}, new Callable<List<Purchase>>() {
      @Override
      @NonNull
      public List<Purchase> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProgramId = CursorUtil.getColumnIndexOrThrow(_cursor, "programId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfMiles = CursorUtil.getColumnIndexOrThrow(_cursor, "miles");
          final int _cursorIndexOfCost = CursorUtil.getColumnIndexOrThrow(_cursor, "cost");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final List<Purchase> _result = new ArrayList<Purchase>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Purchase _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpProgramId;
            _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            final LocalDate _tmpDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDate);
            }
            _tmpDate = __dateConverter.toLocalDate(_tmp);
            final int _tmpMiles;
            _tmpMiles = _cursor.getInt(_cursorIndexOfMiles);
            final double _tmpCost;
            _tmpCost = _cursor.getDouble(_cursorIndexOfCost);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item = new Purchase(_tmpId,_tmpProgramId,_tmpDate,_tmpMiles,_tmpCost,_tmpDescription);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public PagingSource<Integer, Purchase> getPurchasesByProgramPaged(final long programId) {
    final String _sql = "SELECT * FROM purchases WHERE programId = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, programId);
    return new LimitOffsetPagingSource<Purchase>(_statement, __db, "purchases") {
      @Override
      @NonNull
      protected List<Purchase> convertRows(@NonNull final Cursor cursor) {
        final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(cursor, "id");
        final int _cursorIndexOfProgramId = CursorUtil.getColumnIndexOrThrow(cursor, "programId");
        final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(cursor, "date");
        final int _cursorIndexOfMiles = CursorUtil.getColumnIndexOrThrow(cursor, "miles");
        final int _cursorIndexOfCost = CursorUtil.getColumnIndexOrThrow(cursor, "cost");
        final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(cursor, "description");
        final List<Purchase> _result = new ArrayList<Purchase>(cursor.getCount());
        while (cursor.moveToNext()) {
          final Purchase _item;
          final long _tmpId;
          _tmpId = cursor.getLong(_cursorIndexOfId);
          final long _tmpProgramId;
          _tmpProgramId = cursor.getLong(_cursorIndexOfProgramId);
          final LocalDate _tmpDate;
          final String _tmp;
          if (cursor.isNull(_cursorIndexOfDate)) {
            _tmp = null;
          } else {
            _tmp = cursor.getString(_cursorIndexOfDate);
          }
          _tmpDate = __dateConverter.toLocalDate(_tmp);
          final int _tmpMiles;
          _tmpMiles = cursor.getInt(_cursorIndexOfMiles);
          final double _tmpCost;
          _tmpCost = cursor.getDouble(_cursorIndexOfCost);
          final String _tmpDescription;
          if (cursor.isNull(_cursorIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = cursor.getString(_cursorIndexOfDescription);
          }
          _item = new Purchase(_tmpId,_tmpProgramId,_tmpDate,_tmpMiles,_tmpCost,_tmpDescription);
          _result.add(_item);
        }
        return _result;
      }
    };
  }

  @Override
  public Object getPurchasesPage(final int limit, final int offset,
      final Continuation<? super List<Purchase>> $completion) {
    final String _sql = "SELECT * FROM purchases ORDER BY date DESC LIMIT ? OFFSET ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    _argIndex = 2;
    _statement.bindLong(_argIndex, offset);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Purchase>>() {
      @Override
      @NonNull
      public List<Purchase> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProgramId = CursorUtil.getColumnIndexOrThrow(_cursor, "programId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfMiles = CursorUtil.getColumnIndexOrThrow(_cursor, "miles");
          final int _cursorIndexOfCost = CursorUtil.getColumnIndexOrThrow(_cursor, "cost");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final List<Purchase> _result = new ArrayList<Purchase>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Purchase _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpProgramId;
            _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            final LocalDate _tmpDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDate);
            }
            _tmpDate = __dateConverter.toLocalDate(_tmp);
            final int _tmpMiles;
            _tmpMiles = _cursor.getInt(_cursorIndexOfMiles);
            final double _tmpCost;
            _tmpCost = _cursor.getDouble(_cursorIndexOfCost);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item = new Purchase(_tmpId,_tmpProgramId,_tmpDate,_tmpMiles,_tmpCost,_tmpDescription);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPurchaseById(final long id, final Continuation<? super Purchase> $completion) {
    final String _sql = "SELECT * FROM purchases WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Purchase>() {
      @Override
      @Nullable
      public Purchase call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfProgramId = CursorUtil.getColumnIndexOrThrow(_cursor, "programId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfMiles = CursorUtil.getColumnIndexOrThrow(_cursor, "miles");
          final int _cursorIndexOfCost = CursorUtil.getColumnIndexOrThrow(_cursor, "cost");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final Purchase _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpProgramId;
            _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            final LocalDate _tmpDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDate);
            }
            _tmpDate = __dateConverter.toLocalDate(_tmp);
            final int _tmpMiles;
            _tmpMiles = _cursor.getInt(_cursorIndexOfMiles);
            final double _tmpCost;
            _tmpCost = _cursor.getDouble(_cursorIndexOfCost);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _result = new Purchase(_tmpId,_tmpProgramId,_tmpDate,_tmpMiles,_tmpCost,_tmpDescription);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getProgramStatistics(final long programId,
      final Continuation<? super ProgramStatistics> $completion) {
    final String _sql = "\n"
            + "        SELECT\n"
            + "            programId,\n"
            + "            SUM(cost) as totalCost,\n"
            + "            SUM(miles) as totalMiles,\n"
            + "            CASE\n"
            + "                WHEN SUM(miles) > 0 THEN SUM(cost) / SUM(miles)\n"
            + "                ELSE 0.0\n"
            + "            END as averageCostPerMile\n"
            + "        FROM purchases\n"
            + "        WHERE programId = ?\n"
            + "        GROUP BY programId\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, programId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<ProgramStatistics>() {
      @Override
      @Nullable
      public ProgramStatistics call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgramId = 0;
          final int _cursorIndexOfTotalCost = 1;
          final int _cursorIndexOfTotalMiles = 2;
          final int _cursorIndexOfAverageCostPerMile = 3;
          final ProgramStatistics _result;
          if (_cursor.moveToFirst()) {
            final long _tmpProgramId;
            _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            final double _tmpTotalCost;
            _tmpTotalCost = _cursor.getDouble(_cursorIndexOfTotalCost);
            final int _tmpTotalMiles;
            _tmpTotalMiles = _cursor.getInt(_cursorIndexOfTotalMiles);
            final double _tmpAverageCostPerMile;
            _tmpAverageCostPerMile = _cursor.getDouble(_cursorIndexOfAverageCostPerMile);
            _result = new ProgramStatistics(_tmpProgramId,_tmpTotalCost,_tmpTotalMiles,_tmpAverageCostPerMile);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<ProgramStatisticsWithName>> getAllProgramStatistics() {
    final String _sql = "\n"
            + "        SELECT\n"
            + "            p.programId,\n"
            + "            pr.name as programName,\n"
            + "            SUM(p.cost) as totalCost,\n"
            + "            SUM(p.miles) as totalMiles,\n"
            + "            CASE\n"
            + "                WHEN SUM(p.miles) > 0 THEN SUM(p.cost) / SUM(p.miles)\n"
            + "                ELSE 0.0\n"
            + "            END as averageCostPerMile\n"
            + "        FROM purchases p\n"
            + "        INNER JOIN programs pr ON p.programId = pr.id\n"
            + "        WHERE pr.isActive = 1\n"
            + "        GROUP BY p.programId, pr.name\n"
            + "        ORDER BY pr.name\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"purchases",
        "programs"}, new Callable<List<ProgramStatisticsWithName>>() {
      @Override
      @NonNull
      public List<ProgramStatisticsWithName> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgramId = 0;
          final int _cursorIndexOfProgramName = 1;
          final int _cursorIndexOfTotalCost = 2;
          final int _cursorIndexOfTotalMiles = 3;
          final int _cursorIndexOfAverageCostPerMile = 4;
          final List<ProgramStatisticsWithName> _result = new ArrayList<ProgramStatisticsWithName>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ProgramStatisticsWithName _item;
            final long _tmpProgramId;
            _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            final String _tmpProgramName;
            if (_cursor.isNull(_cursorIndexOfProgramName)) {
              _tmpProgramName = null;
            } else {
              _tmpProgramName = _cursor.getString(_cursorIndexOfProgramName);
            }
            final double _tmpTotalCost;
            _tmpTotalCost = _cursor.getDouble(_cursorIndexOfTotalCost);
            final int _tmpTotalMiles;
            _tmpTotalMiles = _cursor.getInt(_cursorIndexOfTotalMiles);
            final double _tmpAverageCostPerMile;
            _tmpAverageCostPerMile = _cursor.getDouble(_cursorIndexOfAverageCostPerMile);
            _item = new ProgramStatisticsWithName(_tmpProgramId,_tmpProgramName,_tmpTotalCost,_tmpTotalMiles,_tmpAverageCostPerMile);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getGlobalStatistics(final Continuation<? super GlobalStatistics> $completion) {
    final String _sql = "\n"
            + "        SELECT\n"
            + "            0 as programId,\n"
            + "            SUM(p.cost) as totalCost,\n"
            + "            SUM(p.miles) as totalMiles,\n"
            + "            CASE\n"
            + "                WHEN SUM(p.miles) > 0 THEN SUM(p.cost) / SUM(p.miles)\n"
            + "                ELSE 0.0\n"
            + "            END as averageCostPerMile\n"
            + "        FROM purchases p\n"
            + "        INNER JOIN programs pr ON p.programId = pr.id\n"
            + "        WHERE pr.isActive = 1\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<GlobalStatistics>() {
      @Override
      @Nullable
      public GlobalStatistics call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfProgramId = 0;
          final int _cursorIndexOfTotalCost = 1;
          final int _cursorIndexOfTotalMiles = 2;
          final int _cursorIndexOfAverageCostPerMile = 3;
          final GlobalStatistics _result;
          if (_cursor.moveToFirst()) {
            final long _tmpProgramId;
            _tmpProgramId = _cursor.getLong(_cursorIndexOfProgramId);
            final double _tmpTotalCost;
            _tmpTotalCost = _cursor.getDouble(_cursorIndexOfTotalCost);
            final int _tmpTotalMiles;
            _tmpTotalMiles = _cursor.getInt(_cursorIndexOfTotalMiles);
            final double _tmpAverageCostPerMile;
            _tmpAverageCostPerMile = _cursor.getDouble(_cursorIndexOfAverageCostPerMile);
            _result = new GlobalStatistics(_tmpProgramId,_tmpTotalCost,_tmpTotalMiles,_tmpAverageCostPerMile);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalPurchaseCount(final Continuation<? super Integer> $completion) {
    final String _sql = "\n"
            + "        SELECT COUNT(*)\n"
            + "        FROM purchases p\n"
            + "        INNER JOIN programs pr ON p.programId = pr.id\n"
            + "        WHERE pr.isActive = 1\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
