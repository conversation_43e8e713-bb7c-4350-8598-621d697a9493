package com.sirch.mileagetracker.presentation.home;

import androidx.lifecycle.ViewModel;
import com.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName;
import com.sirch.mileagetracker.data.repository.AdsRepository;
import com.sirch.mileagetracker.data.repository.PurchaseRepository;
import dagger.hilt.android.lifecycle.HiltViewModel;
import kotlinx.coroutines.flow.StateFlow;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\b\u0010\u000e\u001a\u00020\u000fH\u0002J\b\u0010\u0010\u001a\u00020\u000fH\u0002J\u0006\u0010\u0011\u001a\u00020\u000fR\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0012"}, d2 = {"Lcom/sirch/mileagetracker/presentation/home/<USER>", "Landroidx/lifecycle/ViewModel;", "purchaseRepository", "Lcom/sirch/mileagetracker/data/repository/PurchaseRepository;", "adsRepository", "Lcom/sirch/mileagetracker/data/repository/AdsRepository;", "(Lcom/sirch/mileagetracker/data/repository/PurchaseRepository;Lcom/sirch/mileagetracker/data/repository/AdsRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/sirch/mileagetracker/presentation/home/<USER>", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "loadAdsStatus", "", "loadProgramStatistics", "refresh", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class HomeViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.PurchaseRepository purchaseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.AdsRepository adsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.sirch.mileagetracker.presentation.home.HomeUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.sirch.mileagetracker.presentation.home.HomeUiState> uiState = null;
    
    @javax.inject.Inject()
    public HomeViewModel(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.PurchaseRepository purchaseRepository, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.AdsRepository adsRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.sirch.mileagetracker.presentation.home.HomeUiState> getUiState() {
        return null;
    }
    
    private final void loadProgramStatistics() {
    }
    
    private final void loadAdsStatus() {
    }
    
    public final void refresh() {
    }
}