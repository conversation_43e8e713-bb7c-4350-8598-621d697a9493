package com.sirch.mileagetracker.presentation.theme;

import com.sirch.mileagetracker.data.repository.AdsRepository;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import javax.inject.Inject;
import javax.inject.Singleton;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bJ\u0016\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u000eJ\u000e\u0010\u000f\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u0010\u0010R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/sirch/mileagetracker/presentation/theme/ThemeManager;", "", "settingsRepository", "Lcom/sirch/mileagetracker/data/repository/SettingsRepository;", "adsRepository", "Lcom/sirch/mileagetracker/data/repository/AdsRepository;", "(Lcom/sirch/mileagetracker/data/repository/SettingsRepository;Lcom/sirch/mileagetracker/data/repository/AdsRepository;)V", "getThemeFlow", "Lkotlinx/coroutines/flow/Flow;", "Lcom/sirch/mileagetracker/presentation/theme/ThemeState;", "setDarkMode", "", "enabled", "", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "toggleDarkMode", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public final class ThemeManager {
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.AdsRepository adsRepository = null;
    
    @javax.inject.Inject()
    public ThemeManager(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.AdsRepository adsRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.sirch.mileagetracker.presentation.theme.ThemeState> getThemeFlow() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object toggleDarkMode(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object setDarkMode(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}