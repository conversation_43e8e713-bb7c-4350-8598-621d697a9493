package com.sirch.mileagetracker.data.local.dao

import androidx.room.*
import com.sirch.mileagetracker.data.local.entities.NotificationConfig
import com.sirch.mileagetracker.data.local.entities.NotificationType
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

/**
 * Data Access Object for NotificationConfig entity.
 */
@Dao
interface NotificationConfigDao {
    
    @Query("SELECT * FROM notification_configs WHERE userId = :userId ORDER BY type")
    fun getNotificationConfigsByUser(userId: String): Flow<List<NotificationConfig>>
    
    @Query("SELECT * FROM notification_configs WHERE userId = :userId AND type = :type")
    suspend fun getNotificationConfig(userId: String, type: NotificationType): NotificationConfig?
    
    @Query("SELECT * FROM notification_configs WHERE enabled = 1 AND nextScheduled <= :currentTime")
    suspend fun getNotificationsDue(currentTime: LocalDateTime): List<NotificationConfig>
    
    @Query("SELECT * FROM notification_configs WHERE userId = :userId AND isPremium = 1")
    fun getPremiumNotificationConfigs(userId: String): Flow<List<NotificationConfig>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertNotificationConfig(config: NotificationConfig): Long
    
    @Update
    suspend fun updateNotificationConfig(config: NotificationConfig)
    
    @Query("UPDATE notification_configs SET enabled = :enabled WHERE userId = :userId AND type = :type")
    suspend fun setNotificationEnabled(userId: String, type: NotificationType, enabled: Boolean)
    
    @Query("UPDATE notification_configs SET lastSent = :lastSent, nextScheduled = :nextScheduled WHERE id = :configId")
    suspend fun updateNotificationTiming(configId: Long, lastSent: LocalDateTime, nextScheduled: LocalDateTime)
    
    @Delete
    suspend fun deleteNotificationConfig(config: NotificationConfig)
    
    @Query("DELETE FROM notification_configs WHERE userId = :userId")
    suspend fun deleteNotificationConfigsByUser(userId: String)
}
