package com.sirch.mileagetracker.data.repository;

import com.sirch.mileagetracker.data.local.dao.ProgramDao;
import com.sirch.mileagetracker.data.local.entities.Program;
import kotlinx.coroutines.flow.Flow;
import javax.inject.Inject;
import javax.inject.Singleton;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0012\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\f0\u000bJ\u0018\u0010\r\u001a\u0004\u0018\u00010\b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0010J\u0016\u0010\u0011\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\b0\u000b2\u0006\u0010\u000e\u001a\u00020\u000fJ\u000e\u0010\u0012\u001a\u00020\u0013H\u0086@\u00a2\u0006\u0002\u0010\u0014J\u0016\u0010\u0015\u001a\u00020\u000f2\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u001c\u0010\u0016\u001a\u00020\u00062\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\b0\fH\u0086@\u00a2\u0006\u0002\u0010\u0018J\u000e\u0010\u0019\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010\u0014J\u000e\u0010\u001a\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010\u0014J\u0016\u0010\u001b\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/sirch/mileagetracker/data/repository/ProgramRepository;", "", "programDao", "Lcom/sirch/mileagetracker/data/local/dao/ProgramDao;", "(Lcom/sirch/mileagetracker/data/local/dao/ProgramDao;)V", "deleteProgram", "", "program", "Lcom/sirch/mileagetracker/data/local/entities/Program;", "(Lcom/sirch/mileagetracker/data/local/entities/Program;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllActivePrograms", "Lkotlinx/coroutines/flow/Flow;", "", "getProgramById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getProgramByIdFlow", "getProgramCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertProgram", "insertPrograms", "programs", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "seedDefaultPrograms", "seedTestData", "updateProgram", "app_release"})
public final class ProgramRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.local.dao.ProgramDao programDao = null;
    
    @javax.inject.Inject()
    public ProgramRepository(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.dao.ProgramDao programDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.sirch.mileagetracker.data.local.entities.Program>> getAllActivePrograms() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getProgramById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.local.entities.Program> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.sirch.mileagetracker.data.local.entities.Program> getProgramByIdFlow(long id) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertProgram(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Program program, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertPrograms(@org.jetbrains.annotations.NotNull()
    java.util.List<com.sirch.mileagetracker.data.local.entities.Program> programs, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateProgram(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Program program, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteProgram(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Program program, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getProgramCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object seedDefaultPrograms(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object seedTestData(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}