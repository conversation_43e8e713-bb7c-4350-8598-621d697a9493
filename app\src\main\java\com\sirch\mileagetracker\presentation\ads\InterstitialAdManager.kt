package com.sirch.mileagetracker.presentation.ads

import android.app.Activity
import android.content.Context
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow

class InterstitialAdManager(
    private val context: Context,
    private val adUnitId: String,
    private val showAds: Boolean = true
) {
    private var interstitialAd: InterstitialAd? = null
    private var isLoading = false
    
    private val _adEvents = Channel<AdEvent>(Channel.UNLIMITED)
    val adEvents = _adEvents.receiveAsFlow()
    
    sealed class AdEvent {
        object AdLoaded : AdEvent()
        data class AdFailedToLoad(val error: LoadAdError) : AdEvent()
        object AdShown : AdEvent()
        object AdDismissed : AdEvent()
        data class AdFailedToShow(val error: AdError) : AdEvent()
    }
    
    init {
        if (showAds) {
            loadAd()
        }
    }
    
    private fun loadAd() {
        if (isLoading || interstitialAd != null) return
        
        isLoading = true
        val adRequest = AdRequest.Builder().build()
        
        InterstitialAd.load(
            context,
            adUnitId,
            adRequest,
            object : InterstitialAdLoadCallback() {
                override fun onAdLoaded(ad: InterstitialAd) {
                    interstitialAd = ad
                    isLoading = false
                    _adEvents.trySend(AdEvent.AdLoaded)
                    
                    ad.fullScreenContentCallback = object : FullScreenContentCallback() {
                        override fun onAdDismissedFullScreenContent() {
                            interstitialAd = null
                            _adEvents.trySend(AdEvent.AdDismissed)
                            // Load next ad
                            loadAd()
                        }
                        
                        override fun onAdFailedToShowFullScreenContent(error: AdError) {
                            interstitialAd = null
                            _adEvents.trySend(AdEvent.AdFailedToShow(error))
                            // Load next ad
                            loadAd()
                        }
                        
                        override fun onAdShowedFullScreenContent() {
                            _adEvents.trySend(AdEvent.AdShown)
                        }
                    }
                }
                
                override fun onAdFailedToLoad(error: LoadAdError) {
                    interstitialAd = null
                    isLoading = false
                    _adEvents.trySend(AdEvent.AdFailedToLoad(error))
                }
            }
        )
    }
    
    fun showAd(activity: Activity): Boolean {
        return if (interstitialAd != null && showAds) {
            interstitialAd?.show(activity)
            true
        } else {
            // If no ad is ready, load one for next time
            if (!isLoading) {
                loadAd()
            }
            false
        }
    }
    
    fun isAdReady(): Boolean = interstitialAd != null
    
    fun destroy() {
        interstitialAd = null
        _adEvents.close()
    }
}

@Composable
fun rememberInterstitialAdManager(
    adUnitId: String = AdUnitIds.INTERSTITIAL_ID,
    showAds: Boolean = true
): InterstitialAdManager {
    val context = LocalContext.current
    
    return remember(adUnitId, showAds) {
        InterstitialAdManager(
            context = context,
            adUnitId = adUnitId,
            showAds = showAds
        )
    }
}

@Composable
fun InterstitialAdEffect(
    adManager: InterstitialAdManager,
    onAdLoaded: () -> Unit = {},
    onAdFailedToLoad: (LoadAdError) -> Unit = {},
    onAdShown: () -> Unit = {},
    onAdDismissed: () -> Unit = {},
    onAdFailedToShow: (AdError) -> Unit = {}
) {
    LaunchedEffect(adManager) {
        adManager.adEvents.collect { event ->
            when (event) {
                is InterstitialAdManager.AdEvent.AdLoaded -> onAdLoaded()
                is InterstitialAdManager.AdEvent.AdFailedToLoad -> onAdFailedToLoad(event.error)
                is InterstitialAdManager.AdEvent.AdShown -> onAdShown()
                is InterstitialAdManager.AdEvent.AdDismissed -> onAdDismissed()
                is InterstitialAdManager.AdEvent.AdFailedToShow -> onAdFailedToShow(event.error)
            }
        }
    }
    
    DisposableEffect(adManager) {
        onDispose {
            adManager.destroy()
        }
    }
}
