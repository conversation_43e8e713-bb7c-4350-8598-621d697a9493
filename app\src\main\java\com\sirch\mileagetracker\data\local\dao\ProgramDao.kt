package com.sirch.mileagetracker.data.local.dao

import androidx.room.*
import com.sirch.mileagetracker.data.local.entities.Program
import kotlinx.coroutines.flow.Flow

@Dao
interface ProgramDao {
    
    @Query("SELECT * FROM programs WHERE isActive = 1 ORDER BY name ASC")
    fun getAllActivePrograms(): Flow<List<Program>>
    
    @Query("SELECT * FROM programs WHERE id = :id")
    suspend fun getProgramById(id: Long): Program?
    
    @Query("SELECT * FROM programs WHERE id = :id")
    fun getProgramByIdFlow(id: Long): Flow<Program?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProgram(program: Program): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPrograms(programs: List<Program>)
    
    @Update
    suspend fun updateProgram(program: Program)
    
    @Delete
    suspend fun deleteProgram(program: Program)
    
    @Query("SELECT COUNT(*) FROM programs")
    suspend fun getProgramCount(): Int
    
    @Query("DELETE FROM programs")
    suspend fun deleteAllPrograms()
}
