package com.sirch.mileagetracker.data.local.dao

import androidx.room.*
import com.sirch.mileagetracker.data.local.entities.AggregatedData
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

/**
 * Data Access Object for AggregatedData entity.
 * Provides queries for managing anonymized aggregate statistics.
 */
@Dao
interface AggregatedDataDao {
    
    /**
     * Get all aggregated data
     */
    @Query("SELECT * FROM aggregated_data ORDER BY lastUpdated DESC")
    fun getAllAggregatedData(): Flow<List<AggregatedData>>
    
    /**
     * Get aggregated data for a specific program
     */
    @Query("SELECT * FROM aggregated_data WHERE programId = :programId ORDER BY lastUpdated DESC")
    suspend fun getAggregatedDataByProgram(programId: Long): List<AggregatedData>
    
    /**
     * Get the most recent aggregated data for a program
     */
    @Query("""
        SELECT * FROM aggregated_data 
        WHERE programId = :programId 
        ORDER BY lastUpdated DESC 
        LIMIT 1
    """)
    suspend fun getLatestAggregatedData(programId: Long): AggregatedData?
    
    /**
     * Get aggregated data for a specific program and region
     */
    @Query("""
        SELECT * FROM aggregated_data 
        WHERE programId = :programId AND region = :region 
        ORDER BY lastUpdated DESC 
        LIMIT 1
    """)
    suspend fun getAggregatedDataByProgramAndRegion(programId: Long, region: String): AggregatedData?
    
    /**
     * Get aggregated data that is still valid (not expired)
     */
    @Query("""
        SELECT * FROM aggregated_data 
        WHERE lastUpdated > :cutoffTime 
        ORDER BY lastUpdated DESC
    """)
    suspend fun getValidAggregatedData(cutoffTime: LocalDateTime): List<AggregatedData>
    
    /**
     * Get aggregated data that needs updating (expired)
     */
    @Query("""
        SELECT * FROM aggregated_data 
        WHERE lastUpdated < :cutoffTime
    """)
    suspend fun getExpiredAggregatedData(cutoffTime: LocalDateTime): List<AggregatedData>
    
    /**
     * Get aggregated data with sufficient sample size
     */
    @Query("""
        SELECT * FROM aggregated_data 
        WHERE sampleSize >= :minSampleSize 
        ORDER BY lastUpdated DESC
    """)
    suspend fun getReliableAggregatedData(minSampleSize: Int = 10): List<AggregatedData>
    
    /**
     * Get aggregated data statistics across all programs
     */
    @Query("""
        SELECT 
            COUNT(*) as totalRecords,
            AVG(anonymizedAvgCostPerMile) as overallAvgCost,
            AVG(sampleSize) as avgSampleSize,
            MIN(lastUpdated) as oldestUpdate,
            MAX(lastUpdated) as newestUpdate
        FROM aggregated_data
    """)
    suspend fun getAggregatedDataStatistics(): AggregatedDataStatistics?
    
    /**
     * Get programs with available aggregated data
     */
    @Query("""
        SELECT DISTINCT programId 
        FROM aggregated_data 
        WHERE lastUpdated > :cutoffTime AND sampleSize >= :minSampleSize
    """)
    suspend fun getProgramsWithValidData(cutoffTime: LocalDateTime, minSampleSize: Int = 10): List<Long>
    
    /**
     * Insert aggregated data
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAggregatedData(aggregatedData: AggregatedData): Long
    
    /**
     * Insert multiple aggregated data records
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAggregatedData(aggregatedDataList: List<AggregatedData>)
    
    /**
     * Update aggregated data
     */
    @Update
    suspend fun updateAggregatedData(aggregatedData: AggregatedData)
    
    /**
     * Delete aggregated data
     */
    @Delete
    suspend fun deleteAggregatedData(aggregatedData: AggregatedData)
    
    /**
     * Delete aggregated data for a specific program
     */
    @Query("DELETE FROM aggregated_data WHERE programId = :programId")
    suspend fun deleteAggregatedDataByProgram(programId: Long)
    
    /**
     * Delete expired aggregated data
     */
    @Query("DELETE FROM aggregated_data WHERE lastUpdated < :cutoffTime")
    suspend fun deleteExpiredAggregatedData(cutoffTime: LocalDateTime): Int
    
    /**
     * Delete aggregated data with insufficient sample size
     */
    @Query("DELETE FROM aggregated_data WHERE sampleSize < :minSampleSize")
    suspend fun deleteUnreliableAggregatedData(minSampleSize: Int = 10): Int
    
    /**
     * Update data version for all records
     */
    @Query("UPDATE aggregated_data SET dataVersion = :newVersion WHERE dataVersion = :oldVersion")
    suspend fun updateDataVersion(oldVersion: Int, newVersion: Int): Int
    
    /**
     * Get count of aggregated data records by program
     */
    @Query("SELECT COUNT(*) FROM aggregated_data WHERE programId = :programId")
    suspend fun getAggregatedDataCount(programId: Long): Int
    
    /**
     * Get aggregated data quality score distribution
     */
    @Query("""
        SELECT 
            programId,
            COUNT(*) as recordCount,
            AVG(sampleSize) as avgSampleSize,
            AVG(confidenceLevel) as avgConfidence,
            MAX(lastUpdated) as latestUpdate
        FROM aggregated_data 
        GROUP BY programId
    """)
    suspend fun getDataQualityByProgram(): List<DataQualityInfo>
}

/**
 * Data class for aggregated data statistics
 */
data class AggregatedDataStatistics(
    val totalRecords: Int,
    val overallAvgCost: Double,
    val avgSampleSize: Double,
    val oldestUpdate: LocalDateTime,
    val newestUpdate: LocalDateTime
)

/**
 * Data class for data quality information by program
 */
data class DataQualityInfo(
    val programId: Long,
    val recordCount: Int,
    val avgSampleSize: Double,
    val avgConfidence: Double,
    val latestUpdate: LocalDateTime
)
