package com.sirch.mileagetracker.presentation.purchase;

import com.sirch.mileagetracker.data.repository.ProgramRepository;
import com.sirch.mileagetracker.data.repository.PurchaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PurchaseFormViewModel_Factory implements Factory<PurchaseFormViewModel> {
  private final Provider<PurchaseRepository> purchaseRepositoryProvider;

  private final Provider<ProgramRepository> programRepositoryProvider;

  public PurchaseFormViewModel_Factory(Provider<PurchaseRepository> purchaseRepositoryProvider,
      Provider<ProgramRepository> programRepositoryProvider) {
    this.purchaseRepositoryProvider = purchaseRepositoryProvider;
    this.programRepositoryProvider = programRepositoryProvider;
  }

  @Override
  public PurchaseFormViewModel get() {
    return newInstance(purchaseRepositoryProvider.get(), programRepositoryProvider.get());
  }

  public static PurchaseFormViewModel_Factory create(
      Provider<PurchaseRepository> purchaseRepositoryProvider,
      Provider<ProgramRepository> programRepositoryProvider) {
    return new PurchaseFormViewModel_Factory(purchaseRepositoryProvider, programRepositoryProvider);
  }

  public static PurchaseFormViewModel newInstance(PurchaseRepository purchaseRepository,
      ProgramRepository programRepository) {
    return new PurchaseFormViewModel(purchaseRepository, programRepository);
  }
}
