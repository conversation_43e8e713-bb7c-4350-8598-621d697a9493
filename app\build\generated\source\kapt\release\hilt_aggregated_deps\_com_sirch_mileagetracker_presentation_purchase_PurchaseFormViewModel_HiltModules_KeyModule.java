package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ActivityRetainedComponent",
    modules = "com.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel_HiltModules.KeyModule"
)
@Generated("dagger.hilt.processor.internal.aggregateddeps.AggregatedDepsGenerator")
public class _com_sirch_mileagetracker_presentation_purchase_PurchaseFormViewModel_HiltModules_KeyModule {
}
