package com.sirch.mileagetracker.data.local.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey
import androidx.room.Index
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime

/**
 * Entity representing user's mileage goals and objectives.
 * Supports both program-specific and overall mileage targets.
 *
 * @property id Unique identifier for the goal
 * @property userId Firebase user ID
 * @property programId Reference to specific program (null for overall goals)
 * @property title User-defined goal title
 * @property description Optional goal description
 * @property targetMiles Target number of miles to achieve
 * @property currentMiles Current progress towards the goal
 * @property targetDate Target completion date
 * @property isActive Whether the goal is currently active
 * @property createdAt Goal creation timestamp
 * @property completedAt Goal completion timestamp (null if not completed)
 * @property goalType Type of goal (MILES, SPENDING, EFFICIENCY)
 * @property targetValue Target value for non-miles goals (e.g., spending limit)
 * @property currentValue Current value for non-miles goals
 * @property isPremium Whether this is a premium goal feature
 */
@Entity(
    tableName = "goals",
    foreignKeys = [
        ForeignKey(
            entity = Program::class,
            parentColumns = ["id"],
            childColumns = ["programId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["userId"]),
        Index(value = ["programId"]),
        Index(value = ["userId", "isActive"]),
        Index(value = ["targetDate"]),
        Index(value = ["goalType"]),
        Index(value = ["createdAt"])
    ]
)
data class Goal(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,

    val userId: String,

    val programId: Long? = null, // null for overall goals

    val title: String,

    val description: String? = null,

    val targetMiles: Int,

    val currentMiles: Int = 0,

    val targetDate: LocalDate,

    val isActive: Boolean = true,

    val createdAt: LocalDateTime,

    val completedAt: LocalDateTime? = null,

    val goalType: GoalType = GoalType.MILES,

    val targetValue: Double? = null, // For spending or efficiency goals

    val currentValue: Double? = null,

    val isPremium: Boolean = false
) {
    /**
     * Calculate completion percentage (0-100)
     */
    fun getCompletionPercentage(): Double {
        return when (goalType) {
            GoalType.MILES -> {
                if (targetMiles > 0) {
                    minOf(100.0, (currentMiles.toDouble() / targetMiles.toDouble()) * 100.0)
                } else 0.0
            }
            GoalType.SPENDING, GoalType.EFFICIENCY -> {
                if (targetValue != null && targetValue > 0 && currentValue != null) {
                    minOf(100.0, (currentValue / targetValue) * 100.0)
                } else 0.0
            }
        }
    }

    /**
     * Check if the goal is completed
     */
    fun isCompleted(): Boolean {
        return when (goalType) {
            GoalType.MILES -> currentMiles >= targetMiles
            GoalType.SPENDING, GoalType.EFFICIENCY -> {
                targetValue != null && currentValue != null && currentValue >= targetValue
            }
        }
    }

    /**
     * Check if the goal is overdue
     */
    fun isOverdue(): Boolean {
        // Simplified implementation - will be enhanced later
        return false
    }

    /**
     * Get days remaining until target date
     */
    fun getDaysRemaining(): Int {
        // Simplified implementation - will be enhanced later
        return 30
    }

    /**
     * Calculate required daily progress to meet the goal
     */
    fun getRequiredDailyProgress(): Double {
        val daysRemaining = getDaysRemaining()
        if (daysRemaining <= 0) return 0.0

        return when (goalType) {
            GoalType.MILES -> {
                val remainingMiles = targetMiles - currentMiles
                if (remainingMiles > 0) remainingMiles.toDouble() / daysRemaining else 0.0
            }
            GoalType.SPENDING, GoalType.EFFICIENCY -> {
                if (targetValue != null && currentValue != null) {
                    val remainingValue = targetValue - currentValue
                    if (remainingValue > 0) remainingValue / daysRemaining else 0.0
                } else 0.0
            }
        }
    }

    /**
     * Get goal status description
     */
    fun getStatusDescription(): String {
        return when {
            isCompleted() -> "Completed"
            isOverdue() -> "Overdue"
            getDaysRemaining() <= 7 -> "Due Soon"
            getCompletionPercentage() >= 75 -> "On Track"
            getCompletionPercentage() >= 50 -> "In Progress"
            else -> "Behind Schedule"
        }
    }

    /**
     * Get progress description for UI display
     */
    fun getProgressDescription(): String {
        return when (goalType) {
            GoalType.MILES -> "$currentMiles / $targetMiles miles"
            GoalType.SPENDING -> {
                if (currentValue != null && targetValue != null) {
                    "R$ %.2f / R$ %.2f".format(currentValue, targetValue)
                } else "No data"
            }
            GoalType.EFFICIENCY -> {
                if (currentValue != null && targetValue != null) {
                    "%.1f / %.1f score".format(currentValue, targetValue)
                } else "No data"
            }
        }
    }
}

/**
 * Types of goals supported by the application
 */
enum class GoalType {
    MILES,      // Target number of miles to accumulate
    SPENDING,   // Target spending amount
    EFFICIENCY  // Target efficiency score
}
