{"logs": [{"outputFile": "com.sirch.mileagetracker.app-mergeReleaseResources-3:/values-bn/values-bn.xml", "map": [{"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\fcd13fdc941282eaa2071fb31e6df1d3\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,283,373,471,557,636,742,829,918,988,1058,1136,1217,1300,1375,1443", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "194,278,368,466,552,631,737,824,913,983,1053,1131,1212,1295,1370,1438,1556"}, "to": {"startLines": "11,12,32,33,34,38,39,99,100,104,113,114,115,123,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1060,1154,3511,3601,3699,4102,4181,10698,10785,11134,11964,12034,12112,12619,12803,12878,12946", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "1149,1233,3596,3694,3780,4176,4282,10780,10869,11199,12029,12107,12188,12697,12873,12941,13059"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\51f776a662999a2c4066cf59dc47f5c4\\transformed\\play-services-basement-18.4.0\\res\\values-bn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2219", "endColumns": "151", "endOffsets": "2366"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\831f7f0f93fd70afcef46ca0120dc927\\transformed\\foundation-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "128,129", "startColumns": "4,4", "startOffsets": "13064,13149", "endColumns": "84,84", "endOffsets": "13144,13229"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\ce7063c7507d7ba5446267220fbed412\\transformed\\credentials-1.2.0-rc01\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,111", "endOffsets": "164,276"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,219", "endColumns": "113,111", "endOffsets": "214,326"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\10e14192bcf7bea6e0ef145c1ad40304\\transformed\\play-services-ads-23.5.0\\res\\values-bn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,243,290,344,412,485,592,654,770,896,1012,1066,1120,1223,1320,1360,1445,1483,1528,1584,1670,1718", "endColumns": "43,46,53,67,72,106,61,115,125,115,53,53,102,96,39,84,37,44,55,85,47,55", "endOffsets": "242,289,343,411,484,591,653,769,895,1011,1065,1119,1222,1319,1359,1444,1482,1527,1583,1669,1717,1773"}, "to": {"startLines": "96,97,98,101,102,103,105,106,107,108,109,110,111,112,116,117,118,119,120,121,122,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10541,10589,10640,10874,10946,11023,11204,11270,11390,11520,11640,11698,11756,11863,12193,12237,12326,12368,12417,12477,12567,13234", "endColumns": "47,50,57,71,76,110,65,119,129,119,57,57,106,100,43,88,41,48,59,89,51,59", "endOffsets": "10584,10635,10693,10941,11018,11129,11265,11385,11515,11635,11693,11751,11858,11959,12232,12321,12363,12412,12472,12562,12614,13289"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\3793d766ef17a5c374cbb3773985b3be\\transformed\\play-services-base-18.5.0\\res\\values-bn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,453,577,684,816,934,1042,1142,1281,1386,1538,1662,1791,1935,1991,2054", "endColumns": "104,154,123,106,131,117,107,99,138,104,151,123,128,143,55,62,85", "endOffsets": "297,452,576,683,815,933,1041,1141,1280,1385,1537,1661,1790,1934,1990,2053,2139"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1238,1347,1506,1634,1745,1881,2003,2115,2371,2514,2623,2779,2907,3040,3188,3248,3315", "endColumns": "108,158,127,110,135,121,111,103,142,108,155,127,132,147,59,66,89", "endOffsets": "1342,1501,1629,1740,1876,1998,2110,2214,2509,2618,2774,2902,3035,3183,3243,3310,3400"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\17af91994e13f583c0b6091f8c6fb16f\\transformed\\core-1.15.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "4,5,6,7,8,9,10,124", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "331,430,532,634,737,838,940,12702", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "425,527,629,732,833,935,1055,12798"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\e995885063661f84453093b90e093640\\transformed\\browser-1.8.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,105", "endOffsets": "156,258,367,473"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3405,3785,3887,3996", "endColumns": "105,101,108,105", "endOffsets": "3506,3882,3991,4097"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\4f87b4fe7e5f13faf091c8d26d70e189\\transformed\\material3-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,193,330,449,583,700,799,915,1057,1178,1320,1405,1511,1605,1706,1835,1964,2075,2204,2331,2461,2641,2763,2883,3005,3136,3231,3326,3459,3606,3703,3808,3918,4045,4177,4284,4385,4462,4565,4665,4756,4846,4949,5029,5114,5215,5319,5412,5517,5604,5710,5809,5917,6035,6115,6215", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "188,325,444,578,695,794,910,1052,1173,1315,1400,1506,1600,1701,1830,1959,2070,2199,2326,2456,2636,2758,2878,3000,3131,3226,3321,3454,3601,3698,3803,3913,4040,4172,4279,4380,4457,4560,4660,4751,4841,4944,5024,5109,5210,5314,5407,5512,5599,5705,5804,5912,6030,6110,6210,6304"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4287,4425,4562,4681,4815,4932,5031,5147,5289,5410,5552,5637,5743,5837,5938,6067,6196,6307,6436,6563,6693,6873,6995,7115,7237,7368,7463,7558,7691,7838,7935,8040,8150,8277,8409,8516,8617,8694,8797,8897,8988,9078,9181,9261,9346,9447,9551,9644,9749,9836,9942,10041,10149,10267,10347,10447", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "4420,4557,4676,4810,4927,5026,5142,5284,5405,5547,5632,5738,5832,5933,6062,6191,6302,6431,6558,6688,6868,6990,7110,7232,7363,7458,7553,7686,7833,7930,8035,8145,8272,8404,8511,8612,8689,8792,8892,8983,9073,9176,9256,9341,9442,9546,9639,9744,9831,9937,10036,10144,10262,10342,10442,10536"}}]}]}