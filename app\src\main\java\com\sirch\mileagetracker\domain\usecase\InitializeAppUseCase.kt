package com.sirch.mileagetracker.domain.usecase

import com.sirch.mileagetracker.data.local.entities.Purchase
import com.sirch.mileagetracker.data.repository.ProgramRepository
import com.sirch.mileagetracker.data.repository.PurchaseRepository
import com.sirch.mileagetracker.data.repository.SettingsRepository
import kotlinx.datetime.LocalDate
import javax.inject.Inject

class InitializeAppUseCase @Inject constructor(
    private val programRepository: ProgramRepository,
    private val purchaseRepository: PurchaseRepository,
    private val settingsRepository: SettingsRepository
) {

    suspend operator fun invoke() {
        // Initialize settings if not exists
        settingsRepository.initializeSettings()

        // Seed default programs if database is empty
        programRepository.seedDefaultPrograms()

        // Note: Test purchases removed for production
        // Users will add their own real data
    }


}
