package com.sirch.mileagetracker.presentation.premium

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sirch.mileagetracker.R
import com.sirch.mileagetracker.domain.premium.*
import com.sirch.mileagetracker.presentation.premium.components.*

/**
 * Premium upgrade screen showing subscription options and features
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PremiumUpgradeScreen(
    onNavigateBack: () -> Unit,
    onNavigateToSubscriptionManagement: () -> Unit,
    viewModel: PremiumUpgradeViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.loadSubscriptionProducts()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Premium Features") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Header section
            item {
                PremiumHeaderCard()
            }

            // Current subscription status
            if (uiState.subscriptionStatus.isActive) {
                item {
                    CurrentSubscriptionCard(
                        subscriptionStatus = uiState.subscriptionStatus,
                        onManageSubscription = onNavigateToSubscriptionManagement
                    )
                }
            }

            // Subscription options
            if (!uiState.subscriptionStatus.isActive) {
                item {
                    Text(
                        text = "Choose Your Plan",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }

                items(uiState.subscriptionProducts) { product ->
                    SubscriptionProductCard(
                        product = product,
                        isLoading = uiState.isLoading,
                        onPurchase = { viewModel.purchaseSubscription(context, product.productId) }
                    )
                }
            }

            // Features by category
            item {
                Text(
                    text = "Premium Features",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            items(FeatureCategory.values().toList()) { category ->
                FeatureCategoryCard(
                    category = category,
                    features = PremiumFeature.getFeaturesByCategory(category),
                    hasAccess = uiState.subscriptionStatus.tier.hasFeature(
                        PremiumFeature.getFeaturesByCategory(category).firstOrNull() ?: PremiumFeature.EFFICIENCY_ADVANCED_INSIGHTS
                    )
                )
            }

            // Terms and privacy
            item {
                TermsAndPrivacySection()
            }
        }
    }

    // Handle purchase events
    LaunchedEffect(uiState.purchaseEvent) {
        uiState.purchaseEvent?.let { event ->
            // Handle purchase events here
            // For now, just clear the event
            viewModel.clearPurchaseEvent()
        }
    }
}

/**
 * Header card with premium benefits overview
 */
@Composable
private fun PremiumHeaderCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "🚀 Unlock Premium",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "Get advanced efficiency insights, detailed reports, and priority support",
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }
    }
}

/**
 * Current subscription status card
 */
@Composable
private fun CurrentSubscriptionCard(
    subscriptionStatus: SubscriptionStatus,
    onManageSubscription: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "✅ Premium Active",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )

                    Text(
                        text = subscriptionStatus.tier.displayName,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                }

                TextButton(onClick = onManageSubscription) {
                    Text("Manage")
                }
            }

            // Expiry warning if needed
            subscriptionStatus.getDaysUntilExpiry()?.let { daysLeft ->
                if (daysLeft <= 7) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "⚠️ Expires in $daysLeft days",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
}

/**
 * Terms and privacy section
 */
@Composable
private fun TermsAndPrivacySection() {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "By subscribing, you agree to our Terms of Service and Privacy Policy. Subscriptions auto-renew unless cancelled.",
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(8.dp))

        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            TextButton(onClick = { /* Navigate to terms */ }) {
                Text("Terms of Service")
            }

            TextButton(onClick = { /* Navigate to privacy */ }) {
                Text("Privacy Policy")
            }
        }
    }
}
