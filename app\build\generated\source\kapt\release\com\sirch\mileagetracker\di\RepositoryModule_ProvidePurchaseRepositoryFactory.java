package com.sirch.mileagetracker.di;

import com.sirch.mileagetracker.data.local.dao.PurchaseDao;
import com.sirch.mileagetracker.data.repository.PurchaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvidePurchaseRepositoryFactory implements Factory<PurchaseRepository> {
  private final Provider<PurchaseDao> purchaseDaoProvider;

  public RepositoryModule_ProvidePurchaseRepositoryFactory(
      Provider<PurchaseDao> purchaseDaoProvider) {
    this.purchaseDaoProvider = purchaseDaoProvider;
  }

  @Override
  public PurchaseRepository get() {
    return providePurchaseRepository(purchaseDaoProvider.get());
  }

  public static RepositoryModule_ProvidePurchaseRepositoryFactory create(
      Provider<PurchaseDao> purchaseDaoProvider) {
    return new RepositoryModule_ProvidePurchaseRepositoryFactory(purchaseDaoProvider);
  }

  public static PurchaseRepository providePurchaseRepository(PurchaseDao purchaseDao) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.providePurchaseRepository(purchaseDao));
  }
}
