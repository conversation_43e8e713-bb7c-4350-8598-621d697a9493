package com.sirch.mileagetracker.data.local.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey
import androidx.room.Index
import kotlinx.datetime.LocalDateTime

/**
 * Entity representing user's efficiency score for a specific mileage program.
 * Stores both personal and aggregate statistics for comparison.
 *
 * @property id Unique identifier for the efficiency score record
 * @property userId Firebase user ID (hashed for privacy)
 * @property programId Reference to the mileage program
 * @property personalAvgCostPerMile User's personal average cost per mile for this program
 * @property aggregateAvgCostPerMile Anonymized aggregate average from all users
 * @property score Calculated efficiency score (0-100 scale)
 * @property percentile User's percentile ranking (0-100)
 * @property lastUpdated Timestamp of last score calculation
 * @property sampleSize Number of users in the aggregate calculation
 * @property isDataSharingEnabled Whether user consented to data sharing
 */
@Entity(
    tableName = "efficiency_scores",
    foreignKeys = [
        ForeignKey(
            entity = Program::class,
            parentColumns = ["id"],
            childColumns = ["programId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["userId"]),
        Index(value = ["programId"]),
        Index(value = ["userId", "programId"], unique = true),
        Index(value = ["lastUpdated"]),
        Index(value = ["score"])
    ]
)
data class EfficiencyScore(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,

    val userId: String,

    val programId: Long,

    val personalAvgCostPerMile: Double,

    val aggregateAvgCostPerMile: Double,

    val score: Double, // 0-100 scale where 100 is most efficient

    val percentile: Double, // 0-100 percentile ranking

    val lastUpdated: LocalDateTime,

    val sampleSize: Int, // Number of users in aggregate calculation

    val isDataSharingEnabled: Boolean = true
) {
    /**
     * Calculate star rating based on percentile (1-5 stars)
     */
    fun getStarRating(): Int {
        return when {
            percentile >= 90 -> 5
            percentile >= 75 -> 4
            percentile >= 50 -> 3
            percentile >= 25 -> 2
            else -> 1
        }
    }

    /**
     * Get efficiency category description
     */
    fun getEfficiencyCategory(): String {
        return when {
            percentile >= 80 -> "Excellent"
            percentile >= 60 -> "Good"
            percentile >= 40 -> "Average"
            percentile >= 20 -> "Below Average"
            else -> "Needs Improvement"
        }
    }

    /**
     * Check if the score data is recent (within last 7 days)
     */
    fun isDataRecent(): Boolean {
        // Simplified implementation - will be enhanced later
        return true
    }

    /**
     * Calculate potential savings if user achieved aggregate average
     */
    fun getPotentialSavings(totalMiles: Int): Double {
        return if (personalAvgCostPerMile > aggregateAvgCostPerMile) {
            (personalAvgCostPerMile - aggregateAvgCostPerMile) * totalMiles
        } else {
            0.0
        }
    }
}
