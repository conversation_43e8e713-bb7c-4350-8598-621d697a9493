package com.sirch.mileagetracker.presentation.theme;

import androidx.lifecycle.ViewModel;
import dagger.hilt.android.lifecycle.HiltViewModel;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u0006\u0010\u000b\u001a\u00020\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\f"}, d2 = {"Lcom/sirch/mileagetracker/presentation/theme/ThemeViewModel;", "Landroidx/lifecycle/ViewModel;", "themeManager", "Lcom/sirch/mileagetracker/presentation/theme/ThemeManager;", "(Lcom/sirch/mileagetracker/presentation/theme/ThemeManager;)V", "getThemeManager", "()Lcom/sirch/mileagetracker/presentation/theme/ThemeManager;", "setDarkMode", "", "enabled", "", "toggleDarkMode", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ThemeViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.presentation.theme.ThemeManager themeManager = null;
    
    @javax.inject.Inject()
    public ThemeViewModel(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.presentation.theme.ThemeManager themeManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.presentation.theme.ThemeManager getThemeManager() {
        return null;
    }
    
    public final void toggleDarkMode() {
    }
    
    public final void setDarkMode(boolean enabled) {
    }
}