package com.sirch.mileagetracker.data.local.dao

import androidx.room.*
import com.sirch.mileagetracker.data.local.entities.ReportTemplate
import com.sirch.mileagetracker.data.local.entities.ReportType
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

/**
 * Data Access Object for ReportTemplate entity.
 */
@Dao
interface ReportTemplateDao {
    
    @Query("SELECT * FROM report_templates WHERE userId = :userId ORDER BY lastUsed DESC, createdAt DESC")
    fun getReportTemplatesByUser(userId: String): Flow<List<ReportTemplate>>
    
    @Query("SELECT * FROM report_templates WHERE userId = :userId AND reportType = :reportType ORDER BY lastUsed DESC")
    fun getReportTemplatesByType(userId: String, reportType: ReportType): Flow<List<ReportTemplate>>
    
    @Query("SELECT * FROM report_templates WHERE userId = :userId AND isPremium = 1 ORDER BY lastUsed DESC")
    fun getPremiumReportTemplates(userId: String): Flow<List<ReportTemplate>>
    
    @Query("SELECT * FROM report_templates WHERE shareCode = :shareCode")
    suspend fun getReportTemplateByShareCode(shareCode: String): ReportTemplate?
    
    @Query("SELECT * FROM report_templates WHERE userId = :userId ORDER BY useCount DESC LIMIT :limit")
    suspend fun getMostUsedTemplates(userId: String, limit: Int = 5): List<ReportTemplate>
    
    @Query("SELECT COUNT(*) FROM report_templates WHERE userId = :userId AND isPremium = 0")
    suspend fun getFreeTemplateCount(userId: String): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertReportTemplate(template: ReportTemplate): Long
    
    @Update
    suspend fun updateReportTemplate(template: ReportTemplate)
    
    @Query("UPDATE report_templates SET useCount = useCount + 1, lastUsed = :lastUsed WHERE id = :templateId")
    suspend fun incrementTemplateUsage(templateId: Long, lastUsed: LocalDateTime)
    
    @Query("UPDATE report_templates SET isShared = :isShared, shareCode = :shareCode WHERE id = :templateId")
    suspend fun updateTemplateSharing(templateId: Long, isShared: Boolean, shareCode: String?)
    
    @Delete
    suspend fun deleteReportTemplate(template: ReportTemplate)
    
    @Query("DELETE FROM report_templates WHERE userId = :userId")
    suspend fun deleteReportTemplatesByUser(userId: String)
}
