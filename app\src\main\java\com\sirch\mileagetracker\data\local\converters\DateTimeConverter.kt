package com.sirch.mileagetracker.data.local.converters

import androidx.room.TypeConverter
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime

/**
 * Room TypeConverter for LocalDateTime objects.
 * Converts LocalDateTime to/from Long (epoch milliseconds) for database storage.
 * 
 * Uses system timezone for conversion to ensure consistency across different devices.
 */
class DateTimeConverter {
    
    /**
     * Convert LocalDateTime to Long (epoch milliseconds) for database storage
     */
    @TypeConverter
    fun fromLocalDateTime(dateTime: LocalDateTime?): Long? {
        return dateTime?.toInstant(TimeZone.currentSystemDefault())?.toEpochMilliseconds()
    }
    
    /**
     * Convert Long (epoch milliseconds) to LocalDateTime from database
     */
    @TypeConverter
    fun toLocalDateTime(timestamp: Long?): LocalDateTime? {
        return timestamp?.let { 
            kotlinx.datetime.Instant.fromEpochMilliseconds(it)
                .toLocalDateTime(TimeZone.currentSystemDefault())
        }
    }
}
