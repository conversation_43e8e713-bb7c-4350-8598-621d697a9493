package com.sirch.mileagetracker.presentation.program;

import androidx.lifecycle.ViewModel;
import androidx.paging.PagingData;
import com.sirch.mileagetracker.data.local.dao.ProgramStatistics;
import com.sirch.mileagetracker.data.local.entities.Program;
import com.sirch.mileagetracker.data.local.entities.Purchase;
import com.sirch.mileagetracker.data.repository.ProgramRepository;
import com.sirch.mileagetracker.data.repository.PurchaseRepository;
import dagger.hilt.android.lifecycle.HiltViewModel;
import kotlinx.coroutines.flow.StateFlow;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0013\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001BC\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\fH\u00c6\u0003JG\u0010\u001c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\u00032\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001J\t\u0010!\u001a\u00020\fH\u00d6\u0001R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0010R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006\""}, d2 = {"Lcom/sirch/mileagetracker/presentation/program/ProgramDetailsUiState;", "", "isLoading", "", "program", "Lcom/sirch/mileagetracker/data/local/entities/Program;", "purchases", "", "Lcom/sirch/mileagetracker/data/local/entities/Purchase;", "statistics", "Lcom/sirch/mileagetracker/data/local/dao/ProgramStatistics;", "error", "", "(ZLcom/sirch/mileagetracker/data/local/entities/Program;Ljava/util/List;Lcom/sirch/mileagetracker/data/local/dao/ProgramStatistics;Ljava/lang/String;)V", "getError", "()Ljava/lang/String;", "()Z", "getProgram", "()Lcom/sirch/mileagetracker/data/local/entities/Program;", "getPurchases", "()Ljava/util/List;", "getStatistics", "()Lcom/sirch/mileagetracker/data/local/dao/ProgramStatistics;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_release"})
public final class ProgramDetailsUiState {
    private final boolean isLoading = false;
    @org.jetbrains.annotations.Nullable()
    private final com.sirch.mileagetracker.data.local.entities.Program program = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.sirch.mileagetracker.data.local.entities.Purchase> purchases = null;
    @org.jetbrains.annotations.Nullable()
    private final com.sirch.mileagetracker.data.local.dao.ProgramStatistics statistics = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String error = null;
    
    public ProgramDetailsUiState(boolean isLoading, @org.jetbrains.annotations.Nullable()
    com.sirch.mileagetracker.data.local.entities.Program program, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sirch.mileagetracker.data.local.entities.Purchase> purchases, @org.jetbrains.annotations.Nullable()
    com.sirch.mileagetracker.data.local.dao.ProgramStatistics statistics, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        super();
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sirch.mileagetracker.data.local.entities.Program getProgram() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sirch.mileagetracker.data.local.entities.Purchase> getPurchases() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sirch.mileagetracker.data.local.dao.ProgramStatistics getStatistics() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getError() {
        return null;
    }
    
    public ProgramDetailsUiState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sirch.mileagetracker.data.local.entities.Program component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sirch.mileagetracker.data.local.entities.Purchase> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sirch.mileagetracker.data.local.dao.ProgramStatistics component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.presentation.program.ProgramDetailsUiState copy(boolean isLoading, @org.jetbrains.annotations.Nullable()
    com.sirch.mileagetracker.data.local.entities.Program program, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sirch.mileagetracker.data.local.entities.Purchase> purchases, @org.jetbrains.annotations.Nullable()
    com.sirch.mileagetracker.data.local.dao.ProgramStatistics statistics, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}