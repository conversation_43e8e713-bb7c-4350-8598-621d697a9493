package com.sirch.mileagetracker.data.cloud;

import com.google.firebase.auth.FirebaseAuth;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import javax.inject.Inject;
import javax.inject.Singleton;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 \u00122\u00020\u0001:\u0003\u0011\u0012\u0013B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u000e\u0010\n\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\n\u0010\u000b\u001a\u0004\u0018\u00010\fH\u0002J\u000e\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\tJ\u000e\u0010\u000f\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/sirch/mileagetracker/data/cloud/CloudBackupService;", "", "firebaseAuth", "Lcom/google/firebase/auth/FirebaseAuth;", "settingsRepository", "Lcom/sirch/mileagetracker/data/repository/SettingsRepository;", "(Lcom/google/firebase/auth/FirebaseAuth;Lcom/sirch/mileagetracker/data/repository/SettingsRepository;)V", "backupToCloud", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$BackupResult;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCloudBackup", "getCurrentUserId", "", "hasCloudBackup", "", "restoreFromCloud", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult;", "BackupResult", "Companion", "RestoreResult", "app_release"})
public final class CloudBackupService {
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.auth.FirebaseAuth firebaseAuth = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String COLLECTION_USERS = "users";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String COLLECTION_PROGRAMS = "programs";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String COLLECTION_PURCHASES = "purchases";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String COLLECTION_SETTINGS = "settings";
    @org.jetbrains.annotations.NotNull()
    public static final com.sirch.mileagetracker.data.cloud.CloudBackupService.Companion Companion = null;
    
    @javax.inject.Inject()
    public CloudBackupService(@org.jetbrains.annotations.NotNull()
    com.google.firebase.auth.FirebaseAuth firebaseAuth, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository) {
        super();
    }
    
    private final java.lang.String getCurrentUserId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object backupToCloud(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object restoreFromCloud(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteCloudBackup(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object hasCloudBackup(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0003\u0003\u0004\u0005B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0003\u0006\u0007\b\u00a8\u0006\t"}, d2 = {"Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$BackupResult;", "", "()V", "Error", "Success", "UserNotLoggedIn", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$BackupResult$Error;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$BackupResult$Success;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$BackupResult$UserNotLoggedIn;", "app_release"})
    public static abstract class BackupResult {
        
        private BackupResult() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$BackupResult$Error;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$BackupResult;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_release"})
        public static final class Error extends com.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String message = null;
            
            public Error(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult.Error copy(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$BackupResult$Success;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$BackupResult;", "()V", "app_release"})
        public static final class Success extends com.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult {
            @org.jetbrains.annotations.NotNull()
            public static final com.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult.Success INSTANCE = null;
            
            private Success() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$BackupResult$UserNotLoggedIn;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$BackupResult;", "()V", "app_release"})
        public static final class UserNotLoggedIn extends com.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult {
            @org.jetbrains.annotations.NotNull()
            public static final com.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult.UserNotLoggedIn INSTANCE = null;
            
            private UserNotLoggedIn() {
            }
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$Companion;", "", "()V", "COLLECTION_PROGRAMS", "", "COLLECTION_PURCHASES", "COLLECTION_SETTINGS", "COLLECTION_USERS", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult;", "", "()V", "Error", "NoBackupFound", "Success", "UserNotLoggedIn", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult$Error;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult$NoBackupFound;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult$Success;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult$UserNotLoggedIn;", "app_release"})
    public static abstract class RestoreResult {
        
        private RestoreResult() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult$Error;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_release"})
        public static final class Error extends com.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String message = null;
            
            public Error(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult.Error copy(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult$NoBackupFound;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult;", "()V", "app_release"})
        public static final class NoBackupFound extends com.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult {
            @org.jetbrains.annotations.NotNull()
            public static final com.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult.NoBackupFound INSTANCE = null;
            
            private NoBackupFound() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005J\t\u0010\t\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\n\u001a\u00020\u0003H\u00c6\u0003J\u001d\u0010\u000b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u00d6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007\u00a8\u0006\u0013"}, d2 = {"Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult$Success;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult;", "programsCount", "", "purchasesCount", "(II)V", "getProgramsCount", "()I", "getPurchasesCount", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "toString", "", "app_release"})
        public static final class Success extends com.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult {
            private final int programsCount = 0;
            private final int purchasesCount = 0;
            
            public Success(int programsCount, int purchasesCount) {
            }
            
            public final int getProgramsCount() {
                return 0;
            }
            
            public final int getPurchasesCount() {
                return 0;
            }
            
            public final int component1() {
                return 0;
            }
            
            public final int component2() {
                return 0;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult.Success copy(int programsCount, int purchasesCount) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult$UserNotLoggedIn;", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService$RestoreResult;", "()V", "app_release"})
        public static final class UserNotLoggedIn extends com.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult {
            @org.jetbrains.annotations.NotNull()
            public static final com.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult.UserNotLoggedIn INSTANCE = null;
            
            private UserNotLoggedIn() {
            }
        }
    }
}