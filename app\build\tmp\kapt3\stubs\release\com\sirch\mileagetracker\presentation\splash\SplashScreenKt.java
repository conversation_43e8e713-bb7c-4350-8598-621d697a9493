package com.sirch.mileagetracker.presentation.splash;

import androidx.compose.foundation.layout.*;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.text.style.TextAlign;
import com.sirch.mileagetracker.R;
import com.sirch.mileagetracker.presentation.auth.AuthViewModel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001aB\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u0007\u00a8\u0006\u000b"}, d2 = {"SplashScreen", "", "onNavigateToLogin", "Lkotlin/Function0;", "onNavigateToHome", "modifier", "Landroidx/compose/ui/Modifier;", "authViewModel", "Lcom/sirch/mileagetracker/presentation/auth/AuthViewModel;", "splashViewModel", "Lcom/sirch/mileagetracker/presentation/splash/SplashViewModel;", "app_release"})
public final class SplashScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void SplashScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToLogin, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToHome, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.presentation.auth.AuthViewModel authViewModel, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.presentation.splash.SplashViewModel splashViewModel) {
    }
}