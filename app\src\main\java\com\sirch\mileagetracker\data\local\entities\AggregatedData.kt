package com.sirch.mileagetracker.data.local.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey
import androidx.room.Index
import kotlinx.datetime.LocalDateTime

/**
 * Entity storing anonymized aggregate statistics for efficiency score calculations.
 * Contains no personally identifiable information (PII) and follows LGPD/GDPR compliance.
 *
 * @property id Unique identifier for the aggregate data record
 * @property programId Reference to the mileage program
 * @property anonymizedAvgCostPerMile Aggregate average cost per mile (anonymized)
 * @property sampleSize Number of users contributing to this aggregate (minimum 10 for privacy)
 * @property lastUpdated Timestamp of last aggregate calculation
 * @property region Geographic region code for segmented analysis (optional)
 * @property dataVersion Version of aggregation algorithm used
 * @property confidenceLevel Statistical confidence level of the aggregate (0-100)
 * @property minCostPerMile Minimum cost per mile in the sample (for outlier detection)
 * @property maxCostPerMile Maximum cost per mile in the sample (for outlier detection)
 * @property standardDeviation Standard deviation of the sample
 */
@Entity(
    tableName = "aggregated_data",
    foreignKeys = [
        ForeignKey(
            entity = Program::class,
            parentColumns = ["id"],
            childColumns = ["programId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["programId"]),
        Index(value = ["lastUpdated"]),
        Index(value = ["region"]),
        Index(value = ["programId", "region"], unique = true),
        Index(value = ["sampleSize"])
    ]
)
data class AggregatedData(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,

    val programId: Long,

    val anonymizedAvgCostPerMile: Double,

    val sampleSize: Int, // Minimum 10 for k-anonymity compliance

    val lastUpdated: LocalDateTime,

    val region: String? = null, // ISO country code or region identifier

    val dataVersion: Int = 1, // Version of aggregation algorithm

    val confidenceLevel: Double, // Statistical confidence (0-100)

    val minCostPerMile: Double,

    val maxCostPerMile: Double,

    val standardDeviation: Double
) {
    companion object {
        const val MIN_SAMPLE_SIZE = 10 // Minimum users for k-anonymity
        const val MIN_CONFIDENCE_LEVEL = 80.0 // Minimum confidence for reliable data
        const val MAX_DATA_AGE_DAYS = 30 // Maximum age for aggregate data validity
    }

    /**
     * Check if the aggregate data is statistically reliable
     */
    fun isReliable(): Boolean {
        return sampleSize >= MIN_SAMPLE_SIZE &&
               confidenceLevel >= MIN_CONFIDENCE_LEVEL
    }

    /**
     * Check if the aggregate data is recent enough to be valid
     */
    fun isDataValid(): Boolean {
        // Simplified implementation - will be enhanced later
        return true
    }

    /**
     * Get data quality score (0-100) based on sample size, confidence, and recency
     */
    fun getDataQualityScore(): Double {
        val sampleScore = minOf(100.0, (sampleSize.toDouble() / 100.0) * 100.0)
        val confidenceScore = confidenceLevel
        val recencyScore = if (isDataValid()) 100.0 else 50.0

        return (sampleScore + confidenceScore + recencyScore) / 3.0
    }

    /**
     * Check if a user's cost per mile is an outlier compared to this aggregate
     */
    fun isOutlier(userCostPerMile: Double): Boolean {
        val threshold = 2.0 * standardDeviation
        return kotlin.math.abs(userCostPerMile - anonymizedAvgCostPerMile) > threshold
    }

    /**
     * Calculate percentile for a given cost per mile value
     */
    fun calculatePercentile(userCostPerMile: Double): Double {
        // Simplified percentile calculation using normal distribution approximation
        val zScore = (userCostPerMile - anonymizedAvgCostPerMile) / standardDeviation

        // Convert z-score to percentile (lower cost = higher percentile)
        return when {
            zScore <= -2.0 -> 95.0 // Very efficient (low cost)
            zScore <= -1.0 -> 84.0
            zScore <= 0.0 -> 50.0 + (25.0 * (-zScore))
            zScore <= 1.0 -> 50.0 - (34.0 * zScore)
            zScore <= 2.0 -> 16.0 - (14.0 * (zScore - 1.0))
            else -> 2.0 // Very inefficient (high cost)
        }
    }
}
