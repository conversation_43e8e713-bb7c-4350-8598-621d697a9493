package com.sirch.mileagetracker.di;

import com.sirch.mileagetracker.data.local.MileageTrackerDatabase;
import com.sirch.mileagetracker.data.local.dao.PurchaseDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvidePurchaseDaoFactory implements Factory<PurchaseDao> {
  private final Provider<MileageTrackerDatabase> databaseProvider;

  public DatabaseModule_ProvidePurchaseDaoFactory(
      Provider<MileageTrackerDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public PurchaseDao get() {
    return providePurchaseDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvidePurchaseDaoFactory create(
      Provider<MileageTrackerDatabase> databaseProvider) {
    return new DatabaseModule_ProvidePurchaseDaoFactory(databaseProvider);
  }

  public static PurchaseDao providePurchaseDao(MileageTrackerDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.providePurchaseDao(database));
  }
}
