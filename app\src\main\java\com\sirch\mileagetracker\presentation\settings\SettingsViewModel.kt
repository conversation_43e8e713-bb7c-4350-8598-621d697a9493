package com.sirch.mileagetracker.presentation.settings

import android.app.Activity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.android.billingclient.api.BillingClient
import com.sirch.mileagetracker.data.auth.AuthRepository
import com.sirch.mileagetracker.data.billing.BillingManager
import com.sirch.mileagetracker.data.cloud.CloudBackupService
import com.sirch.mileagetracker.data.repository.AdsRepository
import com.sirch.mileagetracker.data.repository.SettingsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val adsRepository: AdsRepository,
    private val billingManager: BillingManager,
    private val settingsRepository: SettingsRepository,
    private val cloudBackupService: CloudBackupService
) : ViewModel() {

    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

    init {
        loadUserInfo()
        loadPremiumStatus()
        loadRemoveAdsPrice()
        loadSettings()
        observeBillingEvents()
    }

    private fun loadUserInfo() {
        viewModelScope.launch {
            authRepository.getCurrentUserFlow()
                .catch { exception ->
                    _uiState.value = _uiState.value.copy(
                        error = exception.message
                    )
                }
                .collect { user ->
                    _uiState.value = _uiState.value.copy(
                        userEmail = user?.email
                    )
                }
        }
    }

    private fun loadPremiumStatus() {
        viewModelScope.launch {
            combine(
                adsRepository.areAdsRemoved(),
                billingManager.connectionState
            ) { adsRemoved, connectionState ->
                Pair(adsRemoved, connectionState)
            }
            .catch { exception ->
                _uiState.value = _uiState.value.copy(
                    error = exception.message
                )
            }
            .collect { (adsRemoved, connectionState) ->
                _uiState.value = _uiState.value.copy(
                    isPremium = adsRemoved,
                    isBillingConnected = connectionState is BillingManager.BillingConnectionState.CONNECTED
                )
            }
        }
    }

    private fun loadRemoveAdsPrice() {
        viewModelScope.launch {
            billingManager.productDetails
                .collect { productDetailsList ->
                    val removeAdsDetails = productDetailsList.find {
                        it.productId == BillingManager.REMOVE_ADS_PRODUCT_ID
                    }
                    _uiState.value = _uiState.value.copy(
                        removeAdsPrice = removeAdsDetails?.oneTimePurchaseOfferDetails?.formattedPrice
                    )
                }
        }
    }

    private fun observeBillingEvents() {
        viewModelScope.launch {
            billingManager.purchaseEvents
                .collect { event ->
                    when (event) {
                        is BillingManager.PurchaseEvent.PurchaseCompleted -> {
                            _uiState.value = _uiState.value.copy(
                                isPurchasing = false,
                                isPremium = true,
                                successMessage = "Anúncios removidos com sucesso!",
                                error = null
                            )
                            // Clear success message after a delay
                            clearSuccessMessage()
                        }
                        is BillingManager.PurchaseEvent.PurchaseFailed -> {
                            _uiState.value = _uiState.value.copy(
                                isPurchasing = false,
                                error = "Erro na compra: ${event.error}",
                                successMessage = null
                            )
                        }
                        is BillingManager.PurchaseEvent.PurchaseCancelled -> {
                            _uiState.value = _uiState.value.copy(
                                isPurchasing = false,
                                error = null,
                                successMessage = null
                            )
                        }
                    }
                }
        }
    }

    fun purchaseRemoveAds(activity: Activity) {
        if (_uiState.value.isPurchasing || !_uiState.value.isBillingConnected) {
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isPurchasing = true,
                error = null,
                successMessage = null
            )

            try {
                val result = billingManager.launchBillingFlow(activity, BillingManager.REMOVE_ADS_PRODUCT_ID)

                if (result.responseCode != BillingClient.BillingResponseCode.OK) {
                    _uiState.value = _uiState.value.copy(
                        isPurchasing = false,
                        error = "Erro ao iniciar compra: ${result.debugMessage}"
                    )
                }
                // If successful, the purchase flow will handle the rest via billing events
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isPurchasing = false,
                    error = "Erro inesperado: ${e.message}"
                )
            }
        }
    }

    fun signOut() {
        viewModelScope.launch {
            val result = authRepository.signOut()
            if (result.isSuccess) {
                _uiState.value = _uiState.value.copy(
                    userEmail = null,
                    successMessage = "Logout realizado com sucesso"
                )
                clearSuccessMessage()
            } else {
                _uiState.value = _uiState.value.copy(
                    error = "Erro ao fazer logout: ${result.exceptionOrNull()?.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    private fun clearSuccessMessage() {
        viewModelScope.launch {
            kotlinx.coroutines.delay(3000) // 3 seconds
            _uiState.value = _uiState.value.copy(successMessage = null)
        }
    }

    private fun loadSettings() {
        viewModelScope.launch {
            settingsRepository.getSettings()
                .catch { exception ->
                    _uiState.value = _uiState.value.copy(
                        error = exception.message
                    )
                }
                .collect { settings ->
                    val lastBackupDate = if (settings?.lastBackupTimestamp != null && settings.lastBackupTimestamp > 0) {
                        java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault())
                            .format(java.util.Date(settings.lastBackupTimestamp))
                    } else null

                    _uiState.value = _uiState.value.copy(
                        darkModeEnabled = settings?.darkModeEnabled ?: false,
                        cloudBackupEnabled = settings?.cloudBackupEnabled ?: false,
                        lastBackupDate = lastBackupDate
                    )
                }
        }
    }

    fun toggleDarkMode() {
        if (!_uiState.value.isPremium) return

        viewModelScope.launch {
            try {
                val newValue = !_uiState.value.darkModeEnabled
                settingsRepository.updateDarkMode(newValue)
                _uiState.value = _uiState.value.copy(darkModeEnabled = newValue)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Erro ao alterar modo escuro: ${e.message}"
                )
            }
        }
    }

    fun backupToCloud() {
        if (!_uiState.value.isPremium) return

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isBackupInProgress = true,
                error = null
            )

            when (val result = cloudBackupService.backupToCloud()) {
                is CloudBackupService.BackupResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isBackupInProgress = false,
                        successMessage = "Backup realizado com sucesso!"
                    )
                    loadSettings() // Reload to get updated timestamp
                    clearSuccessMessage()
                }
                is CloudBackupService.BackupResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isBackupInProgress = false,
                        error = "Erro no backup: ${result.message}"
                    )
                }
                is CloudBackupService.BackupResult.UserNotLoggedIn -> {
                    _uiState.value = _uiState.value.copy(
                        isBackupInProgress = false,
                        error = "Faça login para usar o backup na nuvem"
                    )
                }
            }
        }
    }

    fun restoreFromCloud() {
        if (!_uiState.value.isPremium) return

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isBackupInProgress = true,
                error = null
            )

            when (val result = cloudBackupService.restoreFromCloud()) {
                is CloudBackupService.RestoreResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isBackupInProgress = false,
                        successMessage = "Dados restaurados! ${result.programsCount} programas, ${result.purchasesCount} compras"
                    )
                    clearSuccessMessage()
                }
                is CloudBackupService.RestoreResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isBackupInProgress = false,
                        error = "Erro na restauração: ${result.message}"
                    )
                }
                is CloudBackupService.RestoreResult.UserNotLoggedIn -> {
                    _uiState.value = _uiState.value.copy(
                        isBackupInProgress = false,
                        error = "Faça login para usar o backup na nuvem"
                    )
                }
                is CloudBackupService.RestoreResult.NoBackupFound -> {
                    _uiState.value = _uiState.value.copy(
                        isBackupInProgress = false,
                        error = "Nenhum backup encontrado na nuvem"
                    )
                }
            }
        }
    }
}

data class SettingsUiState(
    val userEmail: String? = null,
    val isPremium: Boolean = false,
    val isBillingConnected: Boolean = false,
    val isPurchasing: Boolean = false,
    val removeAdsPrice: String? = null,
    val darkModeEnabled: Boolean = false,
    val cloudBackupEnabled: Boolean = false,
    val lastBackupDate: String? = null,
    val isBackupInProgress: Boolean = false,
    val error: String? = null,
    val successMessage: String? = null
)
