{"logs": [{"outputFile": "com.sirch.mileagetracker.app-mergeReleaseResources-3:/values-sv/values-sv.xml", "map": [{"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\17af91994e13f583c0b6091f8c6fb16f\\transformed\\core-1.15.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "4,5,6,7,8,9,10,124", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "330,425,527,625,724,832,937,12526", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "420,522,620,719,827,932,1053,12622"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\ce7063c7507d7ba5446267220fbed412\\transformed\\credentials-1.2.0-rc01\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,112", "endOffsets": "162,275"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,217", "endColumns": "111,112", "endOffsets": "212,325"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\10e14192bcf7bea6e0ef145c1ad40304\\transformed\\play-services-ads-23.5.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,244,291,346,410,479,581,643,759,859,973,1023,1079,1190,1273,1311,1394,1429,1469,1519,1599,1637", "endColumns": "44,46,54,63,68,101,61,115,99,113,49,55,110,82,37,82,34,39,49,79,37,55", "endOffsets": "243,290,345,409,478,580,642,758,858,972,1022,1078,1189,1272,1310,1393,1428,1468,1518,1598,1636,1692"}, "to": {"startLines": "96,97,98,101,102,103,105,106,107,108,109,110,111,112,116,117,118,119,120,121,122,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10461,10510,10561,10790,10858,10931,11101,11167,11287,11391,11509,11563,11623,11738,12065,12107,12194,12233,12277,12331,12415,13071", "endColumns": "48,50,58,67,72,105,65,119,103,117,53,59,114,86,41,86,38,43,53,83,41,59", "endOffsets": "10505,10556,10615,10853,10926,11032,11162,11282,11386,11504,11558,11618,11733,11820,12102,12189,12228,12272,12326,12410,12452,13126"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\831f7f0f93fd70afcef46ca0120dc927\\transformed\\foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "128,129", "startColumns": "4,4", "startOffsets": "12892,12982", "endColumns": "89,88", "endOffsets": "12977,13066"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\fcd13fdc941282eaa2071fb31e6df1d3\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,967,1031,1117,1207,1276,1354,1421", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,962,1026,1112,1202,1271,1349,1416,1536"}, "to": {"startLines": "11,12,32,33,34,38,39,99,100,104,113,114,115,123,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1058,1151,3545,3641,3740,4139,4215,10620,10709,11037,11825,11889,11975,12457,12627,12705,12772", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "1146,1234,3636,3735,3823,4210,4298,10704,10785,11096,11884,11970,12060,12521,12700,12767,12887"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\4f87b4fe7e5f13faf091c8d26d70e189\\transformed\\material3-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,327,434,566,682,778,891,1035,1159,1314,1399,1498,1588,1682,1796,1918,2022,2155,2282,2417,2589,2717,2835,2961,3081,3172,3270,3388,3527,3623,3731,3834,3967,4110,4216,4313,4393,4491,4583,4667,4752,4853,4933,5018,5117,5217,5312,5412,5499,5603,5704,5808,5930,6010,6114", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "189,322,429,561,677,773,886,1030,1154,1309,1394,1493,1583,1677,1791,1913,2017,2150,2277,2412,2584,2712,2830,2956,3076,3167,3265,3383,3522,3618,3726,3829,3962,4105,4211,4308,4388,4486,4578,4662,4747,4848,4928,5013,5112,5212,5307,5407,5494,5598,5699,5803,5925,6005,6109,6208"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4303,4442,4575,4682,4814,4930,5026,5139,5283,5407,5562,5647,5746,5836,5930,6044,6166,6270,6403,6530,6665,6837,6965,7083,7209,7329,7420,7518,7636,7775,7871,7979,8082,8215,8358,8464,8561,8641,8739,8831,8915,9000,9101,9181,9266,9365,9465,9560,9660,9747,9851,9952,10056,10178,10258,10362", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "4437,4570,4677,4809,4925,5021,5134,5278,5402,5557,5642,5741,5831,5925,6039,6161,6265,6398,6525,6660,6832,6960,7078,7204,7324,7415,7513,7631,7770,7866,7974,8077,8210,8353,8459,8556,8636,8734,8826,8910,8995,9096,9176,9261,9360,9460,9555,9655,9742,9846,9947,10051,10173,10253,10357,10456"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\e995885063661f84453093b90e093640\\transformed\\browser-1.8.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3445,3828,3928,4041", "endColumns": "99,99,112,97", "endOffsets": "3540,3923,4036,4134"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\3793d766ef17a5c374cbb3773985b3be\\transformed\\play-services-base-18.5.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1239,1346,1503,1630,1740,1881,2006,2129,2381,2529,2637,2799,2927,3081,3237,3303,3366", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "1341,1498,1625,1735,1876,2001,2124,2228,2524,2632,2794,2922,3076,3232,3298,3361,3440"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\51f776a662999a2c4066cf59dc47f5c4\\transformed\\play-services-basement-18.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2233", "endColumns": "147", "endOffsets": "2376"}}]}]}