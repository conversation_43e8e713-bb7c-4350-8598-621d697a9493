package com.sirch.mileagetracker.data.repository;

import androidx.paging.Pager;
import androidx.paging.PagingConfig;
import androidx.paging.PagingData;
import com.sirch.mileagetracker.data.local.dao.GlobalStatistics;
import com.sirch.mileagetracker.data.local.dao.ProgramStatistics;
import com.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName;
import com.sirch.mileagetracker.data.local.dao.PurchaseDao;
import com.sirch.mileagetracker.data.local.entities.Purchase;
import kotlinx.coroutines.flow.Flow;
import javax.inject.Inject;
import javax.inject.Singleton;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0016\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0012\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000fJ\u0012\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00100\u000fJ\u0012\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00140\u000fJ\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0018\u0010\u0018\u001a\u0004\u0018\u00010\u00192\u0006\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0018\u0010\u001a\u001a\u0004\u0018\u00010\b2\u0006\u0010\u001b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u001a\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00100\u000f2\u0006\u0010\u000b\u001a\u00020\fJ\u001a\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00140\u000f2\u0006\u0010\u000b\u001a\u00020\fJ\u000e\u0010\u001e\u001a\u00020\u001fH\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010 \u001a\u00020\f2\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0016\u0010!\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/sirch/mileagetracker/data/repository/PurchaseRepository;", "", "purchaseDao", "Lcom/sirch/mileagetracker/data/local/dao/PurchaseDao;", "(Lcom/sirch/mileagetracker/data/local/dao/PurchaseDao;)V", "deletePurchase", "", "purchase", "Lcom/sirch/mileagetracker/data/local/entities/Purchase;", "(Lcom/sirch/mileagetracker/data/local/entities/Purchase;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePurchasesByProgram", "programId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllProgramStatistics", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/sirch/mileagetracker/data/local/dao/ProgramStatisticsWithName;", "getAllPurchases", "getAllPurchasesPaged", "Landroidx/paging/PagingData;", "getGlobalStatistics", "Lcom/sirch/mileagetracker/data/local/dao/GlobalStatistics;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getProgramStatistics", "Lcom/sirch/mileagetracker/data/local/dao/ProgramStatistics;", "getPurchaseById", "id", "getPurchasesByProgram", "getPurchasesByProgramPaged", "getTotalPurchaseCount", "", "insertPurchase", "updatePurchase", "app_release"})
public final class PurchaseRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.local.dao.PurchaseDao purchaseDao = null;
    
    @javax.inject.Inject()
    public PurchaseRepository(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.dao.PurchaseDao purchaseDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.sirch.mileagetracker.data.local.entities.Purchase>> getAllPurchases() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<androidx.paging.PagingData<com.sirch.mileagetracker.data.local.entities.Purchase>> getAllPurchasesPaged() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.sirch.mileagetracker.data.local.entities.Purchase>> getPurchasesByProgram(long programId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<androidx.paging.PagingData<com.sirch.mileagetracker.data.local.entities.Purchase>> getPurchasesByProgramPaged(long programId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPurchaseById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.local.entities.Purchase> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertPurchase(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Purchase purchase, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updatePurchase(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Purchase purchase, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deletePurchase(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Purchase purchase, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deletePurchasesByProgram(long programId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getProgramStatistics(long programId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.local.dao.ProgramStatistics> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName>> getAllProgramStatistics() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getGlobalStatistics(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.local.dao.GlobalStatistics> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTotalPurchaseCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
}