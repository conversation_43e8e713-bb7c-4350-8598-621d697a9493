package com.sirch.mileagetracker.data.repository;

import com.sirch.mileagetracker.data.repository.SettingsRepository;
import kotlinx.coroutines.flow.Flow;
import javax.inject.Inject;
import javax.inject.Singleton;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u000e\u0010\b\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\tJ\u000e\u0010\n\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u000e\u0010\f\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0002\u0010\tJ\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/sirch/mileagetracker/data/repository/AdsRepository;", "", "settingsRepository", "Lcom/sirch/mileagetracker/data/repository/SettingsRepository;", "(Lcom/sirch/mileagetracker/data/repository/SettingsRepository;)V", "areAdsRemoved", "Lkotlinx/coroutines/flow/Flow;", "", "getCurrentAdsStatus", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeAds", "", "restoreAds", "shouldShowAds", "app_release"})
public final class AdsRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository = null;
    
    @javax.inject.Inject()
    public AdsRepository(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository) {
        super();
    }
    
    /**
     * Returns true if ads should be shown (user hasn't purchased ad removal)
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> shouldShowAds() {
        return null;
    }
    
    /**
     * Returns true if ads are removed (user purchased ad removal)
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Boolean> areAdsRemoved() {
        return null;
    }
    
    /**
     * Remove ads (called after successful purchase)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeAds(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Restore ads (for testing or refund scenarios)
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object restoreAds(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Check current ads status synchronously
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentAdsStatus(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
}