package com.sirch.mileagetracker.data.local;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;
import android.content.Context;
import com.sirch.mileagetracker.data.local.converters.DateConverter;
import com.sirch.mileagetracker.data.local.dao.ProgramDao;
import com.sirch.mileagetracker.data.local.dao.PurchaseDao;
import com.sirch.mileagetracker.data.local.dao.SettingsDao;
import com.sirch.mileagetracker.data.local.entities.Program;
import com.sirch.mileagetracker.data.local.entities.Purchase;
import com.sirch.mileagetracker.data.local.entities.Settings;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \t2\u00020\u0001:\u0001\tB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&\u00a8\u0006\n"}, d2 = {"Lcom/sirch/mileagetracker/data/local/MileageTrackerDatabase;", "Landroidx/room/RoomDatabase;", "()V", "programDao", "Lcom/sirch/mileagetracker/data/local/dao/ProgramDao;", "purchaseDao", "Lcom/sirch/mileagetracker/data/local/dao/PurchaseDao;", "settingsDao", "Lcom/sirch/mileagetracker/data/local/dao/SettingsDao;", "Companion", "app_release"})
@androidx.room.Database(entities = {com.sirch.mileagetracker.data.local.entities.Program.class, com.sirch.mileagetracker.data.local.entities.Purchase.class, com.sirch.mileagetracker.data.local.entities.Settings.class}, version = 1, exportSchema = false)
@androidx.room.TypeConverters(value = {com.sirch.mileagetracker.data.local.converters.DateConverter.class})
public abstract class MileageTrackerDatabase extends androidx.room.RoomDatabase {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DATABASE_NAME = "mileage_tracker_database_v2";
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.sirch.mileagetracker.data.local.MileageTrackerDatabase INSTANCE;
    @org.jetbrains.annotations.NotNull()
    public static final com.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion Companion = null;
    
    public MileageTrackerDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.sirch.mileagetracker.data.local.dao.ProgramDao programDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.sirch.mileagetracker.data.local.dao.PurchaseDao purchaseDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.sirch.mileagetracker.data.local.dao.SettingsDao settingsDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/sirch/mileagetracker/data/local/MileageTrackerDatabase$Companion;", "", "()V", "DATABASE_NAME", "", "INSTANCE", "Lcom/sirch/mileagetracker/data/local/MileageTrackerDatabase;", "clearInstance", "", "getDatabase", "context", "Landroid/content/Context;", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.sirch.mileagetracker.data.local.MileageTrackerDatabase getDatabase(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
        
        public final void clearInstance() {
        }
    }
}