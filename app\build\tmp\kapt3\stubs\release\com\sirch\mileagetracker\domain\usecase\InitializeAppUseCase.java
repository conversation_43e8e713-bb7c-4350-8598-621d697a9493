package com.sirch.mileagetracker.domain.usecase;

import com.sirch.mileagetracker.data.local.entities.Purchase;
import com.sirch.mileagetracker.data.repository.ProgramRepository;
import com.sirch.mileagetracker.data.repository.PurchaseRepository;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import kotlinx.datetime.LocalDate;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\nH\u0086B\u00a2\u0006\u0002\u0010\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/sirch/mileagetracker/domain/usecase/InitializeAppUseCase;", "", "programRepository", "Lcom/sirch/mileagetracker/data/repository/ProgramRepository;", "purchaseRepository", "Lcom/sirch/mileagetracker/data/repository/PurchaseRepository;", "settingsRepository", "Lcom/sirch/mileagetracker/data/repository/SettingsRepository;", "(Lcom/sirch/mileagetracker/data/repository/ProgramRepository;Lcom/sirch/mileagetracker/data/repository/PurchaseRepository;Lcom/sirch/mileagetracker/data/repository/SettingsRepository;)V", "invoke", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public final class InitializeAppUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.ProgramRepository programRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.PurchaseRepository purchaseRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository = null;
    
    @javax.inject.Inject()
    public InitializeAppUseCase(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.ProgramRepository programRepository, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.PurchaseRepository purchaseRepository, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object invoke(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}