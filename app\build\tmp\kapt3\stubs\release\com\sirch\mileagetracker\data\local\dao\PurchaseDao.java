package com.sirch.mileagetracker.data.local.dao;

import androidx.paging.PagingSource;
import androidx.room.*;
import com.sirch.mileagetracker.data.local.entities.Purchase;
import kotlinx.coroutines.flow.Flow;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\f\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0014\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\r0\fH\'J\u0014\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\fH\'J\u0014\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00050\u0011H\'J\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0015J\u0018\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0018\u0010\u0018\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0019\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010\b\u001a\u00020\tH\'J\u001c\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00050\u00112\u0006\u0010\b\u001a\u00020\tH\'J$\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00050\r2\u0006\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u001fJ\u000e\u0010 \u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0015J\u0016\u0010!\u001a\u00020\t2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\"\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006#"}, d2 = {"Lcom/sirch/mileagetracker/data/local/dao/PurchaseDao;", "", "deletePurchase", "", "purchase", "Lcom/sirch/mileagetracker/data/local/entities/Purchase;", "(Lcom/sirch/mileagetracker/data/local/entities/Purchase;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePurchasesByProgram", "programId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllProgramStatistics", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/sirch/mileagetracker/data/local/dao/ProgramStatisticsWithName;", "getAllPurchases", "getAllPurchasesPaged", "Landroidx/paging/PagingSource;", "", "getGlobalStatistics", "Lcom/sirch/mileagetracker/data/local/dao/GlobalStatistics;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getProgramStatistics", "Lcom/sirch/mileagetracker/data/local/dao/ProgramStatistics;", "getPurchaseById", "id", "getPurchasesByProgram", "getPurchasesByProgramPaged", "getPurchasesPage", "limit", "offset", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTotalPurchaseCount", "insertPurchase", "updatePurchase", "app_release"})
@androidx.room.Dao()
public abstract interface PurchaseDao {
    
    @androidx.room.Query(value = "SELECT * FROM purchases ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.sirch.mileagetracker.data.local.entities.Purchase>> getAllPurchases();
    
    @androidx.room.Query(value = "SELECT * FROM purchases ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.paging.PagingSource<java.lang.Integer, com.sirch.mileagetracker.data.local.entities.Purchase> getAllPurchasesPaged();
    
    @androidx.room.Query(value = "SELECT * FROM purchases WHERE programId = :programId ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.sirch.mileagetracker.data.local.entities.Purchase>> getPurchasesByProgram(long programId);
    
    @androidx.room.Query(value = "SELECT * FROM purchases WHERE programId = :programId ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.paging.PagingSource<java.lang.Integer, com.sirch.mileagetracker.data.local.entities.Purchase> getPurchasesByProgramPaged(long programId);
    
    @androidx.room.Query(value = "SELECT * FROM purchases ORDER BY date DESC LIMIT :limit OFFSET :offset")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPurchasesPage(int limit, int offset, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.sirch.mileagetracker.data.local.entities.Purchase>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM purchases WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPurchaseById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.local.entities.Purchase> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertPurchase(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Purchase purchase, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePurchase(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Purchase purchase, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deletePurchase(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Purchase purchase, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM purchases WHERE programId = :programId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deletePurchasesByProgram(long programId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        SELECT\n            programId,\n            SUM(cost) as totalCost,\n            SUM(miles) as totalMiles,\n            CASE\n                WHEN SUM(miles) > 0 THEN SUM(cost) / SUM(miles)\n                ELSE 0.0\n            END as averageCostPerMile\n        FROM purchases\n        WHERE programId = :programId\n        GROUP BY programId\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProgramStatistics(long programId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.local.dao.ProgramStatistics> $completion);
    
    @androidx.room.Query(value = "\n        SELECT\n            p.programId,\n            pr.name as programName,\n            SUM(p.cost) as totalCost,\n            SUM(p.miles) as totalMiles,\n            CASE\n                WHEN SUM(p.miles) > 0 THEN SUM(p.cost) / SUM(p.miles)\n                ELSE 0.0\n            END as averageCostPerMile\n        FROM purchases p\n        INNER JOIN programs pr ON p.programId = pr.id\n        WHERE pr.isActive = 1\n        GROUP BY p.programId, pr.name\n        ORDER BY pr.name\n    ")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName>> getAllProgramStatistics();
    
    @androidx.room.Query(value = "\n        SELECT\n            0 as programId,\n            SUM(p.cost) as totalCost,\n            SUM(p.miles) as totalMiles,\n            CASE\n                WHEN SUM(p.miles) > 0 THEN SUM(p.cost) / SUM(p.miles)\n                ELSE 0.0\n            END as averageCostPerMile\n        FROM purchases p\n        INNER JOIN programs pr ON p.programId = pr.id\n        WHERE pr.isActive = 1\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getGlobalStatistics(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.local.dao.GlobalStatistics> $completion);
    
    @androidx.room.Query(value = "\n        SELECT COUNT(*)\n        FROM purchases p\n        INNER JOIN programs pr ON p.programId = pr.id\n        WHERE pr.isActive = 1\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTotalPurchaseCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
}