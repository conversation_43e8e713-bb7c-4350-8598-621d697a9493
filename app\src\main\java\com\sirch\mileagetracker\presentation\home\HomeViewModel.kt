package com.sirch.mileagetracker.presentation.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName
import com.sirch.mileagetracker.data.repository.AdsRepository
import com.sirch.mileagetracker.data.repository.PurchaseRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val purchaseRepository: PurchaseRepository,
    private val adsRepository: AdsRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    init {
        loadProgramStatistics()
        loadAdsStatus()
    }

    private fun loadProgramStatistics() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            purchaseRepository.getAllProgramStatistics()
                .catch { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message
                    )
                }
                .collect { statistics ->
                    // Get global statistics more efficiently
                    val globalStats = try {
                        purchaseRepository.getGlobalStatistics()
                    } catch (e: Exception) {
                        null
                    }

                    // Use global stats if available, otherwise calculate from program stats
                    val totalMiles = globalStats?.totalMiles ?: statistics.sumOf { it.totalMiles }
                    val totalSpent = globalStats?.totalCost ?: statistics.sumOf { it.totalCost }
                    val averageCostPerMile = globalStats?.averageCostPerMile
                        ?: if (totalMiles > 0) totalSpent / totalMiles else 0.0

                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        programStatistics = statistics,
                        totalMiles = totalMiles,
                        totalSpent = totalSpent,
                        averageCostPerMile = averageCostPerMile,
                        error = null
                    )
                }
        }
    }

    private fun loadAdsStatus() {
        viewModelScope.launch {
            adsRepository.shouldShowAds()
                .catch { exception ->
                    // Default to showing ads if we can't determine status
                    _uiState.value = _uiState.value.copy(showAds = true)
                }
                .collect { showAds ->
                    _uiState.value = _uiState.value.copy(showAds = showAds)
                }
        }
    }

    fun refresh() {
        loadProgramStatistics()
        loadAdsStatus()
    }
}

data class HomeUiState(
    val isLoading: Boolean = true,
    val programStatistics: List<ProgramStatisticsWithName> = emptyList(),
    val totalMiles: Int = 0,
    val totalSpent: Double = 0.0,
    val averageCostPerMile: Double = 0.0,
    val showAds: Boolean = true,
    val error: String? = null
)
