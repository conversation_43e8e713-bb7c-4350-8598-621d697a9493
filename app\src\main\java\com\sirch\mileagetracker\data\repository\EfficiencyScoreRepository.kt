package com.sirch.mileagetracker.data.repository

import com.sirch.mileagetracker.data.auth.AuthRepository
import com.sirch.mileagetracker.data.local.dao.EfficiencyScoreDao
import com.sirch.mileagetracker.data.local.entities.EfficiencyScore
import com.sirch.mileagetracker.domain.efficiency.EfficiencyScoreEngine
import com.sirch.mileagetracker.domain.efficiency.EfficiencyScoreResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing efficiency score data.
 * Provides a clean interface between the UI layer and the efficiency score engine.
 */
@Singleton
class EfficiencyScoreRepository @Inject constructor(
    private val efficiencyScoreDao: EfficiencyScoreDao,
    private val efficiencyScoreEngine: EfficiencyScoreEngine,
    private val authRepository: AuthRepository
) {

    /**
     * Get efficiency score for a specific program
     */
    suspend fun getEfficiencyScore(programId: Long): EfficiencyScoreResult {
        return efficiencyScoreEngine.getEfficiencyScore(programId)
    }

    /**
     * Get all efficiency scores for current user
     */
    fun getAllEfficiencyScores(): Flow<List<EfficiencyScore>> {
        return efficiencyScoreEngine.getAllEfficiencyScores()
    }

    /**
     * Get efficiency scores for current user as Flow
     */
    fun getEfficiencyScoresByUser(): Flow<List<EfficiencyScore>> = flow {
        val userId = authRepository.getUserId()
        if (userId != null) {
            efficiencyScoreDao.getEfficiencyScoresByUser(userId).collect { scores ->
                emit(scores)
            }
        } else {
            emit(emptyList())
        }
    }

    /**
     * Get efficiency score for specific user and program
     */
    suspend fun getEfficiencyScore(userId: String, programId: Long): EfficiencyScore? {
        return efficiencyScoreDao.getEfficiencyScore(userId, programId)
    }

    /**
     * Force recalculation of efficiency score for a program
     */
    suspend fun recalculateEfficiencyScore(programId: Long): EfficiencyScoreResult {
        val userId = authRepository.getUserId()
            ?: return EfficiencyScoreResult.Error("User not authenticated")
        
        return efficiencyScoreEngine.calculateEfficiencyScore(
            userId = userId,
            programId = programId,
            forceRecalculation = true
        )
    }

    /**
     * Update efficiency scores after purchase changes
     */
    suspend fun updateScoresAfterPurchase(programId: Long) {
        efficiencyScoreEngine.recalculateScoresAfterPurchase(programId)
    }

    /**
     * Get efficiency score statistics for current user
     */
    suspend fun getEfficiencyScoreStatistics(): EfficiencyScoreStatistics? {
        val userId = authRepository.getUserId() ?: return null
        
        return try {
            val stats = efficiencyScoreDao.getEfficiencyScoreStatistics(userId)
            EfficiencyScoreStatistics(
                totalPrograms = stats?.totalPrograms ?: 0,
                averageScore = stats?.averageScore ?: 0.0,
                bestScore = stats?.bestScore ?: 0.0,
                worstScore = stats?.worstScore ?: 0.0,
                lastUpdated = stats?.lastUpdated
            )
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Get recent efficiency scores (updated in last 7 days)
     */
    fun getRecentEfficiencyScores(): Flow<List<EfficiencyScore>> = flow {
        val userId = authRepository.getUserId()
        if (userId != null) {
            efficiencyScoreDao.getRecentEfficiencyScores(userId).collect { scores ->
                emit(scores)
            }
        } else {
            emit(emptyList())
        }
    }

    /**
     * Get efficiency scores that need updating
     */
    suspend fun getOutdatedEfficiencyScores(): List<EfficiencyScore> {
        val userId = authRepository.getUserId() ?: return emptyList()
        return efficiencyScoreDao.getOutdatedEfficiencyScores(userId)
    }

    /**
     * Delete efficiency score for a program
     */
    suspend fun deleteEfficiencyScore(programId: Long) {
        val userId = authRepository.getUserId() ?: return
        val score = efficiencyScoreDao.getEfficiencyScore(userId, programId)
        if (score != null) {
            efficiencyScoreDao.deleteEfficiencyScore(score)
        }
    }

    /**
     * Delete all efficiency scores for current user
     */
    suspend fun deleteAllEfficiencyScores() {
        val userId = authRepository.getUserId() ?: return
        efficiencyScoreDao.deleteEfficiencyScoresByUser(userId)
    }

    /**
     * Check if user has efficiency scores
     */
    suspend fun hasEfficiencyScores(): Boolean {
        val userId = authRepository.getUserId() ?: return false
        return efficiencyScoreDao.getEfficiencyScoreCount(userId) > 0
    }

    /**
     * Get efficiency score count for current user
     */
    suspend fun getEfficiencyScoreCount(): Int {
        val userId = authRepository.getUserId() ?: return 0
        return efficiencyScoreDao.getEfficiencyScoreCount(userId)
    }

    /**
     * Get star rating for a score value
     */
    fun getStarRating(percentile: Double): Int {
        return efficiencyScoreEngine.getStarRating(percentile)
    }

    /**
     * Get efficiency rating for a score value
     */
    fun getEfficiencyRating(score: Double) = 
        efficiencyScoreEngine.getEfficiencyRating(score)

    /**
     * Enable or disable data sharing for efficiency scores
     */
    suspend fun updateDataSharingPreference(enabled: Boolean) {
        val userId = authRepository.getUserId() ?: return
        efficiencyScoreDao.updateDataSharingPreference(userId, enabled)
    }

    /**
     * Get programs with efficiency scores
     */
    suspend fun getProgramsWithEfficiencyScores(): List<Long> {
        val userId = authRepository.getUserId() ?: return emptyList()
        return efficiencyScoreDao.getProgramsWithScores(userId)
    }
}

/**
 * Statistics about user's efficiency scores
 */
data class EfficiencyScoreStatistics(
    val totalPrograms: Int,
    val averageScore: Double,
    val bestScore: Double,
    val worstScore: Double,
    val lastUpdated: kotlinx.datetime.LocalDateTime?
)
