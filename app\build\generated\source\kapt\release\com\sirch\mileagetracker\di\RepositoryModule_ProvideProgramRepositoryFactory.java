package com.sirch.mileagetracker.di;

import com.sirch.mileagetracker.data.local.dao.ProgramDao;
import com.sirch.mileagetracker.data.repository.ProgramRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideProgramRepositoryFactory implements Factory<ProgramRepository> {
  private final Provider<ProgramDao> programDaoProvider;

  public RepositoryModule_ProvideProgramRepositoryFactory(Provider<ProgramDao> programDaoProvider) {
    this.programDaoProvider = programDaoProvider;
  }

  @Override
  public ProgramRepository get() {
    return provideProgramRepository(programDaoProvider.get());
  }

  public static RepositoryModule_ProvideProgramRepositoryFactory create(
      Provider<ProgramDao> programDaoProvider) {
    return new RepositoryModule_ProvideProgramRepositoryFactory(programDaoProvider);
  }

  public static ProgramRepository provideProgramRepository(ProgramDao programDao) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideProgramRepository(programDao));
  }
}
