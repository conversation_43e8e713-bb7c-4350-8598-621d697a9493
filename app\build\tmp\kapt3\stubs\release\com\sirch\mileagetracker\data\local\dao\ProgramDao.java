package com.sirch.mileagetracker.data.local.dao;

import androidx.room.*;
import com.sirch.mileagetracker.data.local.entities.Program;
import kotlinx.coroutines.flow.Flow;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0006\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\nH\'J\u0018\u0010\f\u001a\u0004\u0018\u00010\u00072\u0006\u0010\r\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0018\u0010\u0010\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\n2\u0006\u0010\r\u001a\u00020\u000eH\'J\u000e\u0010\u0011\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0013\u001a\u00020\u000e2\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\u0014\u001a\u00020\u00032\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00070\u000bH\u00a7@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006\u0018"}, d2 = {"Lcom/sirch/mileagetracker/data/local/dao/ProgramDao;", "", "deleteAllPrograms", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteProgram", "program", "Lcom/sirch/mileagetracker/data/local/entities/Program;", "(Lcom/sirch/mileagetracker/data/local/entities/Program;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllActivePrograms", "Lkotlinx/coroutines/flow/Flow;", "", "getProgramById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getProgramByIdFlow", "getProgramCount", "", "insertProgram", "insertPrograms", "programs", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateProgram", "app_release"})
@androidx.room.Dao()
public abstract interface ProgramDao {
    
    @androidx.room.Query(value = "SELECT * FROM programs WHERE isActive = 1 ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.sirch.mileagetracker.data.local.entities.Program>> getAllActivePrograms();
    
    @androidx.room.Query(value = "SELECT * FROM programs WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProgramById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.sirch.mileagetracker.data.local.entities.Program> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM programs WHERE id = :id")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.sirch.mileagetracker.data.local.entities.Program> getProgramByIdFlow(long id);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertProgram(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Program program, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertPrograms(@org.jetbrains.annotations.NotNull()
    java.util.List<com.sirch.mileagetracker.data.local.entities.Program> programs, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateProgram(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Program program, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteProgram(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.entities.Program program, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM programs")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProgramCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "DELETE FROM programs")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllPrograms(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}