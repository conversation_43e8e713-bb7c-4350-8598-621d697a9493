package com.sirch.mileagetracker.presentation.program;

import com.sirch.mileagetracker.data.repository.ProgramRepository;
import com.sirch.mileagetracker.data.repository.PurchaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProgramDetailsViewModel_Factory implements Factory<ProgramDetailsViewModel> {
  private final Provider<ProgramRepository> programRepositoryProvider;

  private final Provider<PurchaseRepository> purchaseRepositoryProvider;

  public ProgramDetailsViewModel_Factory(Provider<ProgramRepository> programRepositoryProvider,
      Provider<PurchaseRepository> purchaseRepositoryProvider) {
    this.programRepositoryProvider = programRepositoryProvider;
    this.purchaseRepositoryProvider = purchaseRepositoryProvider;
  }

  @Override
  public ProgramDetailsViewModel get() {
    return newInstance(programRepositoryProvider.get(), purchaseRepositoryProvider.get());
  }

  public static ProgramDetailsViewModel_Factory create(
      Provider<ProgramRepository> programRepositoryProvider,
      Provider<PurchaseRepository> purchaseRepositoryProvider) {
    return new ProgramDetailsViewModel_Factory(programRepositoryProvider, purchaseRepositoryProvider);
  }

  public static ProgramDetailsViewModel newInstance(ProgramRepository programRepository,
      PurchaseRepository purchaseRepository) {
    return new ProgramDetailsViewModel(programRepository, purchaseRepository);
  }
}
