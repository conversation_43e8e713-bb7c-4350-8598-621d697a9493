package com.sirch.mileagetracker

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.*
import androidx.compose.ui.tooling.preview.Preview
import com.sirch.mileagetracker.presentation.navigation.MileageTrackerApp
import com.sirch.mileagetracker.ui.theme.MileageTrackerTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            MileageTrackerTheme {
                MileageTrackerApp()
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MileageTrackerPreview() {
    MileageTrackerTheme {
        MileageTrackerApp()
    }
}
