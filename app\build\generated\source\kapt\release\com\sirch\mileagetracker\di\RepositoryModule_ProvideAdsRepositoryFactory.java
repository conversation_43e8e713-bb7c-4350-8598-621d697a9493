package com.sirch.mileagetracker.di;

import com.sirch.mileagetracker.data.repository.AdsRepository;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideAdsRepositoryFactory implements Factory<AdsRepository> {
  private final Provider<SettingsRepository> settingsRepositoryProvider;

  public RepositoryModule_ProvideAdsRepositoryFactory(
      Provider<SettingsRepository> settingsRepositoryProvider) {
    this.settingsRepositoryProvider = settingsRepositoryProvider;
  }

  @Override
  public AdsRepository get() {
    return provideAdsRepository(settingsRepositoryProvider.get());
  }

  public static RepositoryModule_ProvideAdsRepositoryFactory create(
      Provider<SettingsRepository> settingsRepositoryProvider) {
    return new RepositoryModule_ProvideAdsRepositoryFactory(settingsRepositoryProvider);
  }

  public static AdsRepository provideAdsRepository(SettingsRepository settingsRepository) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideAdsRepository(settingsRepository));
  }
}
