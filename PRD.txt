Documento de Requisitos do Produto (PRD) - Mileage Tracker App

1. Visão Geral

Nome do Produto: Mileage Tracker

Descrição: Aplicativo mobile para acompanhar e gerenciar despesas e milhas acumuladas em programas de fidelidade de companhias aéreas. Permite registro de compras, cálculo de custo médio por milha, autenticação via Google/Firebase, e exibição de anúncios (AdMob) e compras no app (Billing).

Plataformas: Android (minSdk 33, targetSdk 35)

2. Objetivos do Produto

Ajudar usuários a monitorar gastos e milhas acumuladas em diferentes programas de fidelidade.

Fornecer insights sobre custo médio por milha, auxiliando na otimização de consumo.

Garantir experiência fluida com navegação Compose e sincronização em tempo real via Coroutines/Flow.

Monetização: anúncios intersticiais/banner (AdMob) e compras in-app (recurso premium).

3. Público-Alvo

Viajantes frequentes que participam de programas de milhagem.

Usuários que buscam controle financeiro de suas despesas de viagem.

Amantes de apps simples, com UX limpa e design moderno (Jetpack Compose).

4. Funcionalidades Principais

ID

Funcionalidade

Descrição breve

F1

Autenticação Google/Firebase

Login com conta Google, persistência via FirebaseAuth.

F2

Seed programas

Carregamento inicial de programas padrão (LATAM, Smiles...).

F3

Registro de compras

CRUD de compras: data, programa, milhas, custo.

F4

Lista de compras

Exibição ordenada por data (Compose + Flow).

F5

Cálculo custo médio por milha

Consulta com JOIN e AVG no Room.

F6

Anúncios AdMob

Banner/intersticial configurável via AdMob.

F7

Compras in-app (Billing)

Desbloqueio de recursos premium.

F8

Monitoramento de estados (Hilt)

Injeção de dependências com Hilt + ViewModels.

5. Requisitos Funcionais

RF01: O usuário deve se autenticar via conta Google.

RF02: O app deve criar registro de programas padrão na primeira execução.

RF03: O usuário deve adicionar, editar e excluir compras.

RF04: Deve ser possível filtrar compras por programa.

RF05: Exibir custo médio por milha para cada programa.

RF06: Exibir anúncios de banner na tela principal.

RF07: Ofertar SKU de compra in-app para remover anúncios.

6. Requisitos Não Funcionais

RNF01: Tempo de resposta de consultas < 200ms.

RNF02: Alta disponibilidade offline (dados locais via Room).

RNF03: Suporte a telas diferentes (responsividade Compose).

RNF04: Segurança de dados sensíveis (FirebaseAuth + Room criptografado opcional).

RNF05: Baixa utilização de bateria (Coroutines bem otimizadas).

7. Fluxo de Usuário

Splash: Seed + verificação de login.

Login: Autenticação Google.

Home: Lista de programas e resumo de custo médio.

Detalhes do Programa: Compras associadas.

Formulário de Compra: CRUD.

Configurações: Remover anúncios (IAP).

8. Integrações Técnicas

Room: entidades Program, Purchase, Settings.

Hilt: módulos para repositórios, DAOs, FirebaseAuth.

Firebase: Auth + Analytics.

AdMob: banners e intersticiais.

Billing: implementações de BillingClient.