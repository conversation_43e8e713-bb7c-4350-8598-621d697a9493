package com.sirch.mileagetracker.data.local.dao

import androidx.paging.PagingSource
import androidx.room.*
import com.sirch.mileagetracker.data.local.entities.Purchase
import kotlinx.coroutines.flow.Flow

@Dao
interface PurchaseDao {

    @Query("SELECT * FROM purchases ORDER BY date DESC")
    fun getAllPurchases(): Flow<List<Purchase>>

    @Query("SELECT * FROM purchases ORDER BY date DESC")
    fun getAllPurchasesPaged(): PagingSource<Int, Purchase>

    @Query("SELECT * FROM purchases WHERE programId = :programId ORDER BY date DESC")
    fun getPurchasesByProgram(programId: Long): Flow<List<Purchase>>

    @Query("SELECT * FROM purchases WHERE programId = :programId ORDER BY date DESC")
    fun getPurchasesByProgramPaged(programId: Long): PagingSource<Int, Purchase>

    @Query("SELECT * FROM purchases ORDER BY date DESC LIMIT :limit OFFSET :offset")
    suspend fun getPurchasesPage(limit: Int, offset: Int): List<Purchase>

    @Query("SELECT * FROM purchases WHERE id = :id")
    suspend fun getPurchaseById(id: Long): Purchase?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPurchase(purchase: Purchase): Long

    @Update
    suspend fun updatePurchase(purchase: Purchase)

    @Delete
    suspend fun deletePurchase(purchase: Purchase)

    @Query("DELETE FROM purchases WHERE programId = :programId")
    suspend fun deletePurchasesByProgram(programId: Long)

    @Query("""
        SELECT
            programId,
            SUM(cost) as totalCost,
            SUM(miles) as totalMiles,
            CASE
                WHEN SUM(miles) > 0 THEN SUM(cost) / SUM(miles)
                ELSE 0.0
            END as averageCostPerMile
        FROM purchases
        WHERE programId = :programId
        GROUP BY programId
    """)
    suspend fun getProgramStatistics(programId: Long): ProgramStatistics?

    @Query("""
        SELECT
            p.programId,
            pr.name as programName,
            SUM(p.cost) as totalCost,
            SUM(p.miles) as totalMiles,
            CASE
                WHEN SUM(p.miles) > 0 THEN SUM(p.cost) / SUM(p.miles)
                ELSE 0.0
            END as averageCostPerMile
        FROM purchases p
        INNER JOIN programs pr ON p.programId = pr.id
        WHERE pr.isActive = 1
        GROUP BY p.programId, pr.name
        ORDER BY pr.name
    """)
    fun getAllProgramStatistics(): Flow<List<ProgramStatisticsWithName>>

    @Query("""
        SELECT
            0 as programId,
            SUM(p.cost) as totalCost,
            SUM(p.miles) as totalMiles,
            CASE
                WHEN SUM(p.miles) > 0 THEN SUM(p.cost) / SUM(p.miles)
                ELSE 0.0
            END as averageCostPerMile
        FROM purchases p
        INNER JOIN programs pr ON p.programId = pr.id
        WHERE pr.isActive = 1
    """)
    suspend fun getGlobalStatistics(): GlobalStatistics?

    @Query("""
        SELECT COUNT(*)
        FROM purchases p
        INNER JOIN programs pr ON p.programId = pr.id
        WHERE pr.isActive = 1
    """)
    suspend fun getTotalPurchaseCount(): Int
}

data class ProgramStatistics(
    val programId: Long,
    val totalCost: Double,
    val totalMiles: Int,
    val averageCostPerMile: Double
)

data class ProgramStatisticsWithName(
    val programId: Long,
    val programName: String,
    val totalCost: Double,
    val totalMiles: Int,
    val averageCostPerMile: Double
)

data class GlobalStatistics(
    val programId: Long, // Always 0 for global stats
    val totalCost: Double,
    val totalMiles: Int,
    val averageCostPerMile: Double
)
