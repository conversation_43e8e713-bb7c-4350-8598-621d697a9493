package com.sirch.mileagetracker.data.repository

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.sirch.mileagetracker.data.local.dao.GlobalStatistics
import com.sirch.mileagetracker.data.local.dao.ProgramStatistics
import com.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName
import com.sirch.mileagetracker.data.local.dao.PurchaseDao
import com.sirch.mileagetracker.data.local.entities.Purchase
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PurchaseRepository @Inject constructor(
    private val purchaseDao: PurchaseDao
) {

    fun getAllPurchases(): Flow<List<Purchase>> {
        return purchaseDao.getAllPurchases()
    }

    fun getAllPurchasesPaged(): Flow<PagingData<Purchase>> {
        return Pager(
            config = PagingConfig(
                pageSize = 20,
                enablePlaceholders = false,
                prefetchDistance = 5
            ),
            pagingSourceFactory = { purchaseDao.getAllPurchasesPaged() }
        ).flow
    }

    fun getPurchasesByProgram(programId: Long): Flow<List<Purchase>> {
        return purchaseDao.getPurchasesByProgram(programId)
    }

    fun getPurchasesByProgramPaged(programId: Long): Flow<PagingData<Purchase>> {
        return Pager(
            config = PagingConfig(
                pageSize = 20,
                enablePlaceholders = false,
                prefetchDistance = 5
            ),
            pagingSourceFactory = { purchaseDao.getPurchasesByProgramPaged(programId) }
        ).flow
    }

    suspend fun getPurchaseById(id: Long): Purchase? {
        return purchaseDao.getPurchaseById(id)
    }

    suspend fun insertPurchase(purchase: Purchase): Long {
        return purchaseDao.insertPurchase(purchase)
    }

    suspend fun updatePurchase(purchase: Purchase) {
        purchaseDao.updatePurchase(purchase)
    }

    suspend fun deletePurchase(purchase: Purchase) {
        purchaseDao.deletePurchase(purchase)
    }

    suspend fun deletePurchasesByProgram(programId: Long) {
        purchaseDao.deletePurchasesByProgram(programId)
    }

    suspend fun getProgramStatistics(programId: Long): ProgramStatistics? {
        return purchaseDao.getProgramStatistics(programId)
    }

    fun getAllProgramStatistics(): Flow<List<ProgramStatisticsWithName>> {
        return purchaseDao.getAllProgramStatistics()
    }

    suspend fun getGlobalStatistics(): GlobalStatistics? {
        return purchaseDao.getGlobalStatistics()
    }

    suspend fun getTotalPurchaseCount(): Int {
        return purchaseDao.getTotalPurchaseCount()
    }
}
