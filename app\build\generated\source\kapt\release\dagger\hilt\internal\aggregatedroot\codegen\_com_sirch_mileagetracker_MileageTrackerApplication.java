package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.sirch.mileagetracker.MileageTrackerApplication",
    rootPackage = "com.sirch.mileagetracker",
    originatingRoot = "com.sirch.mileagetracker.MileageTrackerApplication",
    originatingRootPackage = "com.sirch.mileagetracker",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "MileageTrackerApplication",
    originatingRootSimpleNames = "MileageTrackerApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_sirch_mileagetracker_MileageTrackerApplication {
}
