# 🛫 Mileage Tracker

Um aplicativo Android moderno para rastrear milhas e maximizar benefícios de programas de fidelidade.

## 📱 Sobre o App

O Mileage Tracker permite que você:
- 📊 Acompanhe múltiplos programas de milhas
- 💳 Registre compras e ganhos automáticos  
- 📈 Visualize estatísticas detalhadas de acúmulo
- 🌙 Use modo escuro elegante
- 📱 Desfrute de interface moderna e intuitiva

## 🏗️ Arquitetura

### Tecnologias Utilizadas
- **Kotlin** - Linguagem principal
- **Jetpack Compose** - UI moderna e declarativa
- **Room Database** - Persistência local
- **Hilt** - Injeção de dependência
- **Coroutines** - Programação assíncrona
- **MVVM** - Padrão arquitetural
- **Material Design 3** - Design system

### Estrutura do Projeto
```
app/
├── src/main/java/com/sirch/mileagetracker/
│   ├── data/           # Camada de dados
│   │   ├── local/      # Database Room
│   │   └── repository/ # Repositórios
│   ├── domain/         # Regras de negócio
│   ├── presentation/   # UI (Compose)
│   │   ├── screens/    # Telas
│   │   ├── components/ # Componentes reutilizáveis
│   │   └── theme/      # Tema e cores
│   └── utils/          # Utilitários
```

## 🚀 Como Executar

### Pré-requisitos
- Android Studio Arctic Fox ou superior
- JDK 11+
- Android SDK 33+

### Passos
1. **Clone o repositório**
   ```bash
   git clone <repository-url>
   cd MileageTracker
   ```

2. **Abra no Android Studio**
   - File → Open → Selecione a pasta do projeto

3. **Sync do projeto**
   - Android Studio fará sync automático
   - Aguarde download das dependências

4. **Execute o app**
   - Conecte um dispositivo ou inicie emulador
   - Clique em Run (▶️)

## 🔧 Configuração para Produção

### 1. Firebase (Opcional)
- Substitua `google-services.json` pelo seu
- Configure Authentication e Analytics

### 2. AdMob (Opcional)  
- Atualize o AdMob ID em `gma_ad_services_config.xml`
- Configure suas unidades de anúncio

### 3. Assinatura
- Siga o guia em `DEPLOY_GUIDE.md`
- Configure keystore para release

## 📦 Build de Produção

### Automático (Recomendado)
```bash
# Windows
build-release.bat

# Linux/Mac
./build-release.sh
```

### Manual
```bash
# Limpar projeto
./gradlew clean

# Gerar APK
./gradlew assembleRelease

# Gerar AAB (recomendado para Play Store)
./gradlew bundleRelease
```

## 🧪 Testes

### Executar Testes
```bash
# Testes unitários
./gradlew test

# Testes instrumentados
./gradlew connectedAndroidTest
```

### Cobertura de Testes
- Unit tests para ViewModels
- Integration tests para Repository
- UI tests para telas principais

## 📊 Performance

### Otimizações Implementadas
- ✅ **Compose**: Keys estáveis, lambdas otimizados
- ✅ **Room**: Queries eficientes, índices planejados
- ✅ **Memory**: Stable classes, minimal recomposition
- ✅ **ProGuard**: Minificação e ofuscação habilitadas

### Métricas
- **Startup time**: < 2s cold start
- **Memory usage**: < 50MB typical
- **APK size**: < 10MB (com ProGuard)

## 🔐 Segurança

### Implementado
- ✅ ProGuard rules otimizadas
- ✅ Keystore protection
- ✅ Sensitive data exclusion
- ✅ Network security config

### Dados Protegidos
- Keystores não commitados
- Senhas em arquivos locais
- Configurações sensíveis ignoradas

## 📱 Compatibilidade

### Versões Android
- **Mínima**: API 33 (Android 13)
- **Target**: API 35 (Android 15)
- **Compile**: API 35

### Dispositivos
- ✅ Phones (todos os tamanhos)
- ✅ Tablets (layout responsivo)
- ✅ Foldables (adaptive layout)

## 🎨 Design

### Tema
- **Material Design 3** com cores personalizadas
- **Dynamic Color** support (Android 12+)
- **Dark/Light mode** automático

### Cores Principais
- **Primary**: #2196F3 (Azul)
- **Secondary**: #FFC107 (Dourado)
- **Surface**: Adaptive

## 📄 Licença

Este projeto está sob licença MIT. Veja `LICENSE` para detalhes.

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch (`git checkout -b feature/nova-feature`)
3. Commit suas mudanças (`git commit -m 'Add nova feature'`)
4. Push para a branch (`git push origin feature/nova-feature`)
5. Abra um Pull Request

## 📞 Suporte

Para dúvidas ou problemas:
- 📧 Email: [<EMAIL>]
- 🐛 Issues: [GitHub Issues]
- 📖 Documentação: `DEPLOY_GUIDE.md`

## 🎯 Roadmap

### Próximas Features
- [ ] Sincronização na nuvem
- [ ] Notificações push
- [ ] Widget para home screen
- [ ] Export/import de dados
- [ ] Múltiplas moedas

### Melhorias Técnicas
- [ ] Paginação avançada
- [ ] Cache inteligente
- [ ] Offline-first architecture
- [ ] Performance monitoring

---

**Desenvolvido com ❤️ usando as melhores práticas Android**

🚀 **Pronto para produção!**
