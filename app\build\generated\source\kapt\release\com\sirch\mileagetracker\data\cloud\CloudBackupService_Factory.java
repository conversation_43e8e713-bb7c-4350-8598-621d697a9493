package com.sirch.mileagetracker.data.cloud;

import com.google.firebase.auth.FirebaseAuth;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CloudBackupService_Factory implements Factory<CloudBackupService> {
  private final Provider<FirebaseAuth> firebaseAuthProvider;

  private final Provider<SettingsRepository> settingsRepositoryProvider;

  public CloudBackupService_Factory(Provider<FirebaseAuth> firebaseAuthProvider,
      Provider<SettingsRepository> settingsRepositoryProvider) {
    this.firebaseAuthProvider = firebaseAuthProvider;
    this.settingsRepositoryProvider = settingsRepositoryProvider;
  }

  @Override
  public CloudBackupService get() {
    return newInstance(firebaseAuthProvider.get(), settingsRepositoryProvider.get());
  }

  public static CloudBackupService_Factory create(Provider<FirebaseAuth> firebaseAuthProvider,
      Provider<SettingsRepository> settingsRepositoryProvider) {
    return new CloudBackupService_Factory(firebaseAuthProvider, settingsRepositoryProvider);
  }

  public static CloudBackupService newInstance(FirebaseAuth firebaseAuth,
      SettingsRepository settingsRepository) {
    return new CloudBackupService(firebaseAuth, settingsRepository);
  }
}
