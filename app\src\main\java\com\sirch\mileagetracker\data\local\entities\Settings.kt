package com.sirch.mileagetracker.data.local.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "settings")
data class Settings(
    @PrimaryKey
    val id: Int = 1, // Single row table
    val adsRemoved: Boolean = false,
    val userId: String? = null,
    val lastSyncTime: Long? = null,
    val cloudBackupEnabled: Boolean = false,
    val lastBackupTimestamp: Long = 0L
)
