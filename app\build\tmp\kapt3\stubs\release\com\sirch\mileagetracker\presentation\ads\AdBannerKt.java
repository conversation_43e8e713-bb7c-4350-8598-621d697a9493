package com.sirch.mileagetracker.presentation.ads;

import androidx.compose.runtime.*;
import androidx.compose.ui.Modifier;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdSize;
import com.google.android.gms.ads.AdView;
import com.sirch.mileagetracker.BuildConfig;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.admanager.AdManagerAdRequest;
import com.google.android.gms.ads.admanager.AdManagerAdView;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00004\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a.\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001ad\u0010\n\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\f2\u0014\b\u0002\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00010\u000e2\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\fH\u0007\u00a8\u0006\u0011"}, d2 = {"AdBanner", "", "adUnitId", "", "modifier", "Landroidx/compose/ui/Modifier;", "adSize", "Lcom/google/android/gms/ads/AdSize;", "showAds", "", "AdBannerWithCallback", "onAdLoaded", "Lkotlin/Function0;", "onAdFailedToLoad", "Lkotlin/Function1;", "Lcom/google/android/gms/ads/LoadAdError;", "onAdClicked", "app_release"})
public final class AdBannerKt {
    
    @androidx.compose.runtime.Composable()
    public static final void AdBanner(@org.jetbrains.annotations.NotNull()
    java.lang.String adUnitId, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.google.android.gms.ads.AdSize adSize, boolean showAds) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AdBannerWithCallback(@org.jetbrains.annotations.NotNull()
    java.lang.String adUnitId, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.google.android.gms.ads.AdSize adSize, boolean showAds, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAdLoaded, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.google.android.gms.ads.LoadAdError, kotlin.Unit> onAdFailedToLoad, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAdClicked) {
    }
}