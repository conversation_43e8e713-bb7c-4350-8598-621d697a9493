{"Animal Sniffer": {"length": 1096, "start": 19}, "Checker Framework Annotations": {"length": 1892, "start": 1149}, "Dagger": {"length": 11358, "start": 3052}, "Error Prone": {"length": 11357, "start": 14426}, "Firebase": {"length": 11357, "start": 25796}, "Google Auto": {"length": 11358, "start": 37169}, "Guava JDK5": {"length": 11358, "start": 48542}, "Guava JDK7": {"length": 11362, "start": 59915}, "HighwayHash": {"length": 11359, "start": 71293}, "J2ObjC": {"length": 81008, "start": 82663}, "JSR 250": {"length": 11358, "start": 163683}, "JSR 305": {"length": 1602, "start": 175053}, "JSR 330": {"length": 11365, "start": 176667}, "JSpecify": {"length": 11358, "start": 188045}, "Jakarta Inject": {"length": 11357, "start": 199422}, "JsInterop Annotations": {"length": 11307, "start": 210805}, "Kotlin": {"length": 112095, "start": 222123}, "Kotlin coroutines": {"length": 11357, "start": 334240}, "Protocol Buffers for Java": {"length": 1732, "start": 345627}, "SafeParcelable library": {"length": 11358, "start": 347386}, "apksig": {"length": 10695, "start": 358755}, "checker_framework_util": {"length": 1036, "start": 369477}, "gsfclient": {"length": 11358, "start": 370527}, "java_annotations": {"length": 9116, "start": 381906}, "kotlin": {"length": 1732, "start": 391033}, "kotlinx_atomicfu": {"length": 11356, "start": 392786}}