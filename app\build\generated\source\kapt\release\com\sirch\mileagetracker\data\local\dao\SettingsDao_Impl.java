package com.sirch.mileagetracker.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.sirch.mileagetracker.data.local.entities.Settings;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class SettingsDao_Impl implements SettingsDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Settings> __insertionAdapterOfSettings;

  private final EntityDeletionOrUpdateAdapter<Settings> __updateAdapterOfSettings;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAdsRemoved;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUserId;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLastSyncTime;

  public SettingsDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfSettings = new EntityInsertionAdapter<Settings>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `settings` (`id`,`adsRemoved`,`userId`,`lastSyncTime`,`darkModeEnabled`,`cloudBackupEnabled`,`lastBackupTimestamp`) VALUES (?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Settings entity) {
        statement.bindLong(1, entity.getId());
        final int _tmp = entity.getAdsRemoved() ? 1 : 0;
        statement.bindLong(2, _tmp);
        if (entity.getUserId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getUserId());
        }
        if (entity.getLastSyncTime() == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, entity.getLastSyncTime());
        }
        final int _tmp_1 = entity.getDarkModeEnabled() ? 1 : 0;
        statement.bindLong(5, _tmp_1);
        final int _tmp_2 = entity.getCloudBackupEnabled() ? 1 : 0;
        statement.bindLong(6, _tmp_2);
        statement.bindLong(7, entity.getLastBackupTimestamp());
      }
    };
    this.__updateAdapterOfSettings = new EntityDeletionOrUpdateAdapter<Settings>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `settings` SET `id` = ?,`adsRemoved` = ?,`userId` = ?,`lastSyncTime` = ?,`darkModeEnabled` = ?,`cloudBackupEnabled` = ?,`lastBackupTimestamp` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Settings entity) {
        statement.bindLong(1, entity.getId());
        final int _tmp = entity.getAdsRemoved() ? 1 : 0;
        statement.bindLong(2, _tmp);
        if (entity.getUserId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getUserId());
        }
        if (entity.getLastSyncTime() == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, entity.getLastSyncTime());
        }
        final int _tmp_1 = entity.getDarkModeEnabled() ? 1 : 0;
        statement.bindLong(5, _tmp_1);
        final int _tmp_2 = entity.getCloudBackupEnabled() ? 1 : 0;
        statement.bindLong(6, _tmp_2);
        statement.bindLong(7, entity.getLastBackupTimestamp());
        statement.bindLong(8, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateAdsRemoved = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET adsRemoved = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUserId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET userId = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLastSyncTime = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE settings SET lastSyncTime = ? WHERE id = 1";
        return _query;
      }
    };
  }

  @Override
  public Object insertSettings(final Settings settings,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfSettings.insert(settings);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSettings(final Settings settings,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfSettings.handle(settings);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAdsRemoved(final boolean adsRemoved,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAdsRemoved.acquire();
        int _argIndex = 1;
        final int _tmp = adsRemoved ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAdsRemoved.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUserId(final String userId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUserId.acquire();
        int _argIndex = 1;
        if (userId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, userId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateUserId.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLastSyncTime(final long timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLastSyncTime.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLastSyncTime.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<Settings> getSettings() {
    final String _sql = "SELECT * FROM settings WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"settings"}, new Callable<Settings>() {
      @Override
      @Nullable
      public Settings call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAdsRemoved = CursorUtil.getColumnIndexOrThrow(_cursor, "adsRemoved");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfLastSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSyncTime");
          final int _cursorIndexOfDarkModeEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "darkModeEnabled");
          final int _cursorIndexOfCloudBackupEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudBackupEnabled");
          final int _cursorIndexOfLastBackupTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "lastBackupTimestamp");
          final Settings _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final boolean _tmpAdsRemoved;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfAdsRemoved);
            _tmpAdsRemoved = _tmp != 0;
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final Long _tmpLastSyncTime;
            if (_cursor.isNull(_cursorIndexOfLastSyncTime)) {
              _tmpLastSyncTime = null;
            } else {
              _tmpLastSyncTime = _cursor.getLong(_cursorIndexOfLastSyncTime);
            }
            final boolean _tmpDarkModeEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfDarkModeEnabled);
            _tmpDarkModeEnabled = _tmp_1 != 0;
            final boolean _tmpCloudBackupEnabled;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfCloudBackupEnabled);
            _tmpCloudBackupEnabled = _tmp_2 != 0;
            final long _tmpLastBackupTimestamp;
            _tmpLastBackupTimestamp = _cursor.getLong(_cursorIndexOfLastBackupTimestamp);
            _result = new Settings(_tmpId,_tmpAdsRemoved,_tmpUserId,_tmpLastSyncTime,_tmpDarkModeEnabled,_tmpCloudBackupEnabled,_tmpLastBackupTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSettingsOnce(final Continuation<? super Settings> $completion) {
    final String _sql = "SELECT * FROM settings WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Settings>() {
      @Override
      @Nullable
      public Settings call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAdsRemoved = CursorUtil.getColumnIndexOrThrow(_cursor, "adsRemoved");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfLastSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSyncTime");
          final int _cursorIndexOfDarkModeEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "darkModeEnabled");
          final int _cursorIndexOfCloudBackupEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "cloudBackupEnabled");
          final int _cursorIndexOfLastBackupTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "lastBackupTimestamp");
          final Settings _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final boolean _tmpAdsRemoved;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfAdsRemoved);
            _tmpAdsRemoved = _tmp != 0;
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final Long _tmpLastSyncTime;
            if (_cursor.isNull(_cursorIndexOfLastSyncTime)) {
              _tmpLastSyncTime = null;
            } else {
              _tmpLastSyncTime = _cursor.getLong(_cursorIndexOfLastSyncTime);
            }
            final boolean _tmpDarkModeEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfDarkModeEnabled);
            _tmpDarkModeEnabled = _tmp_1 != 0;
            final boolean _tmpCloudBackupEnabled;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfCloudBackupEnabled);
            _tmpCloudBackupEnabled = _tmp_2 != 0;
            final long _tmpLastBackupTimestamp;
            _tmpLastBackupTimestamp = _cursor.getLong(_cursorIndexOfLastBackupTimestamp);
            _result = new Settings(_tmpId,_tmpAdsRemoved,_tmpUserId,_tmpLastSyncTime,_tmpDarkModeEnabled,_tmpCloudBackupEnabled,_tmpLastBackupTimestamp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
