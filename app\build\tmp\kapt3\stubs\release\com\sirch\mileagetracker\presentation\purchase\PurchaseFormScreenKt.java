package com.sirch.mileagetracker.presentation.purchase;

import androidx.compose.foundation.layout.*;
import androidx.compose.foundation.text.KeyboardOptions;
import androidx.compose.material.icons.Icons;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.text.input.KeyboardType;
import com.sirch.mileagetracker.R;
import com.sirch.mileagetracker.data.local.entities.Program;
import kotlinx.datetime.LocalDate;
import kotlinx.datetime.Instant;
import kotlinx.datetime.TimeZone;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000F\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a0\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001aH\u0010\b\u001a\u00020\u00012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\b\u0010\f\u001a\u0004\u0018\u00010\u000b2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001aG\u0010\u0010\u001a\u00020\u00012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00122\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00122\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u00152\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\u0016\u001a\u00020\u0017H\u0007\u00a2\u0006\u0002\u0010\u0018\u00a8\u0006\u0019"}, d2 = {"DateField", "", "date", "Lkotlinx/datetime/LocalDate;", "onDateSelected", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "ProgramDropdown", "programs", "", "Lcom/sirch/mileagetracker/data/local/entities/Program;", "selectedProgram", "onProgramSelected", "enabled", "", "PurchaseFormScreen", "programId", "", "purchaseId", "onNavigateBack", "Lkotlin/Function0;", "viewModel", "Lcom/sirch/mileagetracker/presentation/purchase/PurchaseFormViewModel;", "(Ljava/lang/Long;Ljava/lang/Long;Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/Modifier;Lcom/sirch/mileagetracker/presentation/purchase/PurchaseFormViewModel;)V", "app_release"})
public final class PurchaseFormScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void PurchaseFormScreen(@org.jetbrains.annotations.Nullable()
    java.lang.Long programId, @org.jetbrains.annotations.Nullable()
    java.lang.Long purchaseId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel viewModel) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ProgramDropdown(@org.jetbrains.annotations.NotNull()
    java.util.List<com.sirch.mileagetracker.data.local.entities.Program> programs, @org.jetbrains.annotations.Nullable()
    com.sirch.mileagetracker.data.local.entities.Program selectedProgram, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.sirch.mileagetracker.data.local.entities.Program, kotlin.Unit> onProgramSelected, boolean enabled, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void DateField(@org.jetbrains.annotations.Nullable()
    kotlinx.datetime.LocalDate date, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super kotlinx.datetime.LocalDate, kotlin.Unit> onDateSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}