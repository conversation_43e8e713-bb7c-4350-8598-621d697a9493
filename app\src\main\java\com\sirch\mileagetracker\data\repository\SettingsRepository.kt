package com.sirch.mileagetracker.data.repository

import com.sirch.mileagetracker.data.local.dao.SettingsDao
import com.sirch.mileagetracker.data.local.entities.Settings
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SettingsRepository @Inject constructor(
    private val settingsDao: SettingsDao
) {

    fun getSettings(): Flow<Settings?> {
        return settingsDao.getSettings()
    }

    suspend fun getSettingsOnce(): Settings? {
        return settingsDao.getSettingsOnce()
    }

    suspend fun insertSettings(settings: Settings) {
        settingsDao.insertSettings(settings)
    }

    suspend fun updateSettings(settings: Settings) {
        settingsDao.updateSettings(settings)
    }

    suspend fun updateAdsRemoved(adsRemoved: Boolean) {
        settingsDao.updateAdsRemoved(adsRemoved)
    }

    suspend fun updateUserId(userId: String?) {
        settingsDao.updateUserId(userId)
    }

    suspend fun updateLastSyncTime(timestamp: Long) {
        settingsDao.updateLastSyncTime(timestamp)
    }

    suspend fun initializeSettings() {
        val existingSettings = getSettingsOnce()
        if (existingSettings == null) {
            insertSettings(Settings())
        }
    }

    suspend fun getCurrentSettings(): Settings {
        return getSettingsOnce() ?: Settings(adsRemoved = false)
    }

    suspend fun updateDarkMode(enabled: Boolean) {
        val currentSettings = getSettingsOnce() ?: Settings()
        updateSettings(currentSettings.copy(darkModeEnabled = enabled))
    }

    suspend fun updateCloudBackup(enabled: Boolean) {
        val currentSettings = getSettingsOnce() ?: Settings()
        updateSettings(currentSettings.copy(cloudBackupEnabled = enabled))
    }

    suspend fun updateLastBackupTimestamp(timestamp: Long) {
        val currentSettings = getSettingsOnce() ?: Settings()
        updateSettings(currentSettings.copy(lastBackupTimestamp = timestamp))
    }
}
