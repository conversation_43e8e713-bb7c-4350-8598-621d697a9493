androidx.activity.compose.ActivityComposeUtilsKt
androidx.activity.compose.ActivityResultLauncherHolder
androidx.activity.compose.ActivityResultRegistryKt
androidx.activity.compose.ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1
androidx.activity.compose.ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1$invoke$$inlined$onDispose$1
androidx.activity.compose.ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1
androidx.activity.compose.BackHandlerKt
androidx.activity.compose.BackHandlerKt$BackHandler$1$1
androidx.activity.compose.BackHandlerKt$BackHandler$2$1
androidx.activity.compose.BackHandlerKt$BackHandler$2$1$invoke$$inlined$onDispose$1
androidx.activity.compose.BackHandlerKt$BackHandler$3
androidx.activity.compose.BackHandlerKt$BackHandler$backCallback$1$1
androidx.activity.compose.ComponentActivityKt
androidx.activity.compose.LocalActivityResultRegistryOwner
androidx.activity.compose.LocalActivityResultRegistryOwner$LocalComposition$1
androidx.activity.compose.LocalFullyDrawnReporterOwner
androidx.activity.compose.LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1
androidx.activity.compose.LocalOnBackPressedDispatcherOwner
androidx.activity.compose.LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1
androidx.activity.compose.ManagedActivityResultLauncher
androidx.activity.compose.OnBackInstance
androidx.activity.compose.OnBackInstance$job$1
androidx.activity.compose.OnBackInstance$job$1$1
androidx.activity.compose.PredictiveBackHandlerCallback
androidx.activity.compose.PredictiveBackHandlerKt
androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$2$1
androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$3$1
androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$3$1$invoke$$inlined$onDispose$1
androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$4
androidx.activity.compose.ReportDrawnComposition
androidx.activity.compose.ReportDrawnComposition$checkReporter$1
androidx.activity.compose.ReportDrawnComposition$observeReporter$1
androidx.activity.compose.ReportDrawnComposition$snapshotStateObserver$1
androidx.activity.compose.ReportDrawnKt
androidx.activity.compose.ReportDrawnKt$ReportDrawn$1
androidx.activity.compose.ReportDrawnKt$ReportDrawn$2
androidx.activity.compose.ReportDrawnKt$ReportDrawnAfter$1$1
androidx.activity.compose.ReportDrawnKt$ReportDrawnAfter$2
androidx.activity.compose.ReportDrawnKt$ReportDrawnAfter$fullyDrawnReporter$1
androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$1$1
androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$1
androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$2
androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$2
androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$fullyDrawnReporter$1