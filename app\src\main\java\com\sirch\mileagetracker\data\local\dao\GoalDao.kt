package com.sirch.mileagetracker.data.local.dao

import androidx.room.*
import com.sirch.mileagetracker.data.local.entities.Goal
import com.sirch.mileagetracker.data.local.entities.GoalType
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate

/**
 * Data Access Object for Goal entity.
 * Provides queries for managing user goals and objectives.
 */
@Dao
interface GoalDao {
    
    /**
     * Get all goals for a specific user
     */
    @Query("SELECT * FROM goals WHERE userId = :userId ORDER BY createdAt DESC")
    fun getGoalsByUser(userId: String): Flow<List<Goal>>
    
    /**
     * Get active goals for a specific user
     */
    @Query("SELECT * FROM goals WHERE userId = :userId AND isActive = 1 ORDER BY targetDate ASC")
    fun getActiveGoalsByUser(userId: String): Flow<List<Goal>>
    
    /**
     * Get goal by ID
     */
    @Query("SELECT * FROM goals WHERE id = :goalId")
    suspend fun getGoalById(goalId: Long): Goal?
    
    /**
     * Get goals for a specific program
     */
    @Query("SELECT * FROM goals WHERE userId = :userId AND programId = :programId ORDER BY createdAt DESC")
    fun getGoalsByProgram(userId: String, programId: Long): Flow<List<Goal>>
    
    /**
     * Get goals by type
     */
    @Query("SELECT * FROM goals WHERE userId = :userId AND goalType = :goalType ORDER BY createdAt DESC")
    fun getGoalsByType(userId: String, goalType: GoalType): Flow<List<Goal>>
    
    /**
     * Get completed goals
     */
    @Query("SELECT * FROM goals WHERE userId = :userId AND completedAt IS NOT NULL ORDER BY completedAt DESC")
    fun getCompletedGoals(userId: String): Flow<List<Goal>>
    
    /**
     * Get overdue goals
     */
    @Query("SELECT * FROM goals WHERE userId = :userId AND isActive = 1 AND targetDate < :currentDate AND completedAt IS NULL")
    suspend fun getOverdueGoals(userId: String, currentDate: LocalDate): List<Goal>
    
    /**
     * Get goals due soon (within specified days)
     */
    @Query("SELECT * FROM goals WHERE userId = :userId AND isActive = 1 AND targetDate BETWEEN :currentDate AND :dueSoonDate AND completedAt IS NULL")
    suspend fun getGoalsDueSoon(userId: String, currentDate: LocalDate, dueSoonDate: LocalDate): List<Goal>
    
    /**
     * Get premium goals for a user
     */
    @Query("SELECT * FROM goals WHERE userId = :userId AND isPremium = 1 ORDER BY createdAt DESC")
    fun getPremiumGoals(userId: String): Flow<List<Goal>>
    
    /**
     * Get goal statistics for a user
     */
    @Query("""
        SELECT 
            COUNT(*) as totalGoals,
            COUNT(CASE WHEN completedAt IS NOT NULL THEN 1 END) as completedGoals,
            COUNT(CASE WHEN isActive = 1 THEN 1 END) as activeGoals,
            COUNT(CASE WHEN isActive = 1 AND targetDate < :currentDate AND completedAt IS NULL THEN 1 END) as overdueGoals
        FROM goals 
        WHERE userId = :userId
    """)
    suspend fun getGoalStatistics(userId: String, currentDate: LocalDate): GoalStatistics?
    
    /**
     * Get goals progress summary
     */
    @Query("""
        SELECT 
            goalType,
            COUNT(*) as totalGoals,
            AVG(CASE 
                WHEN goalType = 'MILES' AND targetMiles > 0 THEN (currentMiles * 100.0 / targetMiles)
                WHEN goalType != 'MILES' AND targetValue > 0 AND currentValue IS NOT NULL THEN (currentValue * 100.0 / targetValue)
                ELSE 0 
            END) as avgProgress
        FROM goals 
        WHERE userId = :userId AND isActive = 1
        GROUP BY goalType
    """)
    suspend fun getGoalsProgressSummary(userId: String): List<GoalProgressSummary>
    
    /**
     * Insert goal
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGoal(goal: Goal): Long
    
    /**
     * Insert multiple goals
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGoals(goals: List<Goal>)
    
    /**
     * Update goal
     */
    @Update
    suspend fun updateGoal(goal: Goal)
    
    /**
     * Update goal progress (miles)
     */
    @Query("UPDATE goals SET currentMiles = :currentMiles WHERE id = :goalId")
    suspend fun updateGoalMilesProgress(goalId: Long, currentMiles: Int)
    
    /**
     * Update goal progress (value)
     */
    @Query("UPDATE goals SET currentValue = :currentValue WHERE id = :goalId")
    suspend fun updateGoalValueProgress(goalId: Long, currentValue: Double)
    
    /**
     * Mark goal as completed
     */
    @Query("UPDATE goals SET completedAt = :completedAt, isActive = 0 WHERE id = :goalId")
    suspend fun markGoalCompleted(goalId: Long, completedAt: kotlinx.datetime.LocalDateTime)
    
    /**
     * Activate/deactivate goal
     */
    @Query("UPDATE goals SET isActive = :isActive WHERE id = :goalId")
    suspend fun setGoalActive(goalId: Long, isActive: Boolean)
    
    /**
     * Delete goal
     */
    @Delete
    suspend fun deleteGoal(goal: Goal)
    
    /**
     * Delete goal by ID
     */
    @Query("DELETE FROM goals WHERE id = :goalId")
    suspend fun deleteGoalById(goalId: Long)
    
    /**
     * Delete all goals for a user
     */
    @Query("DELETE FROM goals WHERE userId = :userId")
    suspend fun deleteGoalsByUser(userId: String)
    
    /**
     * Delete completed goals older than specified date
     */
    @Query("DELETE FROM goals WHERE completedAt IS NOT NULL AND completedAt < :cutoffDate")
    suspend fun deleteOldCompletedGoals(cutoffDate: kotlinx.datetime.LocalDateTime): Int
    
    /**
     * Get count of active goals for a user
     */
    @Query("SELECT COUNT(*) FROM goals WHERE userId = :userId AND isActive = 1")
    suspend fun getActiveGoalsCount(userId: String): Int
    
    /**
     * Get count of goals by type for a user
     */
    @Query("SELECT COUNT(*) FROM goals WHERE userId = :userId AND goalType = :goalType")
    suspend fun getGoalsCountByType(userId: String, goalType: GoalType): Int
}

/**
 * Data class for goal statistics
 */
data class GoalStatistics(
    val totalGoals: Int,
    val completedGoals: Int,
    val activeGoals: Int,
    val overdueGoals: Int
) {
    val completionRate: Double
        get() = if (totalGoals > 0) (completedGoals.toDouble() / totalGoals.toDouble()) * 100.0 else 0.0
}

/**
 * Data class for goal progress summary by type
 */
data class GoalProgressSummary(
    val goalType: GoalType,
    val totalGoals: Int,
    val avgProgress: Double
)
