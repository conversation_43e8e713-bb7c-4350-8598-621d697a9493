package com.sirch.mileagetracker.presentation.ads;

import android.app.Activity;
import android.content.Context;
import androidx.compose.runtime.*;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001:\u0001\u001aB\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010\u0013\u001a\u00020\u0014J\u0006\u0010\u0015\u001a\u00020\u0007J\b\u0010\u0016\u001a\u00020\u0014H\u0002J\u000e\u0010\u0017\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u0019R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001b"}, d2 = {"Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager;", "", "context", "Landroid/content/Context;", "adUnitId", "", "showAds", "", "(Landroid/content/Context;Ljava/lang/String;Z)V", "_adEvents", "Lkotlinx/coroutines/channels/Channel;", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent;", "adEvents", "Lkotlinx/coroutines/flow/Flow;", "getAdEvents", "()Lkotlinx/coroutines/flow/Flow;", "interstitialAd", "Lcom/google/android/gms/ads/interstitial/InterstitialAd;", "isLoading", "destroy", "", "isAdReady", "loadAd", "showAd", "activity", "Landroid/app/Activity;", "AdEvent", "app_release"})
public final class InterstitialAdManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String adUnitId = null;
    private final boolean showAds = false;
    @org.jetbrains.annotations.Nullable()
    private com.google.android.gms.ads.interstitial.InterstitialAd interstitialAd;
    private boolean isLoading = false;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.channels.Channel<com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent> _adEvents = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent> adEvents = null;
    
    public InterstitialAdManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String adUnitId, boolean showAds) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent> getAdEvents() {
        return null;
    }
    
    private final void loadAd() {
    }
    
    public final boolean showAd(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
        return false;
    }
    
    public final boolean isAdReady() {
        return false;
    }
    
    public final void destroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0005\u0003\u0004\u0005\u0006\u0007B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0005\b\t\n\u000b\f\u00a8\u0006\r"}, d2 = {"Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent;", "", "()V", "AdDismissed", "AdFailedToLoad", "AdFailedToShow", "AdLoaded", "AdShown", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent$AdDismissed;", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent$AdFailedToLoad;", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent$AdFailedToShow;", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent$AdLoaded;", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent$AdShown;", "app_release"})
    public static abstract class AdEvent {
        
        private AdEvent() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent$AdDismissed;", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent;", "()V", "app_release"})
        public static final class AdDismissed extends com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent {
            @org.jetbrains.annotations.NotNull()
            public static final com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent.AdDismissed INSTANCE = null;
            
            private AdDismissed() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent$AdFailedToLoad;", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent;", "error", "Lcom/google/android/gms/ads/LoadAdError;", "(Lcom/google/android/gms/ads/LoadAdError;)V", "getError", "()Lcom/google/android/gms/ads/LoadAdError;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_release"})
        public static final class AdFailedToLoad extends com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent {
            @org.jetbrains.annotations.NotNull()
            private final com.google.android.gms.ads.LoadAdError error = null;
            
            public AdFailedToLoad(@org.jetbrains.annotations.NotNull()
            com.google.android.gms.ads.LoadAdError error) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.google.android.gms.ads.LoadAdError getError() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.google.android.gms.ads.LoadAdError component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent.AdFailedToLoad copy(@org.jetbrains.annotations.NotNull()
            com.google.android.gms.ads.LoadAdError error) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent$AdFailedToShow;", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent;", "error", "Lcom/google/android/gms/ads/AdError;", "(Lcom/google/android/gms/ads/AdError;)V", "getError", "()Lcom/google/android/gms/ads/AdError;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_release"})
        public static final class AdFailedToShow extends com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent {
            @org.jetbrains.annotations.NotNull()
            private final com.google.android.gms.ads.AdError error = null;
            
            public AdFailedToShow(@org.jetbrains.annotations.NotNull()
            com.google.android.gms.ads.AdError error) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.google.android.gms.ads.AdError getError() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.google.android.gms.ads.AdError component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent.AdFailedToShow copy(@org.jetbrains.annotations.NotNull()
            com.google.android.gms.ads.AdError error) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent$AdLoaded;", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent;", "()V", "app_release"})
        public static final class AdLoaded extends com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent {
            @org.jetbrains.annotations.NotNull()
            public static final com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent.AdLoaded INSTANCE = null;
            
            private AdLoaded() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent$AdShown;", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager$AdEvent;", "()V", "app_release"})
        public static final class AdShown extends com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent {
            @org.jetbrains.annotations.NotNull()
            public static final com.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent.AdShown INSTANCE = null;
            
            private AdShown() {
            }
        }
    }
}