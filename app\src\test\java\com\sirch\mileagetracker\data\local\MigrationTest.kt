package com.sirch.mileagetracker.data.local

import androidx.room.Room
import androidx.room.testing.MigrationTestHelper
import androidx.sqlite.db.framework.FrameworkSQLiteOpenHelperFactory
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.sirch.mileagetracker.data.local.entities.*
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import java.io.IOException

/**
 * Test class for database migrations.
 * Ensures that migration from v1 to v2 works correctly and maintains data integrity.
 */
@RunWith(AndroidJUnit4::class)
class MigrationTest {

    private val TEST_DB = "migration-test"

    @get:Rule
    val helper: MigrationTestHelper = MigrationTestHelper(
        InstrumentationRegistry.getInstrumentation(),
        MileageTrackerDatabase::class.java,
        listOf(),
        FrameworkSQLiteOpenHelperFactory()
    )

    @Test
    @Throws(IOException::class)
    fun migrate1To2() {
        var db = helper.createDatabase(TEST_DB, 1).apply {
            // Insert test data in v1 schema
            execSQL("""
                INSERT INTO programs (id, name, description, logoUrl, isActive)
                VALUES (1, 'Test Program', 'Test Description', null, 1)
            """)

            execSQL("""
                INSERT INTO purchases (id, programId, date, miles, cost, description)
                VALUES (1, 1, '2024-01-01', 1000, 100.0, 'Test Purchase')
            """)

            execSQL("""
                INSERT INTO settings (id, adsRemoved, userId, lastSyncTime, darkModeEnabled, cloudBackupEnabled, lastBackupTimestamp)
                VALUES (1, 0, 'test-user', null, 0, 0, 0)
            """)

            close()
        }

        // Re-open the database with version 3 and provide migrations as the migration process
        db = helper.runMigrationsAndValidate(TEST_DB, 3, true, MileageTrackerDatabase.MIGRATION_1_2, MileageTrackerDatabase.MIGRATION_2_3)

        // Verify that new tables exist
        val cursor = db.query("SELECT name FROM sqlite_master WHERE type='table'")
        val tableNames = mutableSetOf<String>()

        while (cursor.moveToNext()) {
            tableNames.add(cursor.getString(0))
        }
        cursor.close()

        // Check that all new tables were created
        assert(tableNames.contains("efficiency_scores"))
        assert(tableNames.contains("aggregated_data"))
        assert(tableNames.contains("goals"))
        assert(tableNames.contains("notification_configs"))
        assert(tableNames.contains("backup_history"))
        assert(tableNames.contains("report_templates"))

        // Verify that old data is still present
        val programCursor = db.query("SELECT * FROM programs WHERE id = 1")
        assert(programCursor.moveToFirst())
        assert(programCursor.getString(programCursor.getColumnIndexOrThrow("name")) == "Test Program")
        programCursor.close()

        val purchaseCursor = db.query("SELECT * FROM purchases WHERE id = 1")
        assert(purchaseCursor.moveToFirst())
        assert(purchaseCursor.getInt(purchaseCursor.getColumnIndexOrThrow("miles")) == 1000)
        purchaseCursor.close()

        val settingsCursor = db.query("SELECT * FROM settings WHERE id = 1")
        assert(settingsCursor.moveToFirst())
        assert(settingsCursor.getString(settingsCursor.getColumnIndexOrThrow("userId")) == "test-user")
        settingsCursor.close()

        db.close()
    }

    @Test
    @Throws(IOException::class)
    fun testNewEntitiesCanBeInserted() {
        // Create database with v2 schema
        val db = helper.createDatabase(TEST_DB, 2).apply {
            // Insert test data for new entities
            val now = Clock.System.now().toEpochMilliseconds()

            // Test EfficiencyScore insertion
            execSQL("""
                INSERT INTO efficiency_scores
                (userId, programId, personalAvgCostPerMile, aggregateAvgCostPerMile, score, percentile, lastUpdated, sampleSize, isDataSharingEnabled)
                VALUES ('test-user', 1, 0.10, 0.12, 85.5, 75.0, $now, 50, 1)
            """)

            // Test AggregatedData insertion
            execSQL("""
                INSERT INTO aggregated_data
                (programId, anonymizedAvgCostPerMile, sampleSize, lastUpdated, region, dataVersion, confidenceLevel, minCostPerMile, maxCostPerMile, standardDeviation)
                VALUES (1, 0.12, 100, $now, 'BR', 1, 95.0, 0.05, 0.25, 0.03)
            """)

            // Test Goal insertion
            val targetDate = Clock.System.now().plus(kotlinx.datetime.DateTimePeriod(days = 30)).toEpochMilliseconds()
            execSQL("""
                INSERT INTO goals
                (userId, programId, title, targetMiles, currentMiles, targetDate, isActive, createdAt, goalType, isPremium)
                VALUES ('test-user', 1, 'Test Goal', 5000, 1000, $targetDate, 1, $now, 'MILES', 0)
            """)

            // Test NotificationConfig insertion
            execSQL("""
                INSERT INTO notification_configs
                (userId, type, enabled, frequency, timeOfDay, daysOfWeek, isPremium)
                VALUES ('test-user', 'PURCHASE_REMINDER', 1, 'WEEKLY', 18, 64, 0)
            """)

            // Test BackupHistory insertion
            execSQL("""
                INSERT INTO backup_history
                (userId, backupTimestamp, backupSize, isPremium, status, backupType, isEncrypted)
                VALUES ('test-user', $now, 1024000, 0, 'COMPLETED', 'AUTOMATIC', 1)
            """)

            // Test ReportTemplate insertion
            execSQL("""
                INSERT INTO report_templates
                (userId, name, filters, isPremium, createdAt, reportType, outputFormat, dateRange, includeCharts)
                VALUES ('test-user', 'Test Report', '{}', 0, $now, 'SUMMARY', 'PDF', 'LAST_MONTH', 1)
            """)

            close()
        }

        // Verify data was inserted correctly
        val efficiencyScoreCursor = db.query("SELECT * FROM efficiency_scores WHERE userId = 'test-user'")
        assert(efficiencyScoreCursor.moveToFirst())
        assert(efficiencyScoreCursor.getDouble(efficiencyScoreCursor.getColumnIndexOrThrow("score")) == 85.5)
        efficiencyScoreCursor.close()

        val aggregatedDataCursor = db.query("SELECT * FROM aggregated_data WHERE programId = 1")
        assert(aggregatedDataCursor.moveToFirst())
        assert(aggregatedDataCursor.getInt(aggregatedDataCursor.getColumnIndexOrThrow("sampleSize")) == 100)
        aggregatedDataCursor.close()

        val goalCursor = db.query("SELECT * FROM goals WHERE userId = 'test-user'")
        assert(goalCursor.moveToFirst())
        assert(goalCursor.getString(goalCursor.getColumnIndexOrThrow("title")) == "Test Goal")
        goalCursor.close()

        db.close()
    }

    @Test
    @Throws(IOException::class)
    fun testIndexesAreCreated() {
        val db = helper.createDatabase(TEST_DB, 2)

        // Check that indexes were created
        val indexCursor = db.query("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'index_%'")
        val indexNames = mutableSetOf<String>()

        while (indexCursor.moveToNext()) {
            indexNames.add(indexCursor.getString(0))
        }
        indexCursor.close()

        // Verify some key indexes exist
        assert(indexNames.contains("index_efficiency_scores_userId"))
        assert(indexNames.contains("index_efficiency_scores_programId"))
        assert(indexNames.contains("index_aggregated_data_programId"))
        assert(indexNames.contains("index_goals_userId"))
        assert(indexNames.contains("index_notification_configs_userId_type"))

        db.close()
    }

    @Test
    @Throws(IOException::class)
    fun migrate2To3_RemovesDarkModeField() {
        // Create database with v2 schema
        var db = helper.createDatabase(TEST_DB, 2).apply {
            // Insert test data with darkModeEnabled field
            execSQL("""
                INSERT INTO settings (id, adsRemoved, userId, lastSyncTime, darkModeEnabled, cloudBackupEnabled, lastBackupTimestamp)
                VALUES (1, 1, 'test-user', 1234567890, 1, 0, 0)
            """)

            close()
        }

        // Run migration 2_3
        db = helper.runMigrationsAndValidate(TEST_DB, 3, true, MileageTrackerDatabase.MIGRATION_2_3)

        // Verify that darkModeEnabled field was removed
        val cursor = db.query("PRAGMA table_info(settings)")
        val columnNames = mutableSetOf<String>()

        while (cursor.moveToNext()) {
            val columnName = cursor.getString(cursor.getColumnIndexOrThrow("name"))
            columnNames.add(columnName)
        }
        cursor.close()

        // Check that darkModeEnabled field is not present
        assert(!columnNames.contains("darkModeEnabled"))

        // Check that other fields are still present
        assert(columnNames.contains("id"))
        assert(columnNames.contains("adsRemoved"))
        assert(columnNames.contains("userId"))
        assert(columnNames.contains("lastSyncTime"))
        assert(columnNames.contains("cloudBackupEnabled"))
        assert(columnNames.contains("lastBackupTimestamp"))

        // Verify that data was preserved (excluding darkModeEnabled)
        val settingsCursor = db.query("SELECT * FROM settings WHERE id = 1")
        assert(settingsCursor.moveToFirst())
        assert(settingsCursor.getInt(settingsCursor.getColumnIndexOrThrow("adsRemoved")) == 1)
        assert(settingsCursor.getString(settingsCursor.getColumnIndexOrThrow("userId")) == "test-user")
        settingsCursor.close()

        db.close()
    }
}
