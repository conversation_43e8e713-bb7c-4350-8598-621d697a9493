package com.sirch.mileagetracker.presentation.purchase

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sirch.mileagetracker.data.local.entities.Program
import com.sirch.mileagetracker.data.local.entities.Purchase
import com.sirch.mileagetracker.data.repository.ProgramRepository
import com.sirch.mileagetracker.data.repository.PurchaseRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.todayIn
import javax.inject.Inject

@HiltViewModel
class PurchaseFormViewModel @Inject constructor(
    private val purchaseRepository: PurchaseRepository,
    private val programRepository: ProgramRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(PurchaseFormUiState())
    val uiState: StateFlow<PurchaseFormUiState> = _uiState.asStateFlow()
    
    private var currentPurchaseId: Long? = null
    
    fun loadPrograms() {
        viewModelScope.launch {
            try {
                val programs = programRepository.getAllActivePrograms().first()
                _uiState.value = _uiState.value.copy(
                    programs = programs,
                    isLoading = false
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message,
                    isLoading = false
                )
            }
        }
    }
    
    fun initializeForProgram(programId: Long) {
        viewModelScope.launch {
            try {
                val program = programRepository.getProgramById(programId)
                _uiState.value = _uiState.value.copy(
                    selectedProgram = program,
                    date = Clock.System.todayIn(TimeZone.currentSystemDefault())
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = e.message)
            }
        }
    }
    
    fun loadPurchase(purchaseId: Long) {
        currentPurchaseId = purchaseId
        viewModelScope.launch {
            try {
                val purchase = purchaseRepository.getPurchaseById(purchaseId)
                if (purchase != null) {
                    val program = programRepository.getProgramById(purchase.programId)
                    _uiState.value = _uiState.value.copy(
                        selectedProgram = program,
                        date = purchase.date,
                        miles = purchase.miles.toString(),
                        cost = purchase.cost.toString(),
                        description = purchase.description ?: "",
                        isLoading = false
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        error = "Compra não encontrada",
                        isLoading = false
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message,
                    isLoading = false
                )
            }
        }
    }
    
    fun selectProgram(program: Program) {
        _uiState.value = _uiState.value.copy(selectedProgram = program)
    }
    
    fun selectDate(date: LocalDate) {
        _uiState.value = _uiState.value.copy(date = date)
    }
    
    fun updateMiles(miles: String) {
        _uiState.value = _uiState.value.copy(
            miles = miles,
            milesError = null
        )
    }
    
    fun updateCost(cost: String) {
        _uiState.value = _uiState.value.copy(
            cost = cost,
            costError = null
        )
    }
    
    fun updateDescription(description: String) {
        _uiState.value = _uiState.value.copy(description = description)
    }
    
    fun savePurchase() {
        if (!validateForm()) return
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSaving = true, error = null)
            
            try {
                val state = _uiState.value
                val purchase = Purchase(
                    id = currentPurchaseId ?: 0,
                    programId = state.selectedProgram!!.id,
                    date = state.date!!,
                    miles = state.miles.toInt(),
                    cost = state.cost.toDouble(),
                    description = state.description.takeIf { it.isNotBlank() }
                )
                
                if (currentPurchaseId != null) {
                    purchaseRepository.updatePurchase(purchase)
                } else {
                    purchaseRepository.insertPurchase(purchase)
                }
                
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    isOperationComplete = true
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    error = e.message
                )
            }
        }
    }
    
    fun showDeleteConfirmation() {
        _uiState.value = _uiState.value.copy(showDeleteConfirmation = true)
    }
    
    fun hideDeleteConfirmation() {
        _uiState.value = _uiState.value.copy(showDeleteConfirmation = false)
    }
    
    fun deletePurchase() {
        val purchaseId = currentPurchaseId ?: return
        
        viewModelScope.launch {
            try {
                val purchase = purchaseRepository.getPurchaseById(purchaseId)
                if (purchase != null) {
                    purchaseRepository.deletePurchase(purchase)
                    _uiState.value = _uiState.value.copy(
                        showDeleteConfirmation = false,
                        isOperationComplete = true
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    showDeleteConfirmation = false,
                    error = e.message
                )
            }
        }
    }
    
    private fun validateForm(): Boolean {
        val state = _uiState.value
        var hasErrors = false
        
        // Validate program selection
        if (state.selectedProgram == null) {
            _uiState.value = _uiState.value.copy(error = "Selecione um programa")
            return false
        }
        
        // Validate date
        if (state.date == null) {
            _uiState.value = _uiState.value.copy(error = "Selecione uma data")
            return false
        }
        
        // Validate miles
        val milesError = when {
            state.miles.isBlank() -> "Digite a quantidade de milhas"
            state.miles.toIntOrNull() == null -> "Digite um número válido"
            state.miles.toInt() <= 0 -> "Quantidade deve ser maior que zero"
            else -> null
        }
        if (milesError != null) {
            _uiState.value = _uiState.value.copy(milesError = milesError)
            hasErrors = true
        }
        
        // Validate cost
        val costError = when {
            state.cost.isBlank() -> "Digite o custo"
            state.cost.toDoubleOrNull() == null -> "Digite um valor válido"
            state.cost.toDouble() <= 0 -> "Custo deve ser maior que zero"
            else -> null
        }
        if (costError != null) {
            _uiState.value = _uiState.value.copy(costError = costError)
            hasErrors = true
        }
        
        return !hasErrors
    }
}

data class PurchaseFormUiState(
    val isLoading: Boolean = true,
    val programs: List<Program> = emptyList(),
    val selectedProgram: Program? = null,
    val date: LocalDate? = null,
    val miles: String = "",
    val cost: String = "",
    val description: String = "",
    val milesError: String? = null,
    val costError: String? = null,
    val error: String? = null,
    val isSaving: Boolean = false,
    val isOperationComplete: Boolean = false,
    val showDeleteConfirmation: Boolean = false
)
