package com.sirch.mileagetracker.presentation.ads;

import androidx.compose.runtime.*;
import androidx.compose.ui.Modifier;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdSize;
import com.google.android.gms.ads.AdView;
import com.sirch.mileagetracker.BuildConfig;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.admanager.AdManagerAdRequest;
import com.google.android.gms.ads.admanager.AdManagerAdView;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0011\u0010\t\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0006R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\u00020\u000eX\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u0011"}, d2 = {"Lcom/sirch/mileagetracker/presentation/ads/AdUnitIds;", "", "()V", "BANNER_ID", "", "getBANNER_ID", "()Ljava/lang/String;", "BANNER_PROD", "BANNER_TEST", "INTERSTITIAL_ID", "getINTERSTITIAL_ID", "INTERSTITIAL_PROD", "INTERSTITIAL_TEST", "IS_DEBUG", "", "getIS_DEBUG", "()Z", "app_release"})
public final class AdUnitIds {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String BANNER_TEST = "ca-app-pub-3940256099942544/6300978111";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String INTERSTITIAL_TEST = "ca-app-pub-3940256099942544/1033173712";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String BANNER_PROD = "ca-app-pub-8316648275077982/2235862620";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String INTERSTITIAL_PROD = "ca-app-pub-8316648275077982/2590553017";
    private static final boolean IS_DEBUG = com.sirch.mileagetracker.BuildConfig.DEBUG;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String BANNER_ID = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String INTERSTITIAL_ID = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.sirch.mileagetracker.presentation.ads.AdUnitIds INSTANCE = null;
    
    private AdUnitIds() {
        super();
    }
    
    public final boolean getIS_DEBUG() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBANNER_ID() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getINTERSTITIAL_ID() {
        return null;
    }
}