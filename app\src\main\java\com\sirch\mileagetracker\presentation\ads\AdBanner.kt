package com.sirch.mileagetracker.presentation.ads

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView
import com.sirch.mileagetracker.BuildConfig
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.admanager.AdManagerAdRequest
import com.google.android.gms.ads.admanager.AdManagerAdView

@Composable
fun AdBanner(
    adUnitId: String,
    modifier: Modifier = Modifier,
    adSize: AdSize = AdSize.BANNER,
    showAds: Boolean = true
) {
    val context = LocalContext.current

    if (!showAds) {
        return
    }

    AndroidView(
        modifier = modifier.fillMaxWidth(),
        factory = { context ->
            AdView(context).apply {
                setAdSize(adSize)
                this.adUnitId = adUnitId
                loadAd(AdRequest.Builder().build())
            }
        },
        update = { adView ->
            adView.loadAd(AdRequest.Builder().build())
        }
    )
}

@Composable
fun AdBannerWithCallback(
    adUnitId: String,
    modifier: Modifier = Modifier,
    adSize: AdSize = AdSize.BANNER,
    showAds: Boolean = true,
    onAdLoaded: () -> Unit = {},
    onAdFailedToLoad: (LoadAdError) -> Unit = {},
    onAdClicked: () -> Unit = {}
) {
    val context = LocalContext.current

    if (!showAds) {
        return
    }

    AndroidView(
        modifier = modifier.fillMaxWidth(),
        factory = { context ->
            AdView(context).apply {
                setAdSize(adSize)
                this.adUnitId = adUnitId

                adListener = object : com.google.android.gms.ads.AdListener() {
                    override fun onAdLoaded() {
                        super.onAdLoaded()
                        onAdLoaded()
                    }

                    override fun onAdFailedToLoad(error: LoadAdError) {
                        super.onAdFailedToLoad(error)
                        onAdFailedToLoad(error)
                    }

                    override fun onAdClicked() {
                        super.onAdClicked()
                        onAdClicked()
                    }
                }

                loadAd(AdRequest.Builder().build())
            }
        },
        update = { adView ->
            adView.loadAd(AdRequest.Builder().build())
        }
    )
}

// Production Ad Unit IDs
object AdUnitIds {
    // Test IDs - Only for development
    const val BANNER_TEST = "ca-app-pub-****************/**********"
    const val INTERSTITIAL_TEST = "ca-app-pub-****************/**********"

    // Production IDs (from your AdMob account)
    const val BANNER_PROD = "ca-app-pub-****************/**********"
    const val INTERSTITIAL_PROD = "ca-app-pub-****************/**********" // Replace with actual ID

    // Automatically detect build type
    val IS_DEBUG = BuildConfig.DEBUG

    val BANNER_ID = if (IS_DEBUG) BANNER_TEST else BANNER_PROD
    val INTERSTITIAL_ID = if (IS_DEBUG) INTERSTITIAL_TEST else INTERSTITIAL_PROD
}
