# 🔐 Configuração Firebase para Release - Mileage Tracker

## 🚨 **Problema: Login não funciona no APK Release**

### ❌ **Sintomas:**
- Login funciona no debug build
- Login falha no release build (APK/AAB)
- Erro: "DEVELOPER_ERROR" ou "SIGN_IN_FAILED"

### 🔍 **Causa:**
O Firebase precisa do **SHA-1 fingerprint** do keystore de release para autorizar o Google Sign-In.

## ✅ **Sol<PERSON><PERSON> Completa:**

### **1. Obter SHA-1 do Keystore de Release**

#### **Se você já tem keystore:**
```bash
# Windows
keytool -list -v -keystore mileage-tracker-release.keystore -alias mileage-tracker

# Linux/Mac
keytool -list -v -keystore mileage-tracker-release.keystore -alias mileage-tracker
```

#### **Se ainda não tem keystore:**
```bash
# Criar keystore
keytool -genkey -v -keystore mileage-tracker-release.keystore -alias mileage-tracker -keyalg RSA -keysize 2048 -validity 10000

# Depois obter SHA-1
keytool -list -v -keystore mileage-tracker-release.keystore -alias mileage-tracker
```

#### **Exemplo de output:**
```
Certificate fingerprints:
SHA1: A1:B2:C3:D4:E5:F6:G7:H8:I9:J0:K1:L2:M3:N4:O5:P6:Q7:R8:S9:T0
SHA256: ...
```

**Copie o valor SHA1!**

### **2. Configurar no Firebase Console**

#### **Passo 1: Acessar Firebase Console**
1. Acesse: https://console.firebase.google.com/
2. Selecione seu projeto "Mileage Tracker"
3. Vá em **Project Settings** (⚙️)

#### **Passo 2: Adicionar SHA-1**
1. Na aba **General**, role até **Your apps**
2. Clique no app Android (com package `com.sirch.mileagetracker`)
3. Clique em **Add fingerprint**
4. Cole o **SHA-1** do seu keystore de release
5. Clique **Save**

#### **Passo 3: Baixar novo google-services.json**
1. Clique em **Download google-services.json**
2. Substitua o arquivo em `app/google-services.json`
3. **Rebuild** o projeto

### **3. Verificar Configuração**

#### **No Firebase Console:**
- ✅ **Authentication** habilitado
- ✅ **Google Sign-In** provider ativo
- ✅ **SHA-1 debug** configurado (para desenvolvimento)
- ✅ **SHA-1 release** configurado (para produção)

#### **No Android Studio:**
- ✅ **google-services.json** atualizado
- ✅ **Package name** correto: `com.sirch.mileagetracker`
- ✅ **Build limpo** após mudanças

### **4. Testar a Correção**

#### **Build Release:**
```bash
# Limpar projeto
./gradlew clean

# Gerar APK release
./gradlew assembleRelease

# Instalar no dispositivo
adb install app/build/outputs/apk/release/app-release.apk
```

#### **Teste no Dispositivo:**
1. **Desinstalar** versão anterior
2. **Instalar** APK release
3. **Testar** Google Sign-In
4. **Verificar** se login funciona

## 🔧 **Configurações Adicionais**

### **Para Play Store (AAB):**
```bash
# Gerar AAB
./gradlew bundleRelease

# Upload na Play Console
# O Google Play irá gerar SHA-1 adicional
```

#### **SHA-1 do Google Play:**
1. **Publique** na Play Console (internal testing)
2. **Acesse** Play Console → Release → Setup → App signing
3. **Copie** o SHA-1 certificate fingerprint
4. **Adicione** no Firebase Console também

### **Para Múltiplos Ambientes:**

#### **Debug SHA-1 (já configurado):**
```bash
# Obter SHA-1 debug
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

#### **Release SHA-1 (adicionar):**
- Use o SHA-1 do seu keystore de produção

#### **Play Store SHA-1 (adicionar após upload):**
- Use o SHA-1 gerado pelo Google Play

## 🎯 **Checklist Final:**

### **Firebase Console:**
- [ ] **Projeto criado** e configurado
- [ ] **Authentication** habilitado
- [ ] **Google Sign-In** provider ativo
- [ ] **SHA-1 debug** adicionado
- [ ] **SHA-1 release** adicionado
- [ ] **google-services.json** baixado

### **Android Studio:**
- [ ] **google-services.json** atualizado
- [ ] **Package name** correto
- [ ] **Keystore** configurado
- [ ] **Build limpo** realizado

### **Teste:**
- [ ] **Debug build** funciona
- [ ] **Release APK** funciona
- [ ] **Login Google** funciona
- [ ] **Dados sincronizam**

## 🚨 **Troubleshooting:**

### **Erro: DEVELOPER_ERROR**
- ❌ **Causa:** SHA-1 não configurado
- ✅ **Solução:** Adicionar SHA-1 no Firebase

### **Erro: SIGN_IN_CANCELLED**
- ❌ **Causa:** Usuário cancelou
- ✅ **Solução:** Normal, não é erro

### **Erro: NETWORK_ERROR**
- ❌ **Causa:** Sem internet
- ✅ **Solução:** Verificar conexão

### **Login funciona no debug mas não no release**
- ❌ **Causa:** SHA-1 release não configurado
- ✅ **Solução:** Seguir passos acima

## 📱 **Comandos Úteis:**

### **Obter SHA-1 Debug:**
```bash
# Windows
keytool -list -v -keystore %USERPROFILE%\.android\debug.keystore -alias androiddebugkey -storepass android -keypass android

# Linux/Mac
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

### **Obter SHA-1 Release:**
```bash
keytool -list -v -keystore SEU_KEYSTORE.keystore -alias SEU_ALIAS
```

### **Verificar Package Name:**
```bash
# No AndroidManifest.xml
grep "package=" app/src/main/AndroidManifest.xml
```

## 🎉 **Após Configurar:**

**O Google Sign-In funcionará perfeitamente no release build!**

**Lembre-se:**
- ✅ **Sempre** adicionar SHA-1 release no Firebase
- ✅ **Baixar** novo google-services.json
- ✅ **Rebuild** projeto após mudanças
- ✅ **Testar** em dispositivo real

**Agora seu app está pronto para produção com autenticação funcionando! 🚀**
