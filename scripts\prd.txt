Documento de Requisitos do Produto (PRD) - Mileage Tracker App v0.2.0

1. Visão Geral

Nome do Produto: Mileage Tracker
Versão: 0.2.0

Descrição: Aplicativo mobile avançado para acompanhar e gerenciar despesas e milhas acumuladas em programas de fidelidade de companhias aéreas. Inclui análises detalhadas, exportação de dados, backup na nuvem, notificações inteligentes, e recursos premium para usuários avançados.

Plataformas: Android (minSdk 33, targetSdk 35)

Principais Novidades v0.2.0:
- Dashboard com gráficos e estatísticas avançadas
- Sistema de backup e sincronização na nuvem (Firebase)
- Exportação de relatórios (PDF/Excel)
- Notificações inteligentes e lembretes
- Recursos premium expandidos
- Remoção do toggle de dark mode (usa configuração do sistema)
- Efficiency Score: comparação com outros usuários
- Metas e objetivos de milhagem

2. Objetivos do Produto

Ajudar usuários a monitorar gastos e milhas acumuladas em diferentes programas de fidelidade.

Fornecer insights avançados com gráficos, estatísticas e análises preditivas.

Garantir experiência fluida com navegação Compose e sincronização em tempo real via Coroutines/Flow.

Oferecer backup seguro na nuvem e sincronização entre dispositivos.

Monetização: anúncios intersticiais/banner (AdMob) e recursos premium robustos.

Facilitar tomada de decisões com relatórios exportáveis e notificações inteligentes.

3. Público-Alvo

Viajantes frequentes que participam de programas de milhagem.

Usuários que buscam controle financeiro de suas despesas de viagem.

Amantes de apps simples, com UX limpa e design moderno (Jetpack Compose).

4. Funcionalidades Principais

=== RECURSOS BÁSICOS (Gratuitos) ===

ID | Funcionalidade | Descrição breve
F1 | Autenticação Google/Firebase | Login com conta Google, persistência via FirebaseAuth.
F2 | Seed programas | Carregamento inicial de programas padrão (LATAM, Smiles...).
F3 | Registro de compras | CRUD de compras: data, programa, milhas, custo.
F4 | Lista de compras | Exibição ordenada por data (Compose + Flow).
F5 | Cálculo custo médio por milha | Consulta com JOIN e AVG no Room.
F6 | Anúncios AdMob | Banner/intersticial configurável via AdMob.
F7 | Compras in-app (Billing) | Desbloqueio de recursos premium.
F8 | Monitoramento de estados (Hilt) | Injeção de dependências com Hilt + ViewModels.

=== NOVOS RECURSOS v0.2.0 (Básicos) ===

F9 | Dashboard com estatísticas básicas | Gráficos simples de gastos mensais e anuais.
F10 | Efficiency Score básico | Comparação simples com média agregada de outros usuários.
F11 | Remoção do toggle dark mode | App segue configuração do sistema automaticamente.
F12 | Notificações básicas | Lembretes para registrar compras.
F13 | Backup básico na nuvem | Sincronização automática dos dados com Firebase.

=== RECURSOS PREMIUM v0.2.0 ===

F14 | Dashboard avançado | Gráficos detalhados, análises preditivas, comparações.
F15 | Efficiency Score avançado | Insights detalhados, recomendações personalizadas, histórico.
F16 | Exportação de relatórios | PDF e Excel com dados personalizáveis.
F17 | Metas e objetivos | Definir metas de milhagem e acompanhar progresso.
F18 | Análises avançadas | Insights sobre padrões de gasto e otimizações.
F19 | Notificações inteligentes | Alertas sobre promoções, vencimentos, metas.
F20 | Backup premium | Histórico de versões, restauração seletiva.
F21 | Múltiplos dispositivos | Sincronização em tempo real entre dispositivos.
F22 | Relatórios personalizados | Filtros avançados, períodos customizados.
F23 | Suporte prioritário | Atendimento preferencial e recursos beta.

5. Requisitos Funcionais

=== REQUISITOS BÁSICOS ===
RF01: O usuário deve se autenticar via conta Google.
RF02: O app deve criar registro de programas padrão na primeira execução.
RF03: O usuário deve adicionar, editar e excluir compras.
RF04: Deve ser possível filtrar compras por programa.
RF05: Exibir custo médio por milha para cada programa.
RF06: Exibir anúncios de banner na tela principal.
RF07: Ofertar SKU de compra in-app para remover anúncios.

=== NOVOS REQUISITOS v0.2.0 ===
RF08: Dashboard deve exibir gráficos de gastos mensais e anuais.
RF09: Sistema deve calcular Efficiency Score comparando com média agregada de usuários.
RF10: App deve seguir automaticamente o tema do sistema (dark/light).
RF11: Sistema deve enviar notificações de lembrete configuráveis.
RF12: Dados devem ser sincronizados automaticamente com Firebase Cloud.
RF13: Efficiency Score deve usar dados anonimizados e agregados para comparações.
RF14: Sistema deve coletar dados agregados respeitando privacidade do usuário.
RF15: Usuários premium devem poder exportar relatórios em PDF/Excel.
RF16: Sistema deve permitir definir e acompanhar metas de milhagem.
RF17: Dashboard premium deve incluir análises preditivas e comparações.
RF18: Efficiency Score premium deve fornecer insights e recomendações detalhadas.
RF19: Notificações inteligentes devem alertar sobre promoções e vencimentos.
RF20: Backup premium deve manter histórico de versões dos dados.
RF21: Sincronização deve funcionar em tempo real entre múltiplos dispositivos.
RF22: Relatórios devem ser personalizáveis com filtros avançados.
RF23: Usuários premium devem ter acesso a suporte prioritário.

6. Requisitos Não Funcionais

RNF01: Tempo de resposta de consultas < 200ms.
RNF02: Alta disponibilidade offline (dados locais via Room).
RNF03: Suporte a telas diferentes (responsividade Compose).
RNF04: Segurança de dados sensíveis (FirebaseAuth + Room criptografado opcional).
RNF05: Baixa utilização de bateria (Coroutines bem otimizadas).
RNF06: Sincronização em nuvem deve ser resiliente a falhas de rede.
RNF07: Exportação de relatórios deve ser processada em background.
RNF08: Notificações devem respeitar configurações de Do Not Disturb.
RNF09: Interface deve seguir Material Design 3 guidelines.
RNF10: App deve funcionar offline com sincronização posterior.
RNF11: Dados para Efficiency Score devem ser completamente anonimizados.
RNF12: Agregação de dados deve ser processada em servidor seguro.
RNF13: Cálculo de Efficiency Score deve ser atualizado diariamente.
RNF14: Sistema deve garantir LGPD/GDPR compliance para dados agregados.
RNF15: Falhas na coleta de dados agregados não devem afetar funcionalidades básicas.

7. Fluxo de Usuário

=== FLUXO BÁSICO ===
Splash: Seed + verificação de login + sincronização inicial.
Login: Autenticação Google.
Dashboard: Visão geral com gráficos, estatísticas e Efficiency Score básico.
Home: Lista de programas e resumo de custo médio.
Detalhes do Programa: Compras associadas + gráficos específicos.
Formulário de Compra: CRUD de compras.
Configurações: Remover anúncios, notificações, backup.

=== NOVOS FLUXOS v0.2.0 ===
Efficiency Score: Visualizar comparação com outros usuários e score pessoal.
Dashboard Avançado (Premium): Análises detalhadas, comparações e insights.
Efficiency Score Premium: Recomendações personalizadas e histórico detalhado.
Metas: Definir objetivos e acompanhar progresso.
Relatórios: Gerar e exportar relatórios personalizados.
Notificações: Configurar lembretes e alertas inteligentes.
Backup: Gerenciar sincronização e restauração de dados.

8. Integrações Técnicas

=== EXISTENTES ===
Room: entidades Program, Purchase, Settings.
Hilt: módulos para repositórios, DAOs, FirebaseAuth.
Firebase: Auth + Analytics.
AdMob: banners e intersticiais.
Billing: implementações de BillingClient.

=== NOVAS INTEGRAÇÕES v0.2.0 ===
Firebase Cloud Storage: backup e sincronização de dados.
Firebase Cloud Messaging: notificações push inteligentes.
Charts Library: gráficos e visualizações (MPAndroidChart ou Compose Charts).
PDF Generation: biblioteca para exportação de relatórios (iText ou similar).
Excel Export: Apache POI ou similar para planilhas.
WorkManager: processamento em background para sync e relatórios.
Notification Manager: sistema de notificações locais e push.

=== NOVAS ENTIDADES ROOM ===
EfficiencyScore: dados de score de eficiência do usuário.
AggregatedData: dados agregados anonimizados para comparações.
Goal: metas e objetivos de milhagem.
Notification: configurações de notificações.
BackupHistory: histórico de backups (premium).
Report: templates de relatórios salvos (premium).

=== ESTRUTURA PREMIUM ===
PremiumFeatureManager: controle de acesso a recursos premium.
EfficiencyScoreEngine: cálculo e análise de scores de eficiência.
DataAggregationService: coleta e processamento de dados agregados.
AnalyticsEngine: processamento de dados para insights avançados.
SyncManager: sincronização em tempo real entre dispositivos.
ReportGenerator: geração de relatórios personalizados.

9. Monetização v0.2.0

=== PLANO GRATUITO ===
- Funcionalidades básicas de tracking
- Anúncios (banner + intersticial)
- Dashboard básico com gráficos simples
- Efficiency Score básico (comparação simples)
- Backup básico na nuvem
- Notificações básicas

=== PLANO PREMIUM (R$ 9,90/mês ou R$ 89,90/ano) ===
- Remoção de anúncios
- Dashboard avançado com análises preditivas
- Efficiency Score avançado com insights e recomendações detalhadas
- Histórico de evolução do Efficiency Score
- Exportação ilimitada de relatórios
- Metas e objetivos personalizados
- Notificações inteligentes
- Backup premium com histórico
- Sincronização em múltiplos dispositivos
- Suporte prioritário
- Acesso antecipado a novos recursos

10. Detalhamento do Efficiency Score

=== FUNCIONAMENTO BÁSICO ===
- Calcula custo médio por milha do usuário para cada programa
- Compara com média agregada anonimizada de todos os usuários
- Exibe score de 1-5 estrelas ou percentil (ex: "Você está no top 25%")
- Atualização diária dos dados agregados

=== VERSÃO PREMIUM ===
- Insights detalhados sobre como melhorar eficiência
- Recomendações personalizadas baseadas em padrões
- Histórico de evolução do score ao longo do tempo
- Comparações segmentadas (por região, tipo de programa, etc.)
- Alertas quando score melhora ou piora significativamente

=== PRIVACIDADE E SEGURANÇA ===
- Dados completamente anonimizados antes da agregação
- Nenhuma informação pessoal identificável é compartilhada
- Conformidade com LGPD/GDPR
- Opt-out disponível para usuários que não querem participar
- Processamento seguro em servidor com criptografia
