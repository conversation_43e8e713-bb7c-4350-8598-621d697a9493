package com.sirch.mileagetracker.presentation.program

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.sirch.mileagetracker.data.local.dao.ProgramStatistics
import com.sirch.mileagetracker.data.local.entities.Program
import com.sirch.mileagetracker.data.local.entities.Purchase
import com.sirch.mileagetracker.data.repository.ProgramRepository
import com.sirch.mileagetracker.data.repository.PurchaseRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProgramDetailsViewModel @Inject constructor(
    private val programRepository: ProgramRepository,
    private val purchaseRepository: PurchaseRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ProgramDetailsUiState())
    val uiState: StateFlow<ProgramDetailsUiState> = _uiState.asStateFlow()

    fun loadProgramDetails(programId: Long) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                // Combine program info, purchases, and statistics
                combine(
                    programRepository.getProgramByIdFlow(programId),
                    purchaseRepository.getPurchasesByProgram(programId)
                ) { program, purchases ->
                    Triple(program, purchases, null)
                }
                .catch { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message
                    )
                }
                .collect { (program, purchases, _) ->
                    // Get statistics
                    val statistics = if (purchases.isNotEmpty()) {
                        purchaseRepository.getProgramStatistics(programId)
                    } else {
                        null
                    }

                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        program = program,
                        purchases = purchases,
                        statistics = statistics,
                        error = null
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }

    fun refresh(programId: Long) {
        loadProgramDetails(programId)
    }
}

data class ProgramDetailsUiState(
    val isLoading: Boolean = true,
    val program: Program? = null,
    val purchases: List<Purchase> = emptyList(),
    val statistics: ProgramStatistics? = null,
    val error: String? = null
)
