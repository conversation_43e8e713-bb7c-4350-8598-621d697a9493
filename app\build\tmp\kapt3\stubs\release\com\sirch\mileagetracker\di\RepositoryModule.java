package com.sirch.mileagetracker.di;

import android.content.Context;
import com.sirch.mileagetracker.data.auth.AuthRepository;
import com.sirch.mileagetracker.data.billing.BillingManager;
import com.sirch.mileagetracker.data.cloud.CloudBackupService;
import com.sirch.mileagetracker.data.local.dao.ProgramDao;
import com.sirch.mileagetracker.data.local.dao.PurchaseDao;
import com.sirch.mileagetracker.data.local.dao.SettingsDao;
import com.sirch.mileagetracker.data.repository.AdsRepository;
import com.sirch.mileagetracker.data.repository.ProgramRepository;
import com.sirch.mileagetracker.data.repository.PurchaseRepository;
import com.sirch.mileagetracker.data.repository.SettingsRepository;
import com.google.firebase.auth.FirebaseAuth;
import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.android.qualifiers.ApplicationContext;
import dagger.hilt.components.SingletonComponent;
import javax.inject.Singleton;

@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0007J\u001a\u0010\u000b\u001a\u00020\f2\b\b\u0001\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0004H\u0007J\u0018\u0010\u0010\u001a\u00020\u00112\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0007J\u0010\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u0019H\u0007J\u0010\u0010\u001a\u001a\u00020\u00062\u0006\u0010\u001b\u001a\u00020\u001cH\u0007\u00a8\u0006\u001d"}, d2 = {"Lcom/sirch/mileagetracker/di/RepositoryModule;", "", "()V", "provideAdsRepository", "Lcom/sirch/mileagetracker/data/repository/AdsRepository;", "settingsRepository", "Lcom/sirch/mileagetracker/data/repository/SettingsRepository;", "provideAuthRepository", "Lcom/sirch/mileagetracker/data/auth/AuthRepository;", "firebaseAuth", "Lcom/google/firebase/auth/FirebaseAuth;", "provideBillingManager", "Lcom/sirch/mileagetracker/data/billing/BillingManager;", "context", "Landroid/content/Context;", "adsRepository", "provideCloudBackupService", "Lcom/sirch/mileagetracker/data/cloud/CloudBackupService;", "provideProgramRepository", "Lcom/sirch/mileagetracker/data/repository/ProgramRepository;", "programDao", "Lcom/sirch/mileagetracker/data/local/dao/ProgramDao;", "providePurchaseRepository", "Lcom/sirch/mileagetracker/data/repository/PurchaseRepository;", "purchaseDao", "Lcom/sirch/mileagetracker/data/local/dao/PurchaseDao;", "provideSettingsRepository", "settingsDao", "Lcom/sirch/mileagetracker/data/local/dao/SettingsDao;", "app_release"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class RepositoryModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.sirch.mileagetracker.di.RepositoryModule INSTANCE = null;
    
    private RepositoryModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.data.repository.ProgramRepository provideProgramRepository(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.dao.ProgramDao programDao) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.data.repository.PurchaseRepository providePurchaseRepository(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.dao.PurchaseDao purchaseDao) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.data.repository.SettingsRepository provideSettingsRepository(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.local.dao.SettingsDao settingsDao) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.data.auth.AuthRepository provideAuthRepository(@org.jetbrains.annotations.NotNull()
    com.google.firebase.auth.FirebaseAuth firebaseAuth) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.data.repository.AdsRepository provideAdsRepository(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.data.billing.BillingManager provideBillingManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.AdsRepository adsRepository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.sirch.mileagetracker.data.cloud.CloudBackupService provideCloudBackupService(@org.jetbrains.annotations.NotNull()
    com.google.firebase.auth.FirebaseAuth firebaseAuth, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.SettingsRepository settingsRepository) {
        return null;
    }
}