package com.sirch.mileagetracker.utils;

import androidx.compose.runtime.*;
import androidx.compose.ui.unit.Dp;

/**
 * Stable wrapper for lambda functions to prevent recomposition
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0006\b\u0007\u0018\u0000*\u0004\b\u0000\u0010\u00012\u00020\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00028\u0000\u00a2\u0006\u0002\u0010\u0004R\u0013\u0010\u0003\u001a\u00028\u0000\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\b"}, d2 = {"Lcom/sirch/mileagetracker/utils/StableLambda;", "T", "", "lambda", "(Ljava/lang/Object;)V", "getLambda", "()Ljava/lang/Object;", "Ljava/lang/Object;", "app_release"})
@androidx.compose.runtime.Stable()
public final class StableLambda<T extends java.lang.Object> {
    private final T lambda = null;
    
    public StableLambda(T lambda) {
        super();
    }
    
    public final T getLambda() {
        return null;
    }
}