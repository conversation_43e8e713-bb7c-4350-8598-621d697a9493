# Exemplo de configuração do keystore para assinatura de release
# Copie este arquivo para keystore.properties e preencha com seus dados

# Caminho para o arquivo keystore (relativo à raiz do projeto)
storeFile=mileage-tracker-release.keystore

# Senha do keystore
storePassword=SUA_SENHA_STORE_AQUI

# Alias da chave dentro do keystore
keyAlias=mileage-tracker

# Senha da chave específica
keyPassword=SUA_SENHA_KEY_AQUI

# IMPORTANTE:
# 1. Nunca commite o arquivo keystore.properties real no Git
# 2. Mantenha suas senhas seguras
# 3. Faça backup do arquivo .keystore em local seguro
# 4. Se perder o keystore, não poderá atualizar o app na Play Store
