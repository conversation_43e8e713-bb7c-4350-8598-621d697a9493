  Activity android.app  Application android.app  Bundle android.app.Activity  Context android.content  Bundle android.content.Context  Bundle android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  ActivityResultContracts !androidx.activity.result.contract  
Composable androidx.compose.animation  ExperimentalMaterial3Api androidx.compose.animation  Image androidx.compose.foundation  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  Add &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  	DateRange &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  ShoppingCart &androidx.compose.material.icons.filled  Star &androidx.compose.material.icons.filled  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
MaterialTheme androidx.compose.material3  
Typography androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  BuildConfig androidx.compose.runtime  Channel androidx.compose.runtime  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  MutableState androidx.compose.runtime  Stable androidx.compose.runtime  State androidx.compose.runtime  androidx androidx.compose.runtime  com androidx.compose.runtime  
receiveAsFlow androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  clip androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  LocalDensity androidx.compose.ui.platform  painterResource androidx.compose.ui.res  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Bundle #androidx.core.app.ComponentActivity  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  Activity androidx.lifecycle.ViewModel  
AdsRepository androidx.lifecycle.ViewModel  AuthRepository androidx.lifecycle.ViewModel  AuthUiState androidx.lifecycle.ViewModel  BillingManager androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  CloudBackupService androidx.lifecycle.ViewModel  HomeUiState androidx.lifecycle.ViewModel  InitializeAppUseCase androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  	LocalDate androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  Program androidx.lifecycle.ViewModel  ProgramDetailsUiState androidx.lifecycle.ViewModel  ProgramRepository androidx.lifecycle.ViewModel  PurchaseFormUiState androidx.lifecycle.ViewModel  PurchaseRepository androidx.lifecycle.ViewModel  SettingsRepository androidx.lifecycle.ViewModel  SettingsUiState androidx.lifecycle.ViewModel  
SplashUiState androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  ThemeManager androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  Pager androidx.paging  PagingConfig androidx.paging  
PagingData androidx.paging  PagingSource androidx.paging  cachedIn androidx.paging  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  Context androidx.room.RoomDatabase  MileageTrackerDatabase androidx.room.RoomDatabase  
ProgramDao androidx.room.RoomDatabase  PurchaseDao androidx.room.RoomDatabase  SettingsDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  
BillingClient com.android.billingclient.api  BillingClientStateListener com.android.billingclient.api  BillingConnectionState com.android.billingclient.api  
BillingResult com.android.billingclient.api  Channel com.android.billingclient.api  CoroutineScope com.android.billingclient.api  Dispatchers com.android.billingclient.api  MutableStateFlow com.android.billingclient.api  PendingPurchasesParams com.android.billingclient.api  ProductDetails com.android.billingclient.api  Purchase com.android.billingclient.api  PurchasesUpdatedListener com.android.billingclient.api  asStateFlow com.android.billingclient.api  	emptyList com.android.billingclient.api  
receiveAsFlow com.android.billingclient.api  ProductType +com.android.billingclient.api.BillingClient  
newBuilder +com.android.billingclient.api.BillingClient  build 3com.android.billingclient.api.BillingClient.Builder  enablePendingPurchases 3com.android.billingclient.api.BillingClient.Builder  setListener 3com.android.billingclient.api.BillingClient.Builder  INAPP 7com.android.billingclient.api.BillingClient.ProductType  
newBuilder 4com.android.billingclient.api.PendingPurchasesParams  build <com.android.billingclient.api.PendingPurchasesParams.Builder  enableOneTimeProducts <com.android.billingclient.api.PendingPurchasesParams.Builder  AdError com.google.android.gms.ads  	AdRequest com.google.android.gms.ads  AdSize com.google.android.gms.ads  AdView com.google.android.gms.ads  FullScreenContentCallback com.google.android.gms.ads  LoadAdError com.google.android.gms.ads  AdManagerAdRequest $com.google.android.gms.ads.admanager  AdManagerAdView $com.google.android.gms.ads.admanager  InterstitialAd 'com.google.android.gms.ads.interstitial  InterstitialAdLoadCallback 'com.google.android.gms.ads.interstitial  GoogleSignIn &com.google.android.gms.auth.api.signin  GoogleSignInOptions &com.google.android.gms.auth.api.signin  ApiException !com.google.android.gms.common.api  FirebaseAnalytics com.google.firebase.analytics  	analytics !com.google.firebase.analytics.ktx  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  GoogleAuthProvider com.google.firebase.auth  currentUser %com.google.firebase.auth.FirebaseAuth  getCURRENTUser %com.google.firebase.auth.FirebaseAuth  getCurrentUser %com.google.firebase.auth.FirebaseAuth  setCurrentUser %com.google.firebase.auth.FirebaseAuth  equals %com.google.firebase.auth.FirebaseUser  auth com.google.firebase.auth.ktx  Firebase com.google.firebase.ktx  BuildConfig com.sirch.mileagetracker  
Composable com.sirch.mileagetracker  MainActivity com.sirch.mileagetracker  MileageTrackerApplication com.sirch.mileagetracker  MileageTrackerPreview com.sirch.mileagetracker  R com.sirch.mileagetracker  DEBUG $com.sirch.mileagetracker.BuildConfig  Bundle %com.sirch.mileagetracker.MainActivity  AuthRepository "com.sirch.mileagetracker.data.auth  Boolean "com.sirch.mileagetracker.data.auth  Result "com.sirch.mileagetracker.data.auth  String "com.sirch.mileagetracker.data.auth  Unit "com.sirch.mileagetracker.data.auth  Boolean 1com.sirch.mileagetracker.data.auth.AuthRepository  FirebaseAuth 1com.sirch.mileagetracker.data.auth.AuthRepository  FirebaseUser 1com.sirch.mileagetracker.data.auth.AuthRepository  Flow 1com.sirch.mileagetracker.data.auth.AuthRepository  Inject 1com.sirch.mileagetracker.data.auth.AuthRepository  Result 1com.sirch.mileagetracker.data.auth.AuthRepository  String 1com.sirch.mileagetracker.data.auth.AuthRepository  Unit 1com.sirch.mileagetracker.data.auth.AuthRepository  currentUser 1com.sirch.mileagetracker.data.auth.AuthRepository  firebaseAuth 1com.sirch.mileagetracker.data.auth.AuthRepository  
BillingClient %com.sirch.mileagetracker.data.billing  BillingClientStateListener %com.sirch.mileagetracker.data.billing  BillingConnectionState %com.sirch.mileagetracker.data.billing  BillingManager %com.sirch.mileagetracker.data.billing  
BillingResult %com.sirch.mileagetracker.data.billing  Boolean %com.sirch.mileagetracker.data.billing  Channel %com.sirch.mileagetracker.data.billing  CoroutineScope %com.sirch.mileagetracker.data.billing  Dispatchers %com.sirch.mileagetracker.data.billing  List %com.sirch.mileagetracker.data.billing  MutableList %com.sirch.mileagetracker.data.billing  MutableStateFlow %com.sirch.mileagetracker.data.billing  PendingPurchasesParams %com.sirch.mileagetracker.data.billing  ProductDetails %com.sirch.mileagetracker.data.billing  Purchase %com.sirch.mileagetracker.data.billing  PurchasesUpdatedListener %com.sirch.mileagetracker.data.billing  String %com.sirch.mileagetracker.data.billing  asStateFlow %com.sirch.mileagetracker.data.billing  	emptyList %com.sirch.mileagetracker.data.billing  
receiveAsFlow %com.sirch.mileagetracker.data.billing  Activity 4com.sirch.mileagetracker.data.billing.BillingManager  
AdsRepository 4com.sirch.mileagetracker.data.billing.BillingManager  ApplicationContext 4com.sirch.mileagetracker.data.billing.BillingManager  
BillingClient 4com.sirch.mileagetracker.data.billing.BillingManager  BillingConnectionState 4com.sirch.mileagetracker.data.billing.BillingManager  
BillingResult 4com.sirch.mileagetracker.data.billing.BillingManager  Boolean 4com.sirch.mileagetracker.data.billing.BillingManager  Channel 4com.sirch.mileagetracker.data.billing.BillingManager  Context 4com.sirch.mileagetracker.data.billing.BillingManager  CoroutineScope 4com.sirch.mileagetracker.data.billing.BillingManager  Dispatchers 4com.sirch.mileagetracker.data.billing.BillingManager  Flow 4com.sirch.mileagetracker.data.billing.BillingManager  Inject 4com.sirch.mileagetracker.data.billing.BillingManager  List 4com.sirch.mileagetracker.data.billing.BillingManager  MutableList 4com.sirch.mileagetracker.data.billing.BillingManager  MutableStateFlow 4com.sirch.mileagetracker.data.billing.BillingManager  PendingPurchasesParams 4com.sirch.mileagetracker.data.billing.BillingManager  ProductDetails 4com.sirch.mileagetracker.data.billing.BillingManager  Purchase 4com.sirch.mileagetracker.data.billing.BillingManager  
PurchaseEvent 4com.sirch.mileagetracker.data.billing.BillingManager  	StateFlow 4com.sirch.mileagetracker.data.billing.BillingManager  String 4com.sirch.mileagetracker.data.billing.BillingManager  _connectionState 4com.sirch.mileagetracker.data.billing.BillingManager  _productDetails 4com.sirch.mileagetracker.data.billing.BillingManager  _purchaseEvents 4com.sirch.mileagetracker.data.billing.BillingManager  asStateFlow 4com.sirch.mileagetracker.data.billing.BillingManager  context 4com.sirch.mileagetracker.data.billing.BillingManager  	emptyList 4com.sirch.mileagetracker.data.billing.BillingManager  getASStateFlow 4com.sirch.mileagetracker.data.billing.BillingManager  getAsStateFlow 4com.sirch.mileagetracker.data.billing.BillingManager  getEMPTYList 4com.sirch.mileagetracker.data.billing.BillingManager  getEmptyList 4com.sirch.mileagetracker.data.billing.BillingManager  getRECEIVEAsFlow 4com.sirch.mileagetracker.data.billing.BillingManager  getReceiveAsFlow 4com.sirch.mileagetracker.data.billing.BillingManager  invoke 4com.sirch.mileagetracker.data.billing.BillingManager  
receiveAsFlow 4com.sirch.mileagetracker.data.billing.BillingManager  BillingConnectionState Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState  DISCONNECTED Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState  String Kcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState  String Qcom.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState.ERROR  Activity >com.sirch.mileagetracker.data.billing.BillingManager.Companion  
AdsRepository >com.sirch.mileagetracker.data.billing.BillingManager.Companion  ApplicationContext >com.sirch.mileagetracker.data.billing.BillingManager.Companion  
BillingClient >com.sirch.mileagetracker.data.billing.BillingManager.Companion  BillingConnectionState >com.sirch.mileagetracker.data.billing.BillingManager.Companion  
BillingResult >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Boolean >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Channel >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Context >com.sirch.mileagetracker.data.billing.BillingManager.Companion  CoroutineScope >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Dispatchers >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Flow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Inject >com.sirch.mileagetracker.data.billing.BillingManager.Companion  List >com.sirch.mileagetracker.data.billing.BillingManager.Companion  MutableList >com.sirch.mileagetracker.data.billing.BillingManager.Companion  MutableStateFlow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  PendingPurchasesParams >com.sirch.mileagetracker.data.billing.BillingManager.Companion  ProductDetails >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Purchase >com.sirch.mileagetracker.data.billing.BillingManager.Companion  	StateFlow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  String >com.sirch.mileagetracker.data.billing.BillingManager.Companion  asStateFlow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  	emptyList >com.sirch.mileagetracker.data.billing.BillingManager.Companion  getASStateFlow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  getAsStateFlow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  getEMPTYList >com.sirch.mileagetracker.data.billing.BillingManager.Companion  getEmptyList >com.sirch.mileagetracker.data.billing.BillingManager.Companion  getRECEIVEAsFlow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  getReceiveAsFlow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  invoke >com.sirch.mileagetracker.data.billing.BillingManager.Companion  
receiveAsFlow >com.sirch.mileagetracker.data.billing.BillingManager.Companion  Purchase Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent  
PurchaseEvent Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent  String Bcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent  String Tcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent.PurchaseCancelled  Purchase Tcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent.PurchaseCompleted  String Qcom.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent.PurchaseFailed  Boolean #com.sirch.mileagetracker.data.cloud  CloudBackupService #com.sirch.mileagetracker.data.cloud  Int #com.sirch.mileagetracker.data.cloud  String #com.sirch.mileagetracker.data.cloud  BackupResult 6com.sirch.mileagetracker.data.cloud.CloudBackupService  Boolean 6com.sirch.mileagetracker.data.cloud.CloudBackupService  FirebaseAuth 6com.sirch.mileagetracker.data.cloud.CloudBackupService  Inject 6com.sirch.mileagetracker.data.cloud.CloudBackupService  Int 6com.sirch.mileagetracker.data.cloud.CloudBackupService  
RestoreResult 6com.sirch.mileagetracker.data.cloud.CloudBackupService  SettingsRepository 6com.sirch.mileagetracker.data.cloud.CloudBackupService  String 6com.sirch.mileagetracker.data.cloud.CloudBackupService  BackupResult Ccom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult  String Ccom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult  String Icom.sirch.mileagetracker.data.cloud.CloudBackupService.BackupResult.Error  Boolean @com.sirch.mileagetracker.data.cloud.CloudBackupService.Companion  FirebaseAuth @com.sirch.mileagetracker.data.cloud.CloudBackupService.Companion  Inject @com.sirch.mileagetracker.data.cloud.CloudBackupService.Companion  Int @com.sirch.mileagetracker.data.cloud.CloudBackupService.Companion  SettingsRepository @com.sirch.mileagetracker.data.cloud.CloudBackupService.Companion  String @com.sirch.mileagetracker.data.cloud.CloudBackupService.Companion  Int Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult  
RestoreResult Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult  String Dcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult  String Jcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult.Error  Int Lcom.sirch.mileagetracker.data.cloud.CloudBackupService.RestoreResult.Success  
DateConverter #com.sirch.mileagetracker.data.local  MileageTrackerDatabase #com.sirch.mileagetracker.data.local  Program #com.sirch.mileagetracker.data.local  Purchase #com.sirch.mileagetracker.data.local  Settings #com.sirch.mileagetracker.data.local  Volatile #com.sirch.mileagetracker.data.local  Context :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  MileageTrackerDatabase :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  
ProgramDao :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  PurchaseDao :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  SettingsDao :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  Volatile :com.sirch.mileagetracker.data.local.MileageTrackerDatabase  Context Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  MileageTrackerDatabase Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  
ProgramDao Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  PurchaseDao Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  SettingsDao Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  Volatile Dcom.sirch.mileagetracker.data.local.MileageTrackerDatabase.Companion  
DateConverter .com.sirch.mileagetracker.data.local.converters  String .com.sirch.mileagetracker.data.local.converters  	LocalDate <com.sirch.mileagetracker.data.local.converters.DateConverter  String <com.sirch.mileagetracker.data.local.converters.DateConverter  
TypeConverter <com.sirch.mileagetracker.data.local.converters.DateConverter  Boolean 'com.sirch.mileagetracker.data.local.dao  Dao 'com.sirch.mileagetracker.data.local.dao  Delete 'com.sirch.mileagetracker.data.local.dao  Double 'com.sirch.mileagetracker.data.local.dao  GlobalStatistics 'com.sirch.mileagetracker.data.local.dao  Insert 'com.sirch.mileagetracker.data.local.dao  Int 'com.sirch.mileagetracker.data.local.dao  List 'com.sirch.mileagetracker.data.local.dao  Long 'com.sirch.mileagetracker.data.local.dao  OnConflictStrategy 'com.sirch.mileagetracker.data.local.dao  
ProgramDao 'com.sirch.mileagetracker.data.local.dao  ProgramStatistics 'com.sirch.mileagetracker.data.local.dao  ProgramStatisticsWithName 'com.sirch.mileagetracker.data.local.dao  PurchaseDao 'com.sirch.mileagetracker.data.local.dao  Query 'com.sirch.mileagetracker.data.local.dao  SettingsDao 'com.sirch.mileagetracker.data.local.dao  String 'com.sirch.mileagetracker.data.local.dao  Update 'com.sirch.mileagetracker.data.local.dao  Double 8com.sirch.mileagetracker.data.local.dao.GlobalStatistics  Int 8com.sirch.mileagetracker.data.local.dao.GlobalStatistics  Long 8com.sirch.mileagetracker.data.local.dao.GlobalStatistics  Delete 2com.sirch.mileagetracker.data.local.dao.ProgramDao  Flow 2com.sirch.mileagetracker.data.local.dao.ProgramDao  Insert 2com.sirch.mileagetracker.data.local.dao.ProgramDao  Int 2com.sirch.mileagetracker.data.local.dao.ProgramDao  List 2com.sirch.mileagetracker.data.local.dao.ProgramDao  Long 2com.sirch.mileagetracker.data.local.dao.ProgramDao  OnConflictStrategy 2com.sirch.mileagetracker.data.local.dao.ProgramDao  Program 2com.sirch.mileagetracker.data.local.dao.ProgramDao  Query 2com.sirch.mileagetracker.data.local.dao.ProgramDao  Update 2com.sirch.mileagetracker.data.local.dao.ProgramDao  Double 9com.sirch.mileagetracker.data.local.dao.ProgramStatistics  Int 9com.sirch.mileagetracker.data.local.dao.ProgramStatistics  Long 9com.sirch.mileagetracker.data.local.dao.ProgramStatistics  Double Acom.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName  Int Acom.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName  Long Acom.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName  String Acom.sirch.mileagetracker.data.local.dao.ProgramStatisticsWithName  Delete 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  Flow 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  GlobalStatistics 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  Insert 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  Int 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  List 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  Long 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  OnConflictStrategy 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  PagingSource 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  ProgramStatistics 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  ProgramStatisticsWithName 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  Purchase 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  Query 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  Update 3com.sirch.mileagetracker.data.local.dao.PurchaseDao  Boolean 3com.sirch.mileagetracker.data.local.dao.SettingsDao  Flow 3com.sirch.mileagetracker.data.local.dao.SettingsDao  Insert 3com.sirch.mileagetracker.data.local.dao.SettingsDao  Long 3com.sirch.mileagetracker.data.local.dao.SettingsDao  OnConflictStrategy 3com.sirch.mileagetracker.data.local.dao.SettingsDao  Query 3com.sirch.mileagetracker.data.local.dao.SettingsDao  Settings 3com.sirch.mileagetracker.data.local.dao.SettingsDao  String 3com.sirch.mileagetracker.data.local.dao.SettingsDao  Update 3com.sirch.mileagetracker.data.local.dao.SettingsDao  Boolean ,com.sirch.mileagetracker.data.local.entities  Double ,com.sirch.mileagetracker.data.local.entities  Int ,com.sirch.mileagetracker.data.local.entities  Long ,com.sirch.mileagetracker.data.local.entities  Program ,com.sirch.mileagetracker.data.local.entities  Purchase ,com.sirch.mileagetracker.data.local.entities  Settings ,com.sirch.mileagetracker.data.local.entities  String ,com.sirch.mileagetracker.data.local.entities  Boolean 4com.sirch.mileagetracker.data.local.entities.Program  Long 4com.sirch.mileagetracker.data.local.entities.Program  
PrimaryKey 4com.sirch.mileagetracker.data.local.entities.Program  String 4com.sirch.mileagetracker.data.local.entities.Program  Double 5com.sirch.mileagetracker.data.local.entities.Purchase  Int 5com.sirch.mileagetracker.data.local.entities.Purchase  	LocalDate 5com.sirch.mileagetracker.data.local.entities.Purchase  Long 5com.sirch.mileagetracker.data.local.entities.Purchase  
PrimaryKey 5com.sirch.mileagetracker.data.local.entities.Purchase  String 5com.sirch.mileagetracker.data.local.entities.Purchase  Boolean 5com.sirch.mileagetracker.data.local.entities.Settings  Int 5com.sirch.mileagetracker.data.local.entities.Settings  Long 5com.sirch.mileagetracker.data.local.entities.Settings  
PrimaryKey 5com.sirch.mileagetracker.data.local.entities.Settings  String 5com.sirch.mileagetracker.data.local.entities.Settings  darkModeEnabled 5com.sirch.mileagetracker.data.local.entities.Settings  
AdsRepository (com.sirch.mileagetracker.data.repository  Boolean (com.sirch.mileagetracker.data.repository  Int (com.sirch.mileagetracker.data.repository  List (com.sirch.mileagetracker.data.repository  Long (com.sirch.mileagetracker.data.repository  ProgramRepository (com.sirch.mileagetracker.data.repository  PurchaseRepository (com.sirch.mileagetracker.data.repository  SettingsRepository (com.sirch.mileagetracker.data.repository  String (com.sirch.mileagetracker.data.repository  Boolean 6com.sirch.mileagetracker.data.repository.AdsRepository  Flow 6com.sirch.mileagetracker.data.repository.AdsRepository  Inject 6com.sirch.mileagetracker.data.repository.AdsRepository  SettingsRepository 6com.sirch.mileagetracker.data.repository.AdsRepository  
areAdsRemoved 6com.sirch.mileagetracker.data.repository.AdsRepository  Flow :com.sirch.mileagetracker.data.repository.ProgramRepository  Inject :com.sirch.mileagetracker.data.repository.ProgramRepository  Int :com.sirch.mileagetracker.data.repository.ProgramRepository  List :com.sirch.mileagetracker.data.repository.ProgramRepository  Long :com.sirch.mileagetracker.data.repository.ProgramRepository  Program :com.sirch.mileagetracker.data.repository.ProgramRepository  
ProgramDao :com.sirch.mileagetracker.data.repository.ProgramRepository  Flow ;com.sirch.mileagetracker.data.repository.PurchaseRepository  GlobalStatistics ;com.sirch.mileagetracker.data.repository.PurchaseRepository  Inject ;com.sirch.mileagetracker.data.repository.PurchaseRepository  Int ;com.sirch.mileagetracker.data.repository.PurchaseRepository  List ;com.sirch.mileagetracker.data.repository.PurchaseRepository  Long ;com.sirch.mileagetracker.data.repository.PurchaseRepository  
PagingData ;com.sirch.mileagetracker.data.repository.PurchaseRepository  ProgramStatistics ;com.sirch.mileagetracker.data.repository.PurchaseRepository  ProgramStatisticsWithName ;com.sirch.mileagetracker.data.repository.PurchaseRepository  Purchase ;com.sirch.mileagetracker.data.repository.PurchaseRepository  PurchaseDao ;com.sirch.mileagetracker.data.repository.PurchaseRepository  Boolean ;com.sirch.mileagetracker.data.repository.SettingsRepository  Flow ;com.sirch.mileagetracker.data.repository.SettingsRepository  Inject ;com.sirch.mileagetracker.data.repository.SettingsRepository  Long ;com.sirch.mileagetracker.data.repository.SettingsRepository  Settings ;com.sirch.mileagetracker.data.repository.SettingsRepository  SettingsDao ;com.sirch.mileagetracker.data.repository.SettingsRepository  String ;com.sirch.mileagetracker.data.repository.SettingsRepository  getSettings ;com.sirch.mileagetracker.data.repository.SettingsRepository  DatabaseModule com.sirch.mileagetracker.di  FirebaseModule com.sirch.mileagetracker.di  RepositoryModule com.sirch.mileagetracker.di  SingletonComponent com.sirch.mileagetracker.di  ApplicationContext *com.sirch.mileagetracker.di.DatabaseModule  Context *com.sirch.mileagetracker.di.DatabaseModule  MileageTrackerDatabase *com.sirch.mileagetracker.di.DatabaseModule  
ProgramDao *com.sirch.mileagetracker.di.DatabaseModule  Provides *com.sirch.mileagetracker.di.DatabaseModule  PurchaseDao *com.sirch.mileagetracker.di.DatabaseModule  SettingsDao *com.sirch.mileagetracker.di.DatabaseModule  	Singleton *com.sirch.mileagetracker.di.DatabaseModule  FirebaseAnalytics *com.sirch.mileagetracker.di.FirebaseModule  FirebaseAuth *com.sirch.mileagetracker.di.FirebaseModule  Provides *com.sirch.mileagetracker.di.FirebaseModule  	Singleton *com.sirch.mileagetracker.di.FirebaseModule  
AdsRepository ,com.sirch.mileagetracker.di.RepositoryModule  ApplicationContext ,com.sirch.mileagetracker.di.RepositoryModule  AuthRepository ,com.sirch.mileagetracker.di.RepositoryModule  BillingManager ,com.sirch.mileagetracker.di.RepositoryModule  CloudBackupService ,com.sirch.mileagetracker.di.RepositoryModule  Context ,com.sirch.mileagetracker.di.RepositoryModule  FirebaseAuth ,com.sirch.mileagetracker.di.RepositoryModule  
ProgramDao ,com.sirch.mileagetracker.di.RepositoryModule  ProgramRepository ,com.sirch.mileagetracker.di.RepositoryModule  Provides ,com.sirch.mileagetracker.di.RepositoryModule  PurchaseDao ,com.sirch.mileagetracker.di.RepositoryModule  PurchaseRepository ,com.sirch.mileagetracker.di.RepositoryModule  SettingsDao ,com.sirch.mileagetracker.di.RepositoryModule  SettingsRepository ,com.sirch.mileagetracker.di.RepositoryModule  	Singleton ,com.sirch.mileagetracker.di.RepositoryModule  InitializeAppUseCase 'com.sirch.mileagetracker.domain.usecase  Inject <com.sirch.mileagetracker.domain.usecase.InitializeAppUseCase  ProgramRepository <com.sirch.mileagetracker.domain.usecase.InitializeAppUseCase  PurchaseRepository <com.sirch.mileagetracker.domain.usecase.InitializeAppUseCase  SettingsRepository <com.sirch.mileagetracker.domain.usecase.InitializeAppUseCase  AdBanner )com.sirch.mileagetracker.presentation.ads  AdBannerWithCallback )com.sirch.mileagetracker.presentation.ads  	AdUnitIds )com.sirch.mileagetracker.presentation.ads  Boolean )com.sirch.mileagetracker.presentation.ads  BuildConfig )com.sirch.mileagetracker.presentation.ads  Channel )com.sirch.mileagetracker.presentation.ads  
Composable )com.sirch.mileagetracker.presentation.ads  InterstitialAdEffect )com.sirch.mileagetracker.presentation.ads  InterstitialAdManager )com.sirch.mileagetracker.presentation.ads  String )com.sirch.mileagetracker.presentation.ads  Unit )com.sirch.mileagetracker.presentation.ads  
receiveAsFlow )com.sirch.mileagetracker.presentation.ads  rememberInterstitialAdManager )com.sirch.mileagetracker.presentation.ads  BANNER_PROD 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  BANNER_TEST 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  BuildConfig 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  INTERSTITIAL_PROD 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  INTERSTITIAL_TEST 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  IS_DEBUG 3com.sirch.mileagetracker.presentation.ads.AdUnitIds  Activity ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  AdError ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  AdEvent ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  Boolean ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  Channel ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  Context ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  InterstitialAd ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  LoadAdError ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  String ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  	_adEvents ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  getRECEIVEAsFlow ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  getReceiveAsFlow ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  invoke ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  
receiveAsFlow ?com.sirch.mileagetracker.presentation.ads.InterstitialAdManager  AdError Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent  AdEvent Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent  LoadAdError Gcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent  LoadAdError Vcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent.AdFailedToLoad  AdError Vcom.sirch.mileagetracker.presentation.ads.InterstitialAdManager.AdEvent.AdFailedToShow  AuthUiState *com.sirch.mileagetracker.presentation.auth  
AuthViewModel *com.sirch.mileagetracker.presentation.auth  BenefitItem *com.sirch.mileagetracker.presentation.auth  BenefitsSection *com.sirch.mileagetracker.presentation.auth  Boolean *com.sirch.mileagetracker.presentation.auth  
Composable *com.sirch.mileagetracker.presentation.auth  	ErrorCard *com.sirch.mileagetracker.presentation.auth  ExperimentalMaterial3Api *com.sirch.mileagetracker.presentation.auth  GoogleSignInButton *com.sirch.mileagetracker.presentation.auth  LoginScreen *com.sirch.mileagetracker.presentation.auth  MutableStateFlow *com.sirch.mileagetracker.presentation.auth  OptIn *com.sirch.mileagetracker.presentation.auth  String *com.sirch.mileagetracker.presentation.auth  Suppress *com.sirch.mileagetracker.presentation.auth  Unit *com.sirch.mileagetracker.presentation.auth  asStateFlow *com.sirch.mileagetracker.presentation.auth  Boolean 6com.sirch.mileagetracker.presentation.auth.AuthUiState  FirebaseUser 6com.sirch.mileagetracker.presentation.auth.AuthUiState  String 6com.sirch.mileagetracker.presentation.auth.AuthUiState  AuthRepository 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  AuthUiState 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  Inject 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  MutableStateFlow 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  	StateFlow 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  String 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  _uiState 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  asStateFlow 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  getASStateFlow 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  getAsStateFlow 8com.sirch.mileagetracker.presentation.auth.AuthViewModel  Boolean *com.sirch.mileagetracker.presentation.home  
Composable *com.sirch.mileagetracker.presentation.home  Double *com.sirch.mileagetracker.presentation.home  ExperimentalMaterial3Api *com.sirch.mileagetracker.presentation.home  
HomeScreen *com.sirch.mileagetracker.presentation.home  HomeUiState *com.sirch.mileagetracker.presentation.home  
HomeViewModel *com.sirch.mileagetracker.presentation.home  Int *com.sirch.mileagetracker.presentation.home  List *com.sirch.mileagetracker.presentation.home  Long *com.sirch.mileagetracker.presentation.home  MutableStateFlow *com.sirch.mileagetracker.presentation.home  OptIn *com.sirch.mileagetracker.presentation.home  ProgramCard *com.sirch.mileagetracker.presentation.home  String *com.sirch.mileagetracker.presentation.home  Unit *com.sirch.mileagetracker.presentation.home  asStateFlow *com.sirch.mileagetracker.presentation.home  com *com.sirch.mileagetracker.presentation.home  Boolean 6com.sirch.mileagetracker.presentation.home.HomeUiState  Double 6com.sirch.mileagetracker.presentation.home.HomeUiState  Int 6com.sirch.mileagetracker.presentation.home.HomeUiState  List 6com.sirch.mileagetracker.presentation.home.HomeUiState  ProgramStatisticsWithName 6com.sirch.mileagetracker.presentation.home.HomeUiState  String 6com.sirch.mileagetracker.presentation.home.HomeUiState  
AdsRepository 8com.sirch.mileagetracker.presentation.home.HomeViewModel  HomeUiState 8com.sirch.mileagetracker.presentation.home.HomeViewModel  Inject 8com.sirch.mileagetracker.presentation.home.HomeViewModel  MutableStateFlow 8com.sirch.mileagetracker.presentation.home.HomeViewModel  PurchaseRepository 8com.sirch.mileagetracker.presentation.home.HomeViewModel  	StateFlow 8com.sirch.mileagetracker.presentation.home.HomeViewModel  _uiState 8com.sirch.mileagetracker.presentation.home.HomeViewModel  asStateFlow 8com.sirch.mileagetracker.presentation.home.HomeViewModel  getASStateFlow 8com.sirch.mileagetracker.presentation.home.HomeViewModel  getAsStateFlow 8com.sirch.mileagetracker.presentation.home.HomeViewModel  
Composable 0com.sirch.mileagetracker.presentation.navigation  MileageTrackerApp 0com.sirch.mileagetracker.presentation.navigation  Boolean -com.sirch.mileagetracker.presentation.program  
Composable -com.sirch.mileagetracker.presentation.program  ExperimentalMaterial3Api -com.sirch.mileagetracker.presentation.program  List -com.sirch.mileagetracker.presentation.program  Long -com.sirch.mileagetracker.presentation.program  MutableStateFlow -com.sirch.mileagetracker.presentation.program  OptIn -com.sirch.mileagetracker.presentation.program  ProgramDetailsScreen -com.sirch.mileagetracker.presentation.program  ProgramDetailsUiState -com.sirch.mileagetracker.presentation.program  ProgramDetailsViewModel -com.sirch.mileagetracker.presentation.program  PurchaseCard -com.sirch.mileagetracker.presentation.program  
StatisticItem -com.sirch.mileagetracker.presentation.program  String -com.sirch.mileagetracker.presentation.program  Unit -com.sirch.mileagetracker.presentation.program  asStateFlow -com.sirch.mileagetracker.presentation.program  Boolean Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  List Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  Program Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  ProgramStatistics Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  Purchase Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  String Ccom.sirch.mileagetracker.presentation.program.ProgramDetailsUiState  Inject Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  Long Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  MutableStateFlow Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  ProgramDetailsUiState Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  ProgramRepository Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  PurchaseRepository Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  	StateFlow Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  _uiState Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  asStateFlow Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  getASStateFlow Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  getAsStateFlow Ecom.sirch.mileagetracker.presentation.program.ProgramDetailsViewModel  Boolean .com.sirch.mileagetracker.presentation.purchase  
Composable .com.sirch.mileagetracker.presentation.purchase  	DateField .com.sirch.mileagetracker.presentation.purchase  ExperimentalMaterial3Api .com.sirch.mileagetracker.presentation.purchase  List .com.sirch.mileagetracker.presentation.purchase  Long .com.sirch.mileagetracker.presentation.purchase  MutableStateFlow .com.sirch.mileagetracker.presentation.purchase  OptIn .com.sirch.mileagetracker.presentation.purchase  ProgramDropdown .com.sirch.mileagetracker.presentation.purchase  PurchaseFormScreen .com.sirch.mileagetracker.presentation.purchase  PurchaseFormUiState .com.sirch.mileagetracker.presentation.purchase  PurchaseFormViewModel .com.sirch.mileagetracker.presentation.purchase  String .com.sirch.mileagetracker.presentation.purchase  Unit .com.sirch.mileagetracker.presentation.purchase  asStateFlow .com.sirch.mileagetracker.presentation.purchase  Boolean Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  List Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  	LocalDate Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  Program Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  String Bcom.sirch.mileagetracker.presentation.purchase.PurchaseFormUiState  Boolean Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  Inject Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  	LocalDate Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  Long Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  MutableStateFlow Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  Program Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  ProgramRepository Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  PurchaseFormUiState Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  PurchaseRepository Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  	StateFlow Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  String Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  _uiState Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  asStateFlow Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  getASStateFlow Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  getAsStateFlow Dcom.sirch.mileagetracker.presentation.purchase.PurchaseFormViewModel  Boolean .com.sirch.mileagetracker.presentation.settings  
Composable .com.sirch.mileagetracker.presentation.settings  ExperimentalMaterial3Api .com.sirch.mileagetracker.presentation.settings  MutableStateFlow .com.sirch.mileagetracker.presentation.settings  OptIn .com.sirch.mileagetracker.presentation.settings  SettingsScreen .com.sirch.mileagetracker.presentation.settings  SettingsUiState .com.sirch.mileagetracker.presentation.settings  SettingsViewModel .com.sirch.mileagetracker.presentation.settings  String .com.sirch.mileagetracker.presentation.settings  Unit .com.sirch.mileagetracker.presentation.settings  asStateFlow .com.sirch.mileagetracker.presentation.settings  Boolean >com.sirch.mileagetracker.presentation.settings.SettingsUiState  String >com.sirch.mileagetracker.presentation.settings.SettingsUiState  Activity @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  
AdsRepository @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  AuthRepository @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  BillingManager @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  CloudBackupService @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  Inject @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  MutableStateFlow @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  SettingsRepository @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  SettingsUiState @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  	StateFlow @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  _uiState @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  asStateFlow @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  getASStateFlow @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  getAsStateFlow @com.sirch.mileagetracker.presentation.settings.SettingsViewModel  Boolean ,com.sirch.mileagetracker.presentation.splash  
Composable ,com.sirch.mileagetracker.presentation.splash  MutableStateFlow ,com.sirch.mileagetracker.presentation.splash  SplashScreen ,com.sirch.mileagetracker.presentation.splash  
SplashUiState ,com.sirch.mileagetracker.presentation.splash  SplashViewModel ,com.sirch.mileagetracker.presentation.splash  String ,com.sirch.mileagetracker.presentation.splash  Unit ,com.sirch.mileagetracker.presentation.splash  asStateFlow ,com.sirch.mileagetracker.presentation.splash  Boolean :com.sirch.mileagetracker.presentation.splash.SplashUiState  String :com.sirch.mileagetracker.presentation.splash.SplashUiState  InitializeAppUseCase <com.sirch.mileagetracker.presentation.splash.SplashViewModel  Inject <com.sirch.mileagetracker.presentation.splash.SplashViewModel  MutableStateFlow <com.sirch.mileagetracker.presentation.splash.SplashViewModel  
SplashUiState <com.sirch.mileagetracker.presentation.splash.SplashViewModel  	StateFlow <com.sirch.mileagetracker.presentation.splash.SplashViewModel  _uiState <com.sirch.mileagetracker.presentation.splash.SplashViewModel  asStateFlow <com.sirch.mileagetracker.presentation.splash.SplashViewModel  getASStateFlow <com.sirch.mileagetracker.presentation.splash.SplashViewModel  getAsStateFlow <com.sirch.mileagetracker.presentation.splash.SplashViewModel  Boolean +com.sirch.mileagetracker.presentation.theme  ThemeManager +com.sirch.mileagetracker.presentation.theme  
ThemeState +com.sirch.mileagetracker.presentation.theme  ThemeViewModel +com.sirch.mileagetracker.presentation.theme  combine +com.sirch.mileagetracker.presentation.theme  
AdsRepository 8com.sirch.mileagetracker.presentation.theme.ThemeManager  Boolean 8com.sirch.mileagetracker.presentation.theme.ThemeManager  Inject 8com.sirch.mileagetracker.presentation.theme.ThemeManager  SettingsRepository 8com.sirch.mileagetracker.presentation.theme.ThemeManager  
ThemeState 8com.sirch.mileagetracker.presentation.theme.ThemeManager  
adsRepository 8com.sirch.mileagetracker.presentation.theme.ThemeManager  combine 8com.sirch.mileagetracker.presentation.theme.ThemeManager  
getCOMBINE 8com.sirch.mileagetracker.presentation.theme.ThemeManager  
getCombine 8com.sirch.mileagetracker.presentation.theme.ThemeManager  settingsRepository 8com.sirch.mileagetracker.presentation.theme.ThemeManager  Boolean 6com.sirch.mileagetracker.presentation.theme.ThemeState  Boolean :com.sirch.mileagetracker.presentation.theme.ThemeViewModel  Inject :com.sirch.mileagetracker.presentation.theme.ThemeViewModel  ThemeManager :com.sirch.mileagetracker.presentation.theme.ThemeViewModel  Boolean !com.sirch.mileagetracker.ui.theme  DarkColorScheme !com.sirch.mileagetracker.ui.theme  LightColorScheme !com.sirch.mileagetracker.ui.theme  MileageTrackerTheme !com.sirch.mileagetracker.ui.theme  Pink40 !com.sirch.mileagetracker.ui.theme  Pink80 !com.sirch.mileagetracker.ui.theme  Purple40 !com.sirch.mileagetracker.ui.theme  Purple80 !com.sirch.mileagetracker.ui.theme  PurpleGrey40 !com.sirch.mileagetracker.ui.theme  PurpleGrey80 !com.sirch.mileagetracker.ui.theme  
Typography !com.sirch.mileagetracker.ui.theme  Unit !com.sirch.mileagetracker.ui.theme  Any com.sirch.mileagetracker.utils  Boolean com.sirch.mileagetracker.utils  
Composable com.sirch.mileagetracker.utils  
DatabaseUtils com.sirch.mileagetracker.utils  Float com.sirch.mileagetracker.utils  ImageLoadingState com.sirch.mileagetracker.utils  Int com.sirch.mileagetracker.utils  List com.sirch.mileagetracker.utils  Long com.sirch.mileagetracker.utils  MutableState com.sirch.mileagetracker.utils  Stable com.sirch.mileagetracker.utils  StableHolder com.sirch.mileagetracker.utils  StableLambda com.sirch.mileagetracker.utils  State com.sirch.mileagetracker.utils  String com.sirch.mileagetracker.utils  androidx com.sirch.mileagetracker.utils  generateStableKey com.sirch.mileagetracker.utils  rememberCached com.sirch.mileagetracker.utils  rememberContentPadding com.sirch.mileagetracker.utils  rememberDebouncedState com.sirch.mileagetracker.utils  rememberDerivedState com.sirch.mileagetracker.utils  rememberStableHolder com.sirch.mileagetracker.utils  rememberStableLambda com.sirch.mileagetracker.utils  rememberStableList com.sirch.mileagetracker.utils  
toPxCached com.sirch.mileagetracker.utils  Context ,com.sirch.mileagetracker.utils.DatabaseUtils  Boolean 0com.sirch.mileagetracker.utils.ImageLoadingState  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  File java.io  AuthUiState 	java.lang  
BillingClient 	java.lang  BillingConnectionState 	java.lang  BuildConfig 	java.lang  Channel 	java.lang  CoroutineScope 	java.lang  
DateConverter 	java.lang  Dispatchers 	java.lang  ExperimentalMaterial3Api 	java.lang  HomeUiState 	java.lang  MutableStateFlow 	java.lang  OnConflictStrategy 	java.lang  PendingPurchasesParams 	java.lang  Program 	java.lang  ProgramDetailsUiState 	java.lang  Purchase 	java.lang  PurchaseFormUiState 	java.lang  Settings 	java.lang  SettingsUiState 	java.lang  SingletonComponent 	java.lang  
SplashUiState 	java.lang  
ThemeState 	java.lang  androidx 	java.lang  asStateFlow 	java.lang  com 	java.lang  combine 	java.lang  	emptyList 	java.lang  
receiveAsFlow 	java.lang  Inject javax.inject  	Singleton javax.inject  Any kotlin  Array kotlin  AuthUiState kotlin  
BillingClient kotlin  BillingConnectionState kotlin  Boolean kotlin  BuildConfig kotlin  Channel kotlin  CoroutineScope kotlin  
DateConverter kotlin  Dispatchers kotlin  Double kotlin  ExperimentalMaterial3Api kotlin  Float kotlin  HomeUiState kotlin  Int kotlin  Long kotlin  MutableStateFlow kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  PendingPurchasesParams kotlin  Program kotlin  ProgramDetailsUiState kotlin  Purchase kotlin  PurchaseFormUiState kotlin  Result kotlin  Settings kotlin  SettingsUiState kotlin  SingletonComponent kotlin  
SplashUiState kotlin  String kotlin  Suppress kotlin  
ThemeState kotlin  Unit kotlin  Volatile kotlin  androidx kotlin  arrayOf kotlin  asStateFlow kotlin  com kotlin  combine kotlin  	emptyList kotlin  
receiveAsFlow kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  AuthUiState kotlin.annotation  
BillingClient kotlin.annotation  BillingConnectionState kotlin.annotation  BuildConfig kotlin.annotation  Channel kotlin.annotation  CoroutineScope kotlin.annotation  
DateConverter kotlin.annotation  Dispatchers kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  HomeUiState kotlin.annotation  MutableStateFlow kotlin.annotation  OnConflictStrategy kotlin.annotation  PendingPurchasesParams kotlin.annotation  Program kotlin.annotation  ProgramDetailsUiState kotlin.annotation  Purchase kotlin.annotation  PurchaseFormUiState kotlin.annotation  Result kotlin.annotation  Settings kotlin.annotation  SettingsUiState kotlin.annotation  SingletonComponent kotlin.annotation  
SplashUiState kotlin.annotation  
ThemeState kotlin.annotation  Volatile kotlin.annotation  androidx kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  combine kotlin.annotation  	emptyList kotlin.annotation  
receiveAsFlow kotlin.annotation  AuthUiState kotlin.collections  
BillingClient kotlin.collections  BillingConnectionState kotlin.collections  BuildConfig kotlin.collections  Channel kotlin.collections  CoroutineScope kotlin.collections  
DateConverter kotlin.collections  Dispatchers kotlin.collections  ExperimentalMaterial3Api kotlin.collections  HomeUiState kotlin.collections  List kotlin.collections  MutableList kotlin.collections  MutableStateFlow kotlin.collections  OnConflictStrategy kotlin.collections  PendingPurchasesParams kotlin.collections  Program kotlin.collections  ProgramDetailsUiState kotlin.collections  Purchase kotlin.collections  PurchaseFormUiState kotlin.collections  Result kotlin.collections  Settings kotlin.collections  SettingsUiState kotlin.collections  SingletonComponent kotlin.collections  
SplashUiState kotlin.collections  
ThemeState kotlin.collections  Volatile kotlin.collections  androidx kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  combine kotlin.collections  	emptyList kotlin.collections  
receiveAsFlow kotlin.collections  AuthUiState kotlin.comparisons  
BillingClient kotlin.comparisons  BillingConnectionState kotlin.comparisons  BuildConfig kotlin.comparisons  Channel kotlin.comparisons  CoroutineScope kotlin.comparisons  
DateConverter kotlin.comparisons  Dispatchers kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  HomeUiState kotlin.comparisons  MutableStateFlow kotlin.comparisons  OnConflictStrategy kotlin.comparisons  PendingPurchasesParams kotlin.comparisons  Program kotlin.comparisons  ProgramDetailsUiState kotlin.comparisons  Purchase kotlin.comparisons  PurchaseFormUiState kotlin.comparisons  Result kotlin.comparisons  Settings kotlin.comparisons  SettingsUiState kotlin.comparisons  SingletonComponent kotlin.comparisons  
SplashUiState kotlin.comparisons  
ThemeState kotlin.comparisons  Volatile kotlin.comparisons  androidx kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  combine kotlin.comparisons  	emptyList kotlin.comparisons  
receiveAsFlow kotlin.comparisons  SuspendFunction2 kotlin.coroutines  resume kotlin.coroutines  AuthUiState 	kotlin.io  
BillingClient 	kotlin.io  BillingConnectionState 	kotlin.io  BuildConfig 	kotlin.io  Channel 	kotlin.io  CoroutineScope 	kotlin.io  
DateConverter 	kotlin.io  Dispatchers 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  HomeUiState 	kotlin.io  MutableStateFlow 	kotlin.io  OnConflictStrategy 	kotlin.io  PendingPurchasesParams 	kotlin.io  Program 	kotlin.io  ProgramDetailsUiState 	kotlin.io  Purchase 	kotlin.io  PurchaseFormUiState 	kotlin.io  Result 	kotlin.io  Settings 	kotlin.io  SettingsUiState 	kotlin.io  SingletonComponent 	kotlin.io  
SplashUiState 	kotlin.io  
ThemeState 	kotlin.io  Volatile 	kotlin.io  androidx 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  combine 	kotlin.io  	emptyList 	kotlin.io  
receiveAsFlow 	kotlin.io  AuthUiState 
kotlin.jvm  
BillingClient 
kotlin.jvm  BillingConnectionState 
kotlin.jvm  BuildConfig 
kotlin.jvm  Channel 
kotlin.jvm  CoroutineScope 
kotlin.jvm  
DateConverter 
kotlin.jvm  Dispatchers 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  HomeUiState 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  PendingPurchasesParams 
kotlin.jvm  Program 
kotlin.jvm  ProgramDetailsUiState 
kotlin.jvm  Purchase 
kotlin.jvm  PurchaseFormUiState 
kotlin.jvm  Result 
kotlin.jvm  Settings 
kotlin.jvm  SettingsUiState 
kotlin.jvm  SingletonComponent 
kotlin.jvm  
SplashUiState 
kotlin.jvm  
ThemeState 
kotlin.jvm  Volatile 
kotlin.jvm  androidx 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  combine 
kotlin.jvm  	emptyList 
kotlin.jvm  
receiveAsFlow 
kotlin.jvm  AuthUiState 
kotlin.ranges  
BillingClient 
kotlin.ranges  BillingConnectionState 
kotlin.ranges  BuildConfig 
kotlin.ranges  Channel 
kotlin.ranges  CoroutineScope 
kotlin.ranges  
DateConverter 
kotlin.ranges  Dispatchers 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  HomeUiState 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  PendingPurchasesParams 
kotlin.ranges  Program 
kotlin.ranges  ProgramDetailsUiState 
kotlin.ranges  Purchase 
kotlin.ranges  PurchaseFormUiState 
kotlin.ranges  Result 
kotlin.ranges  Settings 
kotlin.ranges  SettingsUiState 
kotlin.ranges  SingletonComponent 
kotlin.ranges  
SplashUiState 
kotlin.ranges  
ThemeState 
kotlin.ranges  Volatile 
kotlin.ranges  androidx 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  combine 
kotlin.ranges  	emptyList 
kotlin.ranges  
receiveAsFlow 
kotlin.ranges  KClass kotlin.reflect  AuthUiState kotlin.sequences  
BillingClient kotlin.sequences  BillingConnectionState kotlin.sequences  BuildConfig kotlin.sequences  Channel kotlin.sequences  CoroutineScope kotlin.sequences  
DateConverter kotlin.sequences  Dispatchers kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  HomeUiState kotlin.sequences  MutableStateFlow kotlin.sequences  OnConflictStrategy kotlin.sequences  PendingPurchasesParams kotlin.sequences  Program kotlin.sequences  ProgramDetailsUiState kotlin.sequences  Purchase kotlin.sequences  PurchaseFormUiState kotlin.sequences  Result kotlin.sequences  Settings kotlin.sequences  SettingsUiState kotlin.sequences  SingletonComponent kotlin.sequences  
SplashUiState kotlin.sequences  
ThemeState kotlin.sequences  Volatile kotlin.sequences  androidx kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  combine kotlin.sequences  	emptyList kotlin.sequences  
receiveAsFlow kotlin.sequences  AuthUiState kotlin.text  
BillingClient kotlin.text  BillingConnectionState kotlin.text  BuildConfig kotlin.text  Channel kotlin.text  CoroutineScope kotlin.text  
DateConverter kotlin.text  Dispatchers kotlin.text  ExperimentalMaterial3Api kotlin.text  HomeUiState kotlin.text  MutableStateFlow kotlin.text  OnConflictStrategy kotlin.text  PendingPurchasesParams kotlin.text  Program kotlin.text  ProgramDetailsUiState kotlin.text  Purchase kotlin.text  PurchaseFormUiState kotlin.text  Result kotlin.text  Settings kotlin.text  SettingsUiState kotlin.text  SingletonComponent kotlin.text  
SplashUiState kotlin.text  
ThemeState kotlin.text  Volatile kotlin.text  androidx kotlin.text  asStateFlow kotlin.text  com kotlin.text  combine kotlin.text  	emptyList kotlin.text  
receiveAsFlow kotlin.text  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  suspendCancellableCoroutine kotlinx.coroutines  Main kotlinx.coroutines.Dispatchers  Channel kotlinx.coroutines.channels  	UNLIMITED #kotlinx.coroutines.channels.Channel  getRECEIVEAsFlow #kotlinx.coroutines.channels.Channel  getReceiveAsFlow #kotlinx.coroutines.channels.Channel  
receiveAsFlow #kotlinx.coroutines.channels.Channel  	UNLIMITED +kotlinx.coroutines.channels.Channel.Factory  invoke +kotlinx.coroutines.channels.Channel.Factory  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  first kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  map kotlinx.coroutines.flow  
receiveAsFlow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  await kotlinx.coroutines.tasks  Clock kotlinx.datetime  Instant kotlinx.datetime  	LocalDate kotlinx.datetime  TimeZone kotlinx.datetime  toLocalDateTime kotlinx.datetime  todayIn kotlinx.datetime                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            