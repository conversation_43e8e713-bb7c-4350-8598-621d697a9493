package com.sirch.mileagetracker.di

import android.content.Context
import com.sirch.mileagetracker.data.auth.AuthRepository
import com.sirch.mileagetracker.data.billing.BillingManager
import com.sirch.mileagetracker.data.cloud.CloudBackupService
import com.sirch.mileagetracker.data.local.dao.ProgramDao
import com.sirch.mileagetracker.data.local.dao.PurchaseDao
import com.sirch.mileagetracker.data.local.dao.SettingsDao
import com.sirch.mileagetracker.data.repository.AdsRepository
import com.sirch.mileagetracker.data.repository.ProgramRepository
import com.sirch.mileagetracker.data.repository.PurchaseRepository
import com.sirch.mileagetracker.data.repository.SettingsRepository

import com.google.firebase.auth.FirebaseAuth
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object RepositoryModule {

    @Provides
    @Singleton
    fun provideProgramRepository(programDao: ProgramDao): ProgramRepository {
        return ProgramRepository(programDao)
    }

    @Provides
    @Singleton
    fun providePurchaseRepository(purchaseDao: PurchaseDao): PurchaseRepository {
        return PurchaseRepository(purchaseDao)
    }

    @Provides
    @Singleton
    fun provideSettingsRepository(settingsDao: SettingsDao): SettingsRepository {
        return SettingsRepository(settingsDao)
    }

    @Provides
    @Singleton
    fun provideAuthRepository(firebaseAuth: FirebaseAuth): AuthRepository {
        return AuthRepository(firebaseAuth)
    }

    @Provides
    @Singleton
    fun provideAdsRepository(settingsRepository: SettingsRepository): AdsRepository {
        return AdsRepository(settingsRepository)
    }

    @Provides
    @Singleton
    fun provideBillingManager(
        @ApplicationContext context: Context,
        adsRepository: AdsRepository
    ): BillingManager {
        return BillingManager(context, adsRepository)
    }

    @Provides
    @Singleton
    fun provideCloudBackupService(
        firebaseAuth: FirebaseAuth,
        settingsRepository: SettingsRepository
    ): CloudBackupService {
        return CloudBackupService(firebaseAuth, settingsRepository)
    }

}
