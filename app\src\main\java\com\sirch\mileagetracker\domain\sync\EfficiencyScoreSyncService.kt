package com.sirch.mileagetracker.domain.sync

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.*
import com.sirch.mileagetracker.data.auth.AuthRepository
import com.sirch.mileagetracker.domain.aggregation.DataAggregationService
import com.sirch.mileagetracker.domain.efficiency.EfficiencyScoreEngine
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit

/**
 * Background service for syncing efficiency scores and aggregate data.
 * Runs daily to update aggregate statistics and refresh efficiency scores.
 * 
 * Features:
 * - Daily background sync of aggregate data
 * - Automatic efficiency score recalculation
 * - Network-aware execution
 * - Battery optimization friendly
 */
@HiltWorker
class EfficiencyScoreSyncWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val efficiencyScoreEngine: EfficiencyScoreEngine,
    private val dataAggregationService: DataAggregationService,
    private val authRepository: AuthRepository
) : CoroutineWorker(context, workerParams) {

    companion object {
        const val WORK_NAME = "efficiency_score_sync"
        const val TAG_DAILY_SYNC = "daily_sync"
        const val TAG_IMMEDIATE_SYNC = "immediate_sync"
        
        private const val MAX_RETRY_ATTEMPTS = 3
        private const val BACKOFF_DELAY_MINUTES = 15L
    }

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            // Check if user is authenticated
            val userId = authRepository.getUserId()
            if (userId == null) {
                return@withContext Result.success()
            }

            // Update aggregate data for all programs
            dataAggregationService.refreshAllAggregateData()

            // Get statistics for monitoring
            val stats = dataAggregationService.getAggregateDataStatistics()
            
            // Log sync completion
            println("Efficiency score sync completed. Programs: ${stats?.totalPrograms}, Records: ${stats?.totalRecords}")

            Result.success()

        } catch (e: Exception) {
            println("Efficiency score sync failed: ${e.message}")
            
            // Retry on failure with exponential backoff
            if (runAttemptCount < MAX_RETRY_ATTEMPTS) {
                Result.retry()
            } else {
                Result.failure()
            }
        }
    }
}

/**
 * Service for managing efficiency score background synchronization
 */
class EfficiencyScoreSyncService @AssistedInject constructor(
    private val workManager: WorkManager
) {

    /**
     * Schedule daily background sync
     */
    fun scheduleDailySync() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .setRequiresBatteryNotLow(true)
            .setRequiresDeviceIdle(false)
            .build()

        val dailySyncRequest = PeriodicWorkRequestBuilder<EfficiencyScoreSyncWorker>(
            repeatInterval = 1,
            repeatIntervalTimeUnit = TimeUnit.DAYS,
            flexTimeInterval = 4,
            flexTimeIntervalUnit = TimeUnit.HOURS
        )
            .setConstraints(constraints)
            .addTag(EfficiencyScoreSyncWorker.TAG_DAILY_SYNC)
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                EfficiencyScoreSyncWorker.BACKOFF_DELAY_MINUTES,
                TimeUnit.MINUTES
            )
            .build()

        workManager.enqueueUniquePeriodicWork(
            EfficiencyScoreSyncWorker.WORK_NAME,
            ExistingPeriodicWorkPolicy.KEEP,
            dailySyncRequest
        )
    }

    /**
     * Trigger immediate sync (e.g., after new purchase)
     */
    fun triggerImmediateSync() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val immediateSyncRequest = OneTimeWorkRequestBuilder<EfficiencyScoreSyncWorker>()
            .setConstraints(constraints)
            .addTag(EfficiencyScoreSyncWorker.TAG_IMMEDIATE_SYNC)
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                EfficiencyScoreSyncWorker.BACKOFF_DELAY_MINUTES,
                TimeUnit.MINUTES
            )
            .build()

        workManager.enqueue(immediateSyncRequest)
    }

    /**
     * Cancel all sync work
     */
    fun cancelSync() {
        workManager.cancelUniqueWork(EfficiencyScoreSyncWorker.WORK_NAME)
        workManager.cancelAllWorkByTag(EfficiencyScoreSyncWorker.TAG_IMMEDIATE_SYNC)
    }

    /**
     * Get sync status
     */
    fun getSyncStatus(): androidx.lifecycle.LiveData<List<WorkInfo>> {
        return workManager.getWorkInfosByTagLiveData(EfficiencyScoreSyncWorker.TAG_DAILY_SYNC)
    }
}

/**
 * Manager for coordinating efficiency score operations
 */
class EfficiencyScoreManager @AssistedInject constructor(
    private val efficiencyScoreEngine: EfficiencyScoreEngine,
    private val syncService: EfficiencyScoreSyncService,
    private val authRepository: AuthRepository
) {

    /**
     * Initialize efficiency score system
     */
    fun initialize() {
        // Schedule daily background sync
        syncService.scheduleDailySync()
    }

    /**
     * Handle new purchase - recalculate scores and trigger sync
     */
    suspend fun onPurchaseAdded(programId: Long) {
        try {
            // Recalculate efficiency scores for the program
            efficiencyScoreEngine.recalculateScoresAfterPurchase(programId)
            
            // Trigger background sync to update aggregate data
            syncService.triggerImmediateSync()
            
        } catch (e: Exception) {
            println("Error handling new purchase: ${e.message}")
        }
    }

    /**
     * Handle purchase update - recalculate scores
     */
    suspend fun onPurchaseUpdated(programId: Long) {
        onPurchaseAdded(programId) // Same logic as new purchase
    }

    /**
     * Handle purchase deletion - recalculate scores
     */
    suspend fun onPurchaseDeleted(programId: Long) {
        onPurchaseAdded(programId) // Same logic as new purchase
    }

    /**
     * Get efficiency score for a program
     */
    suspend fun getEfficiencyScore(programId: Long) = 
        efficiencyScoreEngine.getEfficiencyScore(programId)

    /**
     * Get all efficiency scores for current user
     */
    fun getAllEfficiencyScores() = 
        efficiencyScoreEngine.getAllEfficiencyScores()

    /**
     * Force refresh of efficiency scores
     */
    suspend fun refreshEfficiencyScores() {
        val userId = authRepository.getUserId() ?: return
        
        try {
            // Trigger immediate sync
            syncService.triggerImmediateSync()
            
        } catch (e: Exception) {
            println("Error refreshing efficiency scores: ${e.message}")
        }
    }

    /**
     * Cleanup when user signs out
     */
    fun onUserSignOut() {
        syncService.cancelSync()
    }
}
