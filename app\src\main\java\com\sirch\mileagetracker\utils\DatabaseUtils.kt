package com.sirch.mileagetracker.utils

import android.content.Context
import java.io.File

object DatabaseUtils {
    
    /**
     * Limpa completamente o banco de dados e força uma recriação
     */
    fun clearDatabase(context: Context) {
        try {
            // Lista de possíveis nomes de banco para deletar
            val databaseNames = listOf(
                "mileage_tracker_database",
                "mileage_tracker_database_v2"
            )
            
            databaseNames.forEach { dbName ->
                // Deleta o arquivo principal do banco
                val dbFile = context.getDatabasePath(dbName)
                if (dbFile.exists()) {
                    dbFile.delete()
                }
                
                // Deleta arquivos relacionados (WAL, SHM)
                val walFile = File(dbFile.path + "-wal")
                if (walFile.exists()) {
                    walFile.delete()
                }
                
                val shmFile = File(dbFile.path + "-shm")
                if (shmFile.exists()) {
                    shmFile.delete()
                }
            }
            
            // Limpa também o cache de shared preferences se necessário
            val sharedPrefs = context.getSharedPreferences("room_master_table", Context.MODE_PRIVATE)
            sharedPrefs.edit().clear().apply()
            
        } catch (e: Exception) {
            // Ignora erros de limpeza
            e.printStackTrace()
        }
    }
}
