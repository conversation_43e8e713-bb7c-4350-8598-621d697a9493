{"logs": [{"outputFile": "com.sirch.mileagetracker.app-mergeReleaseResources-3:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\51f776a662999a2c4066cf59dc47f5c4\\transformed\\play-services-basement-18.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2313", "endColumns": "160", "endOffsets": "2469"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\4f87b4fe7e5f13faf091c8d26d70e189\\transformed\\material3-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,347,469,617,743,837,949,1091,1210,1369,1453,1554,1655,1756,1877,2012,2118,2268,2414,2550,2752,2881,2999,3122,3255,3357,3462,3586,3714,3816,3928,4033,4178,4330,4439,4548,4626,4719,4814,4904,4990,5097,5177,5262,5359,5470,5563,5667,5755,5871,5972,6081,6203,6283,6393", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "197,342,464,612,738,832,944,1086,1205,1364,1448,1549,1650,1751,1872,2007,2113,2263,2409,2545,2747,2876,2994,3117,3250,3352,3457,3581,3709,3811,3923,4028,4173,4325,4434,4543,4621,4714,4809,4899,4985,5092,5172,5257,5354,5465,5558,5662,5750,5866,5967,6076,6198,6278,6388,6485"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4710,4855,4977,5125,5251,5345,5457,5599,5718,5877,5961,6062,6163,6264,6385,6520,6626,6776,6922,7058,7260,7389,7507,7630,7763,7865,7970,8094,8222,8324,8436,8541,8686,8838,8947,9056,9134,9227,9322,9412,9498,9605,9685,9770,9867,9978,10071,10175,10263,10379,10480,10589,10711,10791,10901", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "4705,4850,4972,5120,5246,5340,5452,5594,5713,5872,5956,6057,6158,6259,6380,6515,6621,6771,6917,7053,7255,7384,7502,7625,7758,7860,7965,8089,8217,8319,8431,8536,8681,8833,8942,9051,9129,9222,9317,9407,9493,9600,9680,9765,9862,9973,10066,10170,10258,10374,10475,10584,10706,10786,10896,10993"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\831f7f0f93fd70afcef46ca0120dc927\\transformed\\foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "128,129", "startColumns": "4,4", "startOffsets": "13616,13704", "endColumns": "87,94", "endOffsets": "13699,13794"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\10e14192bcf7bea6e0ef145c1ad40304\\transformed\\play-services-ads-23.5.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "203,245,291,354,421,495,603,666,825,937,1076,1126,1183,1315,1407,1455,1549,1585,1620,1681,1766,1806", "endColumns": "41,45,62,66,73,107,62,158,111,138,49,56,131,91,47,93,35,34,60,84,39,55", "endOffsets": "244,290,353,420,494,602,665,824,936,1075,1125,1182,1314,1406,1454,1548,1584,1619,1680,1765,1805,1861"}, "to": {"startLines": "96,97,98,101,102,103,105,106,107,108,109,110,111,112,116,117,118,119,120,121,122,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10998,11044,11094,11340,11411,11489,11672,11739,11902,12018,12161,12215,12276,12412,12742,12794,12892,12932,12971,13036,13125,13799", "endColumns": "45,49,66,70,77,111,66,162,115,142,53,60,135,95,51,97,39,38,64,88,43,59", "endOffsets": "11039,11089,11156,11406,11484,11596,11734,11897,12013,12156,12210,12271,12407,12503,12789,12887,12927,12966,13031,13120,13164,13854"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\fcd13fdc941282eaa2071fb31e6df1d3\\transformed\\ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1007,1075,1156,1241,1317,1396,1465", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1002,1070,1151,1236,1312,1391,1460,1582"}, "to": {"startLines": "11,12,32,33,34,38,39,99,100,104,113,114,115,123,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1063,1162,3772,3870,3976,4389,4469,11161,11253,11601,12508,12576,12657,13169,13346,13425,13494", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "1157,1245,3865,3971,4058,4464,4558,11248,11335,11667,12571,12652,12737,13240,13420,13489,13611"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\e995885063661f84453093b90e093640\\transformed\\browser-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3665,4063,4165,4284", "endColumns": "106,101,118,104", "endOffsets": "3767,4160,4279,4384"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\3793d766ef17a5c374cbb3773985b3be\\transformed\\play-services-base-18.5.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2303", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,62,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2302,2382"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1250,1356,1536,1666,1775,1946,2079,2200,2474,2669,2781,2966,3102,3262,3441,3514,3581", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,66,83", "endOffsets": "1351,1531,1661,1770,1941,2074,2195,2308,2664,2776,2961,3097,3257,3436,3509,3576,3660"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\17af91994e13f583c0b6091f8c6fb16f\\transformed\\core-1.15.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "4,5,6,7,8,9,10,124", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "340,438,540,639,741,845,949,13245", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "433,535,634,736,840,944,1058,13341"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\ce7063c7507d7ba5446267220fbed412\\transformed\\credentials-1.2.0-rc01\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,171", "endColumns": "115,118", "endOffsets": "166,285"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,221", "endColumns": "115,118", "endOffsets": "216,335"}}]}]}