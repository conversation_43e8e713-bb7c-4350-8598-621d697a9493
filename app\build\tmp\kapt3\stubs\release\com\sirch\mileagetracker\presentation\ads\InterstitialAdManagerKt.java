package com.sirch.mileagetracker.presentation.ads;

import android.app.Activity;
import android.content.Context;
import androidx.compose.runtime.*;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00004\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\u001al\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0014\b\u0002\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u00072\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0014\b\u0002\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\u001a\u001c\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u0011H\u0007\u00a8\u0006\u0012"}, d2 = {"InterstitialAdEffect", "", "adManager", "Lcom/sirch/mileagetracker/presentation/ads/InterstitialAdManager;", "onAdLoaded", "Lkotlin/Function0;", "onAdFailedToLoad", "Lkotlin/Function1;", "Lcom/google/android/gms/ads/LoadAdError;", "onAdShown", "onAdDismissed", "onAdFailedToShow", "Lcom/google/android/gms/ads/AdError;", "rememberInterstitialAdManager", "adUnitId", "", "showAds", "", "app_release"})
public final class InterstitialAdManagerKt {
    
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final com.sirch.mileagetracker.presentation.ads.InterstitialAdManager rememberInterstitialAdManager(@org.jetbrains.annotations.NotNull()
    java.lang.String adUnitId, boolean showAds) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void InterstitialAdEffect(@org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.presentation.ads.InterstitialAdManager adManager, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAdLoaded, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.google.android.gms.ads.LoadAdError, kotlin.Unit> onAdFailedToLoad, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAdShown, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAdDismissed, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.google.android.gms.ads.AdError, kotlin.Unit> onAdFailedToShow) {
    }
}