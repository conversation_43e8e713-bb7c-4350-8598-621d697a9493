package com.sirch.mileagetracker.data.billing;

import android.content.Context;
import com.sirch.mileagetracker.data.repository.AdsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BillingManager_Factory implements Factory<BillingManager> {
  private final Provider<Context> contextProvider;

  private final Provider<AdsRepository> adsRepositoryProvider;

  public BillingManager_Factory(Provider<Context> contextProvider,
      Provider<AdsRepository> adsRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.adsRepositoryProvider = adsRepositoryProvider;
  }

  @Override
  public BillingManager get() {
    return newInstance(contextProvider.get(), adsRepositoryProvider.get());
  }

  public static BillingManager_Factory create(Provider<Context> contextProvider,
      Provider<AdsRepository> adsRepositoryProvider) {
    return new BillingManager_Factory(contextProvider, adsRepositoryProvider);
  }

  public static BillingManager newInstance(Context context, AdsRepository adsRepository) {
    return new BillingManager(context, adsRepository);
  }
}
