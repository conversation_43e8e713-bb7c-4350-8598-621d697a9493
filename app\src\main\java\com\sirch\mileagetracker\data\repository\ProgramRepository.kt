package com.sirch.mileagetracker.data.repository

import com.sirch.mileagetracker.data.local.dao.ProgramDao
import com.sirch.mileagetracker.data.local.entities.Program
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ProgramRepository @Inject constructor(
    private val programDao: ProgramDao
) {

    fun getAllActivePrograms(): Flow<List<Program>> {
        return programDao.getAllActivePrograms()
    }

    suspend fun getProgramById(id: Long): Program? {
        return programDao.getProgramById(id)
    }

    fun getProgramByIdFlow(id: Long): Flow<Program?> {
        return programDao.getProgramByIdFlow(id)
    }

    suspend fun insertProgram(program: Program): Long {
        return programDao.insertProgram(program)
    }

    suspend fun insertPrograms(programs: List<Program>) {
        programDao.insertPrograms(programs)
    }

    suspend fun updateProgram(program: Program) {
        programDao.updateProgram(program)
    }

    suspend fun deleteProgram(program: Program) {
        programDao.deleteProgram(program)
    }

    suspend fun getProgramCount(): Int {
        return programDao.getProgramCount()
    }

    suspend fun seedDefaultPrograms() {
        val count = getProgramCount()
        if (count == 0) {
            val defaultPrograms = listOf(
                Program(
                    name = "LATAM Pass",
                    description = "Programa de fidelidade da LATAM Airlines"
                ),
                Program(
                    name = "Smiles",
                    description = "Programa de fidelidade da GOL Linhas Aéreas"
                ),
                Program(
                    name = "TudoAzul",
                    description = "Programa de fidelidade da Azul Linhas Aéreas"
                ),
                Program(
                    name = "Multiplus",
                    description = "Programa de fidelidade da LATAM (antigo TAM)"
                ),
                Program(
                    name = "TAP Miles&Go",
                    description = "Programa de fidelidade da TAP Air Portugal"
                )
            )
            insertPrograms(defaultPrograms)
        }
    }

    suspend fun seedTestData() {
        // Primeiro, seed os programas
        seedDefaultPrograms()

        // Depois, adicionar algumas compras de teste se não existirem
        // Isso será implementado quando tivermos o PurchaseRepository disponível
    }
}
