package com.sirch.mileagetracker.data.billing;

import android.app.Activity;
import android.content.Context;
import com.android.billingclient.api.*;
import com.sirch.mileagetracker.data.repository.AdsRepository;
import dagger.hilt.android.qualifiers.ApplicationContext;
import kotlinx.coroutines.Dispatchers;
import kotlinx.coroutines.flow.Flow;
import kotlinx.coroutines.flow.StateFlow;
import javax.inject.Inject;
import javax.inject.Singleton;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010!\n\u0002\b\u0007\b\u0007\u0018\u0000 <2\u00020\u00012\u00020\u0002:\u0003;<=B\u0019\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\u0016\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0082@\u00a2\u0006\u0002\u0010#J\u0006\u0010$\u001a\u00020 J\b\u0010%\u001a\u0004\u0018\u00010\rJ\u001c\u0010&\u001a\u00020 2\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\"0\fH\u0082@\u00a2\u0006\u0002\u0010(J\u000e\u0010)\u001a\u00020*H\u0086@\u00a2\u0006\u0002\u0010+J\u001e\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020/2\u0006\u00100\u001a\u000201H\u0086@\u00a2\u0006\u0002\u00102J\b\u00103\u001a\u00020 H\u0016J\u0010\u00104\u001a\u00020 2\u0006\u00105\u001a\u00020-H\u0016J \u00106\u001a\u00020 2\u0006\u00105\u001a\u00020-2\u000e\u0010\'\u001a\n\u0012\u0004\u0012\u00020\"\u0018\u000107H\u0016J\b\u00108\u001a\u00020 H\u0002J\b\u00109\u001a\u00020 H\u0002J\b\u0010:\u001a\u00020 H\u0002R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\n0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0016R\u0017\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00100\u001c\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001e\u00a8\u0006>"}, d2 = {"Lcom/sirch/mileagetracker/data/billing/BillingManager;", "Lcom/android/billingclient/api/PurchasesUpdatedListener;", "Lcom/android/billingclient/api/BillingClientStateListener;", "context", "Landroid/content/Context;", "adsRepository", "Lcom/sirch/mileagetracker/data/repository/AdsRepository;", "(Landroid/content/Context;Lcom/sirch/mileagetracker/data/repository/AdsRepository;)V", "_connectionState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState;", "_productDetails", "", "Lcom/android/billingclient/api/ProductDetails;", "_purchaseEvents", "Lkotlinx/coroutines/channels/Channel;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$PurchaseEvent;", "billingClient", "Lcom/android/billingclient/api/BillingClient;", "connectionState", "Lkotlinx/coroutines/flow/StateFlow;", "getConnectionState", "()Lkotlinx/coroutines/flow/StateFlow;", "coroutineScope", "Lkotlinx/coroutines/CoroutineScope;", "productDetails", "getProductDetails", "purchaseEvents", "Lkotlinx/coroutines/flow/Flow;", "getPurchaseEvents", "()Lkotlinx/coroutines/flow/Flow;", "acknowledgePurchase", "", "purchase", "Lcom/android/billingclient/api/Purchase;", "(Lcom/android/billingclient/api/Purchase;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "endConnection", "getRemoveAdsProductDetails", "handlePurchases", "purchases", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isRemoveAdsPurchased", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "launchBillingFlow", "Lcom/android/billingclient/api/BillingResult;", "activity", "Landroid/app/Activity;", "productId", "", "(Landroid/app/Activity;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "onBillingServiceDisconnected", "onBillingSetupFinished", "billingResult", "onPurchasesUpdated", "", "queryProductDetails", "queryPurchases", "startConnection", "BillingConnectionState", "Companion", "PurchaseEvent", "app_release"})
public final class BillingManager implements com.android.billingclient.api.PurchasesUpdatedListener, com.android.billingclient.api.BillingClientStateListener {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.sirch.mileagetracker.data.repository.AdsRepository adsRepository = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String REMOVE_ADS_PRODUCT_ID = "remove_ads";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String INAPP = "inapp";
    @org.jetbrains.annotations.NotNull()
    private final com.android.billingclient.api.BillingClient billingClient = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState> _connectionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState> connectionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.channels.Channel<com.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent> _purchaseEvents = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<com.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent> purchaseEvents = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.android.billingclient.api.ProductDetails>> _productDetails = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.android.billingclient.api.ProductDetails>> productDetails = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope coroutineScope = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.sirch.mileagetracker.data.billing.BillingManager.Companion Companion = null;
    
    @javax.inject.Inject()
    public BillingManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.sirch.mileagetracker.data.repository.AdsRepository adsRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState> getConnectionState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent> getPurchaseEvents() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.android.billingclient.api.ProductDetails>> getProductDetails() {
        return null;
    }
    
    private final void startConnection() {
    }
    
    @java.lang.Override()
    public void onBillingSetupFinished(@org.jetbrains.annotations.NotNull()
    com.android.billingclient.api.BillingResult billingResult) {
    }
    
    @java.lang.Override()
    public void onBillingServiceDisconnected() {
    }
    
    private final void queryProductDetails() {
    }
    
    private final void queryPurchases() {
    }
    
    @java.lang.Override()
    public void onPurchasesUpdated(@org.jetbrains.annotations.NotNull()
    com.android.billingclient.api.BillingResult billingResult, @org.jetbrains.annotations.Nullable()
    java.util.List<com.android.billingclient.api.Purchase> purchases) {
    }
    
    private final java.lang.Object handlePurchases(java.util.List<? extends com.android.billingclient.api.Purchase> purchases, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object acknowledgePurchase(com.android.billingclient.api.Purchase purchase, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object launchBillingFlow(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity, @org.jetbrains.annotations.NotNull()
    java.lang.String productId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.android.billingclient.api.BillingResult> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.android.billingclient.api.ProductDetails getRemoveAdsProductDetails() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object isRemoveAdsPurchased(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    public final void endConnection() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState;", "", "()V", "CONNECTED", "CONNECTING", "DISCONNECTED", "ERROR", "Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState$CONNECTED;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState$CONNECTING;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState$DISCONNECTED;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState$ERROR;", "app_release"})
    public static abstract class BillingConnectionState {
        
        private BillingConnectionState() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState$CONNECTED;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState;", "()V", "app_release"})
        public static final class CONNECTED extends com.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState {
            @org.jetbrains.annotations.NotNull()
            public static final com.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState.CONNECTED INSTANCE = null;
            
            private CONNECTED() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState$CONNECTING;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState;", "()V", "app_release"})
        public static final class CONNECTING extends com.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState {
            @org.jetbrains.annotations.NotNull()
            public static final com.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState.CONNECTING INSTANCE = null;
            
            private CONNECTING() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState$DISCONNECTED;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState;", "()V", "app_release"})
        public static final class DISCONNECTED extends com.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState {
            @org.jetbrains.annotations.NotNull()
            public static final com.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState.DISCONNECTED INSTANCE = null;
            
            private DISCONNECTED() {
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState$ERROR;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$BillingConnectionState;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_release"})
        public static final class ERROR extends com.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String message = null;
            
            public ERROR(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.sirch.mileagetracker.data.billing.BillingManager.BillingConnectionState.ERROR copy(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/sirch/mileagetracker/data/billing/BillingManager$Companion;", "", "()V", "INAPP", "", "REMOVE_ADS_PRODUCT_ID", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u00002\u00020\u0001:\u0003\u0003\u0004\u0005B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0003\u0006\u0007\b\u00a8\u0006\t"}, d2 = {"Lcom/sirch/mileagetracker/data/billing/BillingManager$PurchaseEvent;", "", "()V", "PurchaseCancelled", "PurchaseCompleted", "PurchaseFailed", "Lcom/sirch/mileagetracker/data/billing/BillingManager$PurchaseEvent$PurchaseCancelled;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$PurchaseEvent$PurchaseCompleted;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$PurchaseEvent$PurchaseFailed;", "app_release"})
    public static abstract class PurchaseEvent {
        
        private PurchaseEvent() {
            super();
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/sirch/mileagetracker/data/billing/BillingManager$PurchaseEvent$PurchaseCancelled;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$PurchaseEvent;", "message", "", "(Ljava/lang/String;)V", "getMessage", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_release"})
        public static final class PurchaseCancelled extends com.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String message = null;
            
            public PurchaseCancelled(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getMessage() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent.PurchaseCancelled copy(@org.jetbrains.annotations.NotNull()
            java.lang.String message) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/sirch/mileagetracker/data/billing/BillingManager$PurchaseEvent$PurchaseCompleted;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$PurchaseEvent;", "purchase", "Lcom/android/billingclient/api/Purchase;", "(Lcom/android/billingclient/api/Purchase;)V", "getPurchase", "()Lcom/android/billingclient/api/Purchase;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_release"})
        public static final class PurchaseCompleted extends com.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent {
            @org.jetbrains.annotations.NotNull()
            private final com.android.billingclient.api.Purchase purchase = null;
            
            public PurchaseCompleted(@org.jetbrains.annotations.NotNull()
            com.android.billingclient.api.Purchase purchase) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.android.billingclient.api.Purchase getPurchase() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.android.billingclient.api.Purchase component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent.PurchaseCompleted copy(@org.jetbrains.annotations.NotNull()
            com.android.billingclient.api.Purchase purchase) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/sirch/mileagetracker/data/billing/BillingManager$PurchaseEvent$PurchaseFailed;", "Lcom/sirch/mileagetracker/data/billing/BillingManager$PurchaseEvent;", "error", "", "(Ljava/lang/String;)V", "getError", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_release"})
        public static final class PurchaseFailed extends com.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent {
            @org.jetbrains.annotations.NotNull()
            private final java.lang.String error = null;
            
            public PurchaseFailed(@org.jetbrains.annotations.NotNull()
            java.lang.String error) {
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String getError() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final java.lang.String component1() {
                return null;
            }
            
            @org.jetbrains.annotations.NotNull()
            public final com.sirch.mileagetracker.data.billing.BillingManager.PurchaseEvent.PurchaseFailed copy(@org.jetbrains.annotations.NotNull()
            java.lang.String error) {
                return null;
            }
            
            @java.lang.Override()
            public boolean equals(@org.jetbrains.annotations.Nullable()
            java.lang.Object other) {
                return false;
            }
            
            @java.lang.Override()
            public int hashCode() {
                return 0;
            }
            
            @java.lang.Override()
            @org.jetbrains.annotations.NotNull()
            public java.lang.String toString() {
                return null;
            }
        }
    }
}