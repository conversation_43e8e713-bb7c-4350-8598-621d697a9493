# Mileage Tracker - ProGuard Rules for Production
# Otimizado para performance e segurança

# Preserve line numbers for crash reports
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# Keep generic signatures for reflection
-keepattributes Signature
-keepattributes *Annotation*

# Kotlin specific
-keep class kotlin.** { *; }
-keep class kotlinx.** { *; }
-dontwarn kotlin.**
-dontwarn kotlinx.**

# Room Database
-keep class com.sirch.mileagetracker.data.local.entities.** { *; }
-keep class com.sirch.mileagetracker.data.local.dao.** { *; }
-keep class androidx.room.** { *; }
-dontwarn androidx.room.**

# Firebase & Google Play Services
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Hilt Dependency Injection
-keep class dagger.hilt.** { *; }
-keep class * extends dagger.hilt.android.HiltAndroidApp
-keep @dagger.hilt.android.AndroidEntryPoint class * { *; }
-keep @dagger.hilt.android.HiltAndroidApp class * { *; }
-dontwarn dagger.hilt.**

# Compose
-keep class androidx.compose.** { *; }
-dontwarn androidx.compose.**

# Coroutines
-keepclassmembers class kotlinx.coroutines.** { *; }
-dontwarn kotlinx.coroutines.**

# Billing
-keep class com.android.billingclient.** { *; }
-dontwarn com.android.billingclient.**

# AdMob
-keep class com.google.android.gms.ads.** { *; }
-dontwarn com.google.android.gms.ads.**

# Paging
-keep class androidx.paging.** { *; }
-dontwarn androidx.paging.**

# Navigation
-keep class androidx.navigation.** { *; }
-dontwarn androidx.navigation.**
