package com.sirch.mileagetracker.data.local.dao

import androidx.room.*
import com.sirch.mileagetracker.data.local.entities.EfficiencyScore
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

/**
 * Data Access Object for EfficiencyScore entity.
 * Provides optimized queries for efficiency score operations with proper indexing.
 */
@Dao
interface EfficiencyScoreDao {
    
    /**
     * Get all efficiency scores for a specific user
     */
    @Query("SELECT * FROM efficiency_scores WHERE userId = :userId ORDER BY lastUpdated DESC")
    fun getEfficiencyScoresByUser(userId: String): Flow<List<EfficiencyScore>>
    
    /**
     * Get efficiency score for a specific user and program
     */
    @Query("SELECT * FROM efficiency_scores WHERE userId = :userId AND programId = :programId")
    suspend fun getEfficiencyScore(userId: String, programId: Long): EfficiencyScore?
    
    /**
     * Get efficiency score for a specific user and program as Flow
     */
    @Query("SELECT * FROM efficiency_scores WHERE userId = :userId AND programId = :programId")
    fun getEfficiencyScoreFlow(userId: String, programId: Long): Flow<EfficiencyScore?>
    
    /**
     * Get all efficiency scores for a specific program (for aggregate calculations)
     */
    @Query("SELECT * FROM efficiency_scores WHERE programId = :programId AND isDataSharingEnabled = 1")
    suspend fun getEfficiencyScoresByProgram(programId: Long): List<EfficiencyScore>
    
    /**
     * Get efficiency scores that need updating (older than specified time)
     */
    @Query("SELECT * FROM efficiency_scores WHERE lastUpdated < :cutoffTime")
    suspend fun getOutdatedEfficiencyScores(cutoffTime: LocalDateTime): List<EfficiencyScore>
    
    /**
     * Get top efficiency scores for a program (for leaderboard)
     */
    @Query("""
        SELECT * FROM efficiency_scores 
        WHERE programId = :programId AND isDataSharingEnabled = 1 
        ORDER BY score DESC 
        LIMIT :limit
    """)
    suspend fun getTopEfficiencyScores(programId: Long, limit: Int = 10): List<EfficiencyScore>
    
    /**
     * Get user's ranking for a specific program
     */
    @Query("""
        SELECT COUNT(*) + 1 as ranking
        FROM efficiency_scores 
        WHERE programId = :programId 
        AND isDataSharingEnabled = 1 
        AND score > (
            SELECT score FROM efficiency_scores 
            WHERE userId = :userId AND programId = :programId
        )
    """)
    suspend fun getUserRanking(userId: String, programId: Long): Int?
    
    /**
     * Get average efficiency score for a program
     */
    @Query("""
        SELECT AVG(score) 
        FROM efficiency_scores 
        WHERE programId = :programId AND isDataSharingEnabled = 1
    """)
    suspend fun getAverageEfficiencyScore(programId: Long): Double?
    
    /**
     * Get efficiency score statistics for a program
     */
    @Query("""
        SELECT 
            COUNT(*) as totalUsers,
            AVG(score) as averageScore,
            MIN(score) as minScore,
            MAX(score) as maxScore,
            AVG(personalAvgCostPerMile) as avgPersonalCost,
            AVG(aggregateAvgCostPerMile) as avgAggregateCost
        FROM efficiency_scores 
        WHERE programId = :programId AND isDataSharingEnabled = 1
    """)
    suspend fun getEfficiencyScoreStats(programId: Long): EfficiencyScoreStats?
    
    /**
     * Insert or update efficiency score
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEfficiencyScore(efficiencyScore: EfficiencyScore): Long
    
    /**
     * Insert multiple efficiency scores
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEfficiencyScores(efficiencyScores: List<EfficiencyScore>)
    
    /**
     * Update efficiency score
     */
    @Update
    suspend fun updateEfficiencyScore(efficiencyScore: EfficiencyScore)
    
    /**
     * Delete efficiency score
     */
    @Delete
    suspend fun deleteEfficiencyScore(efficiencyScore: EfficiencyScore)
    
    /**
     * Delete all efficiency scores for a user
     */
    @Query("DELETE FROM efficiency_scores WHERE userId = :userId")
    suspend fun deleteEfficiencyScoresByUser(userId: String)
    
    /**
     * Delete all efficiency scores for a program
     */
    @Query("DELETE FROM efficiency_scores WHERE programId = :programId")
    suspend fun deleteEfficiencyScoresByProgram(programId: Long)
    
    /**
     * Update data sharing preference for a user
     */
    @Query("UPDATE efficiency_scores SET isDataSharingEnabled = :enabled WHERE userId = :userId")
    suspend fun updateDataSharingPreference(userId: String, enabled: Boolean)
    
    /**
     * Get count of users with data sharing enabled for a program
     */
    @Query("""
        SELECT COUNT(*) 
        FROM efficiency_scores 
        WHERE programId = :programId AND isDataSharingEnabled = 1
    """)
    suspend fun getDataSharingUserCount(programId: Long): Int
    
    /**
     * Clean up old efficiency scores (for maintenance)
     */
    @Query("DELETE FROM efficiency_scores WHERE lastUpdated < :cutoffTime")
    suspend fun cleanupOldEfficiencyScores(cutoffTime: LocalDateTime): Int
}

/**
 * Data class for efficiency score statistics
 */
data class EfficiencyScoreStats(
    val totalUsers: Int,
    val averageScore: Double,
    val minScore: Double,
    val maxScore: Double,
    val avgPersonalCost: Double,
    val avgAggregateCost: Double
)
