{"logs": [{"outputFile": "com.sirch.mileagetracker.app-mergeReleaseResources-3:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\fcd13fdc941282eaa2071fb31e6df1d3\\transformed\\ui-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,990,1061,1141,1226,1299,1378,1448", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,985,1056,1136,1221,1294,1373,1443,1561"}, "to": {"startLines": "11,12,32,33,34,38,39,99,100,104,113,114,115,123,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1062,1159,3532,3629,3730,4127,4204,10626,10718,11060,11884,11955,12035,12535,12709,12788,12858", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "1154,1241,3624,3725,3811,4199,4290,10713,10798,11127,11950,12030,12115,12603,12783,12853,12971"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\17af91994e13f583c0b6091f8c6fb16f\\transformed\\core-1.15.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "4,5,6,7,8,9,10,124", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "336,434,536,633,737,841,946,12608", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "429,531,628,732,836,941,1057,12704"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\51f776a662999a2c4066cf59dc47f5c4\\transformed\\play-services-basement-18.4.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "125", "endOffsets": "327"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2243", "endColumns": "129", "endOffsets": "2368"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\4f87b4fe7e5f13faf091c8d26d70e189\\transformed\\material3-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,289,417,534,633,727,838,974,1094,1236,1321,1421,1516,1614,1730,1855,1960,2101,2241,2374,2554,2679,2799,2924,3046,3142,3240,3358,3488,3588,3690,3799,3941,4090,4199,4302,4379,4478,4576,4665,4751,4858,4938,5021,5118,5221,5314,5412,5499,5607,5704,5806,5939,6019,6128", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "167,284,412,529,628,722,833,969,1089,1231,1316,1416,1511,1609,1725,1850,1955,2096,2236,2369,2549,2674,2794,2919,3041,3137,3235,3353,3483,3583,3685,3794,3936,4085,4194,4297,4374,4473,4571,4660,4746,4853,4933,5016,5113,5216,5309,5407,5494,5602,5699,5801,5934,6014,6123,6222"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4295,4412,4529,4657,4774,4873,4967,5078,5214,5334,5476,5561,5661,5756,5854,5970,6095,6200,6341,6481,6614,6794,6919,7039,7164,7286,7382,7480,7598,7728,7828,7930,8039,8181,8330,8439,8542,8619,8718,8816,8905,8991,9098,9178,9261,9358,9461,9554,9652,9739,9847,9944,10046,10179,10259,10368", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "4407,4524,4652,4769,4868,4962,5073,5209,5329,5471,5556,5656,5751,5849,5965,6090,6195,6336,6476,6609,6789,6914,7034,7159,7281,7377,7475,7593,7723,7823,7925,8034,8176,8325,8434,8537,8614,8713,8811,8900,8986,9093,9173,9256,9353,9456,9549,9647,9734,9842,9939,10041,10174,10254,10363,10462"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\e995885063661f84453093b90e093640\\transformed\\browser-1.8.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3421,3816,3916,4029", "endColumns": "110,99,112,97", "endOffsets": "3527,3911,4024,4122"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\10e14192bcf7bea6e0ef145c1ad40304\\transformed\\play-services-ads-23.5.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "206,250,297,353,418,486,598,661,790,898,1015,1070,1127,1232,1318,1359,1449,1485,1518,1577,1665,1705", "endColumns": "43,46,55,64,67,111,62,128,107,116,54,56,104,85,40,89,35,32,58,87,39,55", "endOffsets": "249,296,352,417,485,597,660,789,897,1014,1069,1126,1231,1317,1358,1448,1484,1517,1576,1664,1704,1760"}, "to": {"startLines": "96,97,98,101,102,103,105,106,107,108,109,110,111,112,116,117,118,119,120,121,122,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10467,10515,10566,10803,10872,10944,11132,11199,11332,11444,11565,11624,11685,11794,12120,12165,12259,12299,12336,12399,12491,13158", "endColumns": "47,50,59,68,71,115,66,132,111,120,58,60,108,89,44,93,39,36,62,91,43,59", "endOffsets": "10510,10561,10621,10867,10939,11055,11194,11327,11439,11560,11619,11680,11789,11879,12160,12254,12294,12331,12394,12486,12530,13213"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\ce7063c7507d7ba5446267220fbed412\\transformed\\credentials-1.2.0-rc01\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,119", "endOffsets": "161,281"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,216", "endColumns": "110,119", "endOffsets": "211,331"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\3793d766ef17a5c374cbb3773985b3be\\transformed\\play-services-base-18.5.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "200,303,457,580,686,836,959,1067,1165,1310,1413,1569,1692,1837,1976,2040,2101", "endColumns": "102,153,122,105,149,122,107,97,144,102,155,122,144,138,63,60,75", "endOffsets": "302,456,579,685,835,958,1066,1164,1309,1412,1568,1691,1836,1975,2039,2100,2176"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1246,1353,1511,1638,1748,1902,2029,2141,2373,2522,2629,2789,2916,3065,3208,3276,3341", "endColumns": "106,157,126,109,153,126,111,101,148,106,159,126,148,142,67,64,79", "endOffsets": "1348,1506,1633,1743,1897,2024,2136,2238,2517,2624,2784,2911,3060,3203,3271,3336,3416"}}, {"source": "C:\\projetos\\MileageTracker\\caches\\8.14\\transforms\\831f7f0f93fd70afcef46ca0120dc927\\transformed\\foundation-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "128,129", "startColumns": "4,4", "startOffsets": "12976,13066", "endColumns": "89,91", "endOffsets": "13061,13153"}}]}]}